#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Clean Up Redundant Phase Transition Logic

This script implements task 6.2 from the unified lesson flow refactor spec:
- Remove duplicate or conflicting phase transition code
- Consolidate all phase handling into the main state machine
- Eliminate any code paths that bypass the unified pathway

The unified lesson pathway is defined as:
teaching_start/teaching → quiz_initiate → quiz_questions → quiz_results → final_report_inprogress → complete
"""

import os
import re
import sys
import logging
import time
from typing import List, Dict, Any, Tuple, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Constants
MAIN_PY_PATH = "backend/cloud_function/lesson_manager/main.py"
PHASE_TRANSITION_INTEGRITY_PATH = "backend/cloud_function/lesson_manager/phase_transition_integrity.py"
TEACHING_RULES_PATH = "backend/cloud_function/lesson_manager/teaching_rules.py"

def read_file(file_path: str) -> str:
    """Read a file and return its contents."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return ""

def write_file(file_path: str, content: str) -> bool:
    """Write content to a file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        logger.error(f"Error writing to file {file_path}: {e}")
        return False

def remove_legacy_phase_validation(content: str) -> str:
    """Remove legacy phase validation functions."""
    
    # List of legacy validation functions to remove
    legacy_functions = [
        r'def validate_diagnostic_phase_sequence.*?return True, proposed_phase, f"Unknown diagnostic phase.*?allowing transition"',
        r'def is_forward_transition_validated.*?return True',
        r'def determine_diagnostic_phase.*?return current_phase',
        r'def get_state_transition_rules.*?return \(.*?\)'
    ]
    
    updated_content = content
    
    for pattern in legacy_functions:
        match = re.search(pattern, updated_content, re.DOTALL)
        if match:
            start, end = match.span()
            # Replace with a comment indicating removal
            updated_content = updated_content[:start] + f"# REMOVED: Legacy phase validation function\n" + updated_content[end:]
            logger.info(f"Removed legacy phase validation function: {pattern[:50]}...")
        else:
            logger.warning(f"Could not find legacy function: {pattern[:50]}...")
    
    return updated_content

def consolidate_phase_handling(content: str) -> str:
    """Consolidate all phase handling into the main state machine."""
    
    # Define the consolidated phase handling code
    consolidated_code = """
def handle_phase_transition(current_phase, next_phase, context, request_id=None):
    '''
    Consolidated phase transition handler for the unified pathway.
    
    Args:
        current_phase: Current lesson phase
        next_phase: Requested next phase
        context: Context data for the transition
        request_id: Request ID for logging
        
    Returns:
        Tuple of (valid_phase, state_updates)
    '''
    request_id = request_id or f"transition-{int(time.time())}"
    logger.info(f"[{request_id}] 🔄 PHASE TRANSITION: {current_phase} → {next_phase}")
    
    # Use the phase transition integrity manager to validate the transition
    validation_result = phase_transition_manager.validate_phase_transition(
        current_phase, next_phase, context, request_id
    )
    
    if validation_result.result == PhaseTransitionResult.VALID:
        logger.info(f"[{request_id}] ✅ VALID TRANSITION: {current_phase} → {next_phase}")
        return next_phase, {}
    
    elif validation_result.result == PhaseTransitionResult.CORRECTED:
        corrected_phase = validation_result.corrected_phase
        logger.warning(f"[{request_id}] 🔧 CORRECTED TRANSITION: {next_phase} → {corrected_phase}")
        return corrected_phase, validation_result.preserved_data
    
    elif validation_result.result == PhaseTransitionResult.RECOVERED:
        logger.warning(f"[{request_id}] 🔧 RECOVERED TRANSITION: {current_phase} → {next_phase}")
        return next_phase, validation_result.preserved_data
    
    else:  # INVALID or BLOCKED
        # Determine the next valid phase in the unified pathway
        valid_next_phase = LessonPhase.get_next_phase(current_phase, context)
        logger.error(f"[{request_id}] ❌ INVALID TRANSITION: {current_phase} → {next_phase}, using {valid_next_phase}")
        return valid_next_phase, {'unified_pathway_enforced': True}
"""
    
    # Find a good insertion point after the imports
    pattern = r'# --- Phase Transition Validation ---'
    match = re.search(pattern, content)
    
    if match:
        start = match.end()
        # Insert the consolidated code after the pattern
        updated_content = content[:start] + "\n" + consolidated_code + content[start:]
        logger.info("Inserted consolidated phase handling function")
        return updated_content
    else:
        logger.warning("Could not find insertion point for consolidated phase handling")
        return content

def update_phase_transition_calls(content: str) -> str:
    """Update all phase transition calls to use the consolidated handler."""
    
    # Find all places where phase transitions are validated
    patterns = [
        r'if not log_state_transition\(current_phase, new_phase, "AI processing complete"\):.*?new_phase = valid_next_phase',
        r'if not self\._is_valid_phase_transition\(current_state\.get\(\'current_phase\'\), new_phase\):.*?return False'
    ]
    
    updated_content = content
    
    for pattern in patterns:
        match = re.search(pattern, updated_content, re.DOTALL)
        if match:
            start, end = match.span()
            # Replace with a call to the consolidated handler
            replacement = """
            # Use consolidated phase transition handler
            valid_phase, state_updates = handle_phase_transition(
                current_phase, new_phase, state_updates_from_ai, request_id
            )
            new_phase = valid_phase
            state_updates_from_ai.update(state_updates)
            """
            updated_content = updated_content[:start] + replacement + updated_content[end:]
            logger.info(f"Updated phase transition call: {pattern[:50]}...")
        else:
            logger.warning(f"Could not find phase transition call: {pattern[:50]}...")
    
    return updated_content

def remove_backward_transition_checks(content: str) -> str:
    """Remove redundant backward transition checks."""
    
    # Find all backward transition checks
    patterns = [
        r'# Check for backward progression with more specific rules.*?return \{.*?\'is_backward\': True,.*?\}',
        r'# Check specific completion-based rules.*?return \{.*?\'is_backward\': True,.*?\}'
    ]
    
    updated_content = content
    
    for pattern in patterns:
        match = re.search(pattern, updated_content, re.DOTALL)
        if match:
            start, end = match.span()
            # Replace with a comment
            updated_content = updated_content[:start] + "# REMOVED: Redundant backward transition check - handled by LessonPhase.validate_transition\n" + updated_content[end:]
            logger.info(f"Removed backward transition check: {pattern[:50]}...")
        else:
            logger.warning(f"Could not find backward transition check: {pattern[:50]}...")
    
    return updated_content

def update_phase_transition_integrity(content: str) -> str:
    """Update the phase transition integrity module to use the unified pathway."""
    
    # Define the updated _is_valid_basic_transition method
    updated_method = """
    def _is_valid_basic_transition(self, from_phase: str, to_phase: str) -> bool:
        '''
        Check if transition is in valid transitions map based on the unified pathway.
        
        Args:
            from_phase: Current phase
            to_phase: Proposed next phase
            
        Returns:
            bool: True if transition is valid, False otherwise
        '''
        if not from_phase or not to_phase:
            return False
        
        # CRITICAL: Allow same-phase transitions (staying in the same phase)
        if from_phase == to_phase:
            return True
            
        # Check explicit transitions from the unified pathway
        valid_next_phases = self.VALID_TRANSITIONS.get(from_phase, [])
        if to_phase in valid_next_phases:
            return True
            
        return False
    """
    
    # Find the current implementation
    pattern = r'def _is_valid_basic_transition\(self, from_phase: str, to_phase: str\) -> bool:.*?return False'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        start, end = match.span()
        # Replace with the updated method
        updated_content = content[:start] + updated_method + content[end:]
        logger.info("Updated _is_valid_basic_transition method")
        return updated_content
    else:
        logger.warning("Could not find _is_valid_basic_transition method")
        return content

def main():
    """Main function to clean up redundant phase transition logic."""
    logger.info("Starting cleanup of redundant phase transition logic")
    
    # Read the main.py file
    main_py_content = read_file(MAIN_PY_PATH)
    if not main_py_content:
        logger.error(f"Could not read {MAIN_PY_PATH}")
        return
    
    # Remove legacy phase validation functions
    logger.info("Removing legacy phase validation functions")
    main_py_content = remove_legacy_phase_validation(main_py_content)
    
    # Consolidate phase handling
    logger.info("Consolidating phase handling")
    main_py_content = consolidate_phase_handling(main_py_content)
    
    # Update phase transition calls
    logger.info("Updating phase transition calls")
    main_py_content = update_phase_transition_calls(main_py_content)
    
    # Write the updated main.py file
    logger.info(f"Writing updated {MAIN_PY_PATH}")
    if not write_file(MAIN_PY_PATH, main_py_content):
        logger.error(f"Could not write to {MAIN_PY_PATH}")
        return
    
    # Read the phase_transition_integrity.py file
    phase_transition_integrity_content = read_file(PHASE_TRANSITION_INTEGRITY_PATH)
    if not phase_transition_integrity_content:
        logger.error(f"Could not read {PHASE_TRANSITION_INTEGRITY_PATH}")
        return
    
    # Remove backward transition checks
    logger.info("Removing redundant backward transition checks")
    phase_transition_integrity_content = remove_backward_transition_checks(phase_transition_integrity_content)
    
    # Update the phase transition integrity module
    logger.info("Updating phase transition integrity module")
    phase_transition_integrity_content = update_phase_transition_integrity(phase_transition_integrity_content)
    
    # Write the updated phase_transition_integrity.py file
    logger.info(f"Writing updated {PHASE_TRANSITION_INTEGRITY_PATH}")
    if not write_file(PHASE_TRANSITION_INTEGRITY_PATH, phase_transition_integrity_content):
        logger.error(f"Could not write to {PHASE_TRANSITION_INTEGRITY_PATH}")
        return
    
    logger.info("Successfully cleaned up redundant phase transition logic")

if __name__ == "__main__":
    main()