{"test_metadata": {"test_name": "Comprehensive End-to-End Lesson System Test", "student_id": "andrea_ugono_33305", "student_collection": "testing", "session_id": "e2e_test_1753213594", "started_at": "2025-07-22T20:46:34.350760", "completed_at": "2025-07-22T20:48:42.766037", "test_duration_minutes": 2.1402546166666667}, "success_criteria": {}, "performance_metrics": {"total_requests": 14, "avg_response_time": 8.007654462541852, "max_response_time": 16.20378065109253, "min_response_time": 3.5698204040527344, "requests_under_2s": 0, "performance_threshold_met": false}, "ai_quality_analysis": {"total_assessments": 14, "avg_ai_quality": 56.339285714285715, "max_ai_quality": 72.25, "min_ai_quality": 43.5, "scores_above_70": 1, "quality_threshold_met": false}, "phase_coverage_analysis": {"expected_phases": ["diagnostic", "teaching_start", "teaching", "quiz_initiate", "quiz_questions", "quiz_results", "conclusion_summary", "final_assessment_pending", "completed"], "phases_completed": ["diagnostic", "teaching_start", "teaching", "quiz_initiate", "quiz_questions", "quiz_results", "conclusion_summary", "final_assessment_pending", "completed"], "phases_tracked": ["smart_diagnostic_q1", "smart_diagnostic_start", "smart_diagnostic_q2", "smart_diagnostic_q3", "smart_diagnostic_q4", "smart_diagnostic_q5", "teaching", "teaching_start", "quiz_initiate", "quiz_questions"], "coverage_percentage": 100.0, "missing_phases": [], "coverage_threshold_met": true}, "diagnostic_accuracy_validation": {"questions_asked": 5, "level_adjustments": [], "scoring_accuracy": false, "final_level": 1}, "state_update_compliance": {"total_blocks_found": 0, "blocks_found_in": [], "compliance_rate": 0.0}, "bug_analysis": {"total_bugs": 22, "bugs_by_severity": {"high": 13, "medium": 9}, "bugs_by_type": {"ai_instruction_compliance": 5, "performance": 9, "ai_quality": 8}, "critical_bugs": [], "high_priority_bugs": [{"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 1"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 2"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 3"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 4"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 5"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 67.5% below 70.0% threshold in teaching_start"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 47.8% below 70.0% threshold in teaching"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 49.2% below 70.0% threshold in quiz_initiate"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 43.5% below 70.0% threshold in quiz_questions"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 43.5% below 70.0% threshold in quiz_results"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 53.5% below 70.0% threshold in conclusion_summary"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 49.2% below 70.0% threshold in final_assessment_pending"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 47.2% below 70.0% threshold in completed"}], "all_bugs": [{"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 1"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 2"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 3"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 4"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 5"}, {"type": "performance", "severity": "medium", "description": "Response time 10.11s exceeds 2.0s threshold"}, {"type": "performance", "severity": "medium", "description": "Response time 9.90s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 67.5% below 70.0% threshold in teaching_start"}, {"type": "performance", "severity": "medium", "description": "Response time 16.20s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 47.8% below 70.0% threshold in teaching"}, {"type": "performance", "severity": "medium", "description": "Response time 5.60s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 49.2% below 70.0% threshold in quiz_initiate"}, {"type": "performance", "severity": "medium", "description": "Response time 4.66s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 43.5% below 70.0% threshold in quiz_questions"}, {"type": "performance", "severity": "medium", "description": "Response time 3.57s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 43.5% below 70.0% threshold in quiz_results"}, {"type": "performance", "severity": "medium", "description": "Response time 4.14s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 53.5% below 70.0% threshold in conclusion_summary"}, {"type": "performance", "severity": "medium", "description": "Response time 3.75s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 49.2% below 70.0% threshold in final_assessment_pending"}, {"type": "performance", "severity": "medium", "description": "Response time 4.07s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 47.2% below 70.0% threshold in completed"}]}, "errors_encountered": [], "raw_test_data": {"started_at": "2025-07-22T20:46:34.350760", "firebase_auth": true, "phase_coverage": 100.0, "phases_completed": ["diagnostic", "teaching_start", "teaching", "quiz_initiate", "quiz_questions", "quiz_results", "conclusion_summary", "final_assessment_pending", "completed"], "phase_transitions": ["smart_diagnostic_q1", "smart_diagnostic_start", "smart_diagnostic_q2", "smart_diagnostic_q3", "smart_diagnostic_q4", "smart_diagnostic_q5", "teaching", "teaching_start", "quiz_initiate", "quiz_questions"], "ai_quality_scores": [59.75, 61.0, 62.75000000000001, 65.75, 65.75, 72.25, 67.5, 47.75, 49.25, 43.5, 43.5, 53.5, 49.25, 47.25], "response_times": [14.273049354553223, 9.669467210769653, 8.849912166595459, 8.6544668674469, 8.655786991119385, 10.112378358840942, 9.90036654472351, 16.20378065109253, 5.600370168685913, 4.660635471343994, 3.5698204040527344, 4.144185781478882, 3.745056629180908, 4.067885875701904], "diagnostic_completion": false, "diagnostic_scores": [], "state_update_blocks": [], "session_persistence": false, "errors": [], "bugs_found": [{"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 1"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 2"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 3"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 4"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 5"}, {"type": "performance", "severity": "medium", "description": "Response time 10.11s exceeds 2.0s threshold"}, {"type": "performance", "severity": "medium", "description": "Response time 9.90s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 67.5% below 70.0% threshold in teaching_start"}, {"type": "performance", "severity": "medium", "description": "Response time 16.20s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 47.8% below 70.0% threshold in teaching"}, {"type": "performance", "severity": "medium", "description": "Response time 5.60s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 49.2% below 70.0% threshold in quiz_initiate"}, {"type": "performance", "severity": "medium", "description": "Response time 4.66s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 43.5% below 70.0% threshold in quiz_questions"}, {"type": "performance", "severity": "medium", "description": "Response time 3.57s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 43.5% below 70.0% threshold in quiz_results"}, {"type": "performance", "severity": "medium", "description": "Response time 4.14s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 53.5% below 70.0% threshold in conclusion_summary"}, {"type": "performance", "severity": "medium", "description": "Response time 3.75s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 49.2% below 70.0% threshold in final_assessment_pending"}, {"type": "performance", "severity": "medium", "description": "Response time 4.07s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 47.2% below 70.0% threshold in completed"}], "success_criteria_met": {"phase_coverage_100": true, "diagnostic_completion": false, "avg_response_time_under_2s": false, "avg_ai_quality_over_70": false, "no_critical_bugs": true}, "performance_metrics": {}, "ai_quality_analysis": {}, "diagnostic_accuracy": {"questions_asked": 5, "level_adjustments": [], "scoring_accuracy": false, "final_level": 1}, "completed_at": "2025-07-22T20:48:42.766037"}, "recommendations": [{"priority": "HIGH", "category": "Performance", "issue": "Average response time 8.01s exceeds 2.0s threshold", "recommendation": "Optimize API response times through caching, database indexing, or code optimization"}, {"priority": "HIGH", "category": "AI Quality", "issue": "Average AI quality 56.3% below 70% threshold", "recommendation": "Improve AI instructor prompts, add personalization, and enhance educational effectiveness"}, {"priority": "MEDIUM", "category": "AI Compliance", "issue": "Missing mandatory AI state update blocks detected", "recommendation": "Ensure AI instructor generates proper state update blocks in required format"}]}