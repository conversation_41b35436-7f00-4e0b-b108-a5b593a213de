"""
Data Persistence Module for Lesson Manager

This module implements structured data generation logic for the unified lesson pathway.
It provides functions to generate student_summary, lesson_notes, and blooms_taxonomy_analysis
data structures as Python dictionaries for Firestore persistence.

The module supports the final_report_inprogress and complete phases of the lesson flow.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import re
from google.cloud.firestore import SERVER_TIMESTAMP

logger = logging.getLogger(__name__)

def persist_data_to_firestore(db, session_id: str, data: Dict[str, Any]) -> bool:
    """
    Persist structured data to Firestore using merge operation.
    
    Args:
        db: Firestore database instance
        session_id: Lesson session ID
        data: Dictionary containing structured data to persist
        
    Returns:
        Boolean indicating success or failure
    """
    try:
        # Get reference to the lesson session document
        session_ref = db.collection('lesson_sessions').document(session_id)
        
        # Update the document with merge to preserve existing data
        session_ref.set(data, merge=True)
        
        logger.info(f"Successfully persisted data to Firestore for session {session_id}")
        return True
    except Exception as e:
        logger.error(f"Error persisting data to Firestore: {str(e)}")
        return False

class DataPersistenceManager:
    """
    Manager class for generating structured data for Firestore persistence.
    
    This class provides methods to generate the following data structures:
    - student_summary: Comprehensive summary of student's learning journey
    - lesson_notes: Detailed analysis of the lesson content and interactions
    - blooms_taxonomy_analysis: Cognitive metrics based on Bloom's taxonomy
    """
    
    def __init__(self, session_data: Dict[str, Any]):
        """
        Initialize the DataPersistenceManager with session data.
        
        Args:
            session_data: Dictionary containing all session information including
                          interactions, quiz responses, and lesson metadata.
        """
        self.session_data = session_data
        self.interactions = session_data.get('interactions', [])
        self.quiz_responses = session_data.get('quiz_responses', [])
        self.lesson_metadata = session_data.get('lesson_metadata', {})
        self.student_info = session_data.get('student_info', {})
        
        # Extract lesson grade and subject
        self.grade_level = self.lesson_metadata.get('grade_level', '')
        self.subject = self.lesson_metadata.get('subject', '')
        self.lesson_title = self.lesson_metadata.get('title', '')
        
        logger.info(f"DataPersistenceManager initialized for lesson: {self.lesson_title}")
    
    def generate_student_summary(self) -> Dict[str, Any]:
        """
        Generate a comprehensive student summary as a structured dictionary.
        
        Returns:
            Dictionary containing student learning journey information including:
            - learning_journey_phases: List of phases the student went through
            - homework_assignments: List of suggested homework assignments
            - performance_metrics: Dictionary of performance metrics
            - engagement_analysis: Analysis of student engagement
        """
        logger.info("Generating student summary data")
        
        # Extract learning journey phases
        learning_journey_phases = self._extract_learning_journey_phases()
        
        # Extract homework assignments
        homework_assignments = self._extract_homework_assignments()
        
        # Calculate performance metrics
        performance_metrics = self._calculate_performance_metrics()
        
        # Analyze student engagement
        engagement_analysis = self._analyze_student_engagement()
        
        # Compile the student summary
        student_summary = {
            'learning_journey_phases': learning_journey_phases,
            'homework_assignments': homework_assignments,
            'performance_metrics': performance_metrics,
            'engagement_analysis': engagement_analysis,
            'generated_at': SERVER_TIMESTAMP
        }
        
        logger.info(f"Student summary generated with {len(learning_journey_phases)} phases and {len(homework_assignments)} homework assignments")
        return student_summary
    
    def generate_lesson_notes(self) -> Dict[str, Any]:
        """
        Generate comprehensive lesson notes as a structured dictionary.
        
        Returns:
            Dictionary containing detailed lesson analysis including:
            - lesson_summary: Summary of the lesson content
            - quiz_performance: Analysis of quiz performance
            - interaction_analysis: Analysis of student-AI interactions
            - teaching_effectiveness: Metrics on teaching effectiveness
        """
        logger.info("Generating lesson notes data")
        
        # Generate lesson summary
        lesson_summary = self._generate_lesson_summary()
        
        # Analyze quiz performance
        quiz_performance = self._analyze_quiz_performance()
        
        # Analyze interactions
        interaction_analysis = self._analyze_interactions()
        
        # Evaluate teaching effectiveness
        teaching_effectiveness = self._evaluate_teaching_effectiveness()
        
        # Compile the lesson notes
        lesson_notes = {
            'lesson_summary': lesson_summary,
            'quiz_performance': quiz_performance,
            'interaction_analysis': interaction_analysis,
            'teaching_effectiveness': teaching_effectiveness,
            'generated_at': SERVER_TIMESTAMP
        }
        
        logger.info("Lesson notes generated successfully")
        return lesson_notes
    
    def generate_blooms_taxonomy_analysis(self) -> Dict[str, Any]:
        """
        Generate Bloom's taxonomy analysis as a structured dictionary.
        
        Returns:
            Dictionary containing cognitive metrics based on Bloom's taxonomy:
            - cognitive_complexity_score: Overall complexity score
            - lesson_cognitive_levels: Distribution of cognitive levels
            - learning_objectives_met: List of learning objectives achieved
            - cognitive_progression: Analysis of cognitive progression
        """
        logger.info("Generating Bloom's taxonomy analysis")
        
        # Calculate cognitive complexity score
        cognitive_complexity_score = self._calculate_cognitive_complexity()
        
        # Determine lesson cognitive levels
        lesson_cognitive_levels = self._determine_cognitive_levels()
        
        # Extract learning objectives met
        learning_objectives_met = self._extract_learning_objectives()
        
        # Analyze cognitive progression
        cognitive_progression = self._analyze_cognitive_progression()
        
        # Compile the Bloom's taxonomy analysis
        blooms_taxonomy_analysis = {
            'cognitive_complexity_score': cognitive_complexity_score,
            'lesson_cognitive_levels': lesson_cognitive_levels,
            'learning_objectives_met': learning_objectives_met,
            'cognitive_progression': cognitive_progression,
            'generated_at': SERVER_TIMESTAMP
        }
        
        logger.info(f"Bloom's taxonomy analysis generated with complexity score: {cognitive_complexity_score}")
        return blooms_taxonomy_analysis
        
    def generate_all_data(self) -> Dict[str, Dict[str, Any]]:
        """
        Generate all structured data for Firestore persistence in a single call.
        
        This method generates the following data structures:
        - student_summary: Comprehensive summary of student's learning journey
        - lesson_notes: Detailed analysis of the lesson content and interactions
        - blooms_taxonomy_analysis: Cognitive metrics based on Bloom's taxonomy
        
        Returns:
            Dictionary containing all structured data maps for Firestore persistence
        """
        logger.info("Generating all structured data for Firestore persistence")
        
        # Generate all data structures
        student_summary = self.generate_student_summary()
        lesson_notes = self.generate_lesson_notes()
        blooms_taxonomy_analysis = self.generate_blooms_taxonomy_analysis()
        
        # Compile all data into a single dictionary
        all_data = {
            'student_summary': student_summary,
            'lesson_notes': lesson_notes,
            'blooms_taxonomy_analysis': blooms_taxonomy_analysis,
            'last_updated': SERVER_TIMESTAMP
        }
        
        # Validate the data structures
        self._validate_data_structures(all_data)
        
        logger.info("All structured data generated successfully")
        return all_data
        
    def _validate_data_structures(self, data: Dict[str, Dict[str, Any]]) -> None:
        """
        Validate the generated data structures to ensure they meet requirements.
        
        Args:
            data: Dictionary containing all structured data maps
            
        Raises:
            ValueError: If any required data structure is missing or invalid
        """
        # Check that all required data structures are present
        required_structures = ['student_summary', 'lesson_notes', 'blooms_taxonomy_analysis']
        for structure in required_structures:
            if structure not in data:
                raise ValueError(f"Required data structure '{structure}' is missing")
            
            if not isinstance(data[structure], dict):
                raise ValueError(f"Data structure '{structure}' must be a dictionary")
        
        # Validate student_summary structure
        student_summary = data['student_summary']
        required_student_fields = ['learning_journey_phases', 'homework_assignments', 'performance_metrics']
        for field in required_student_fields:
            if field not in student_summary:
                logger.warning(f"Expected field '{field}' missing in student_summary")
        
        # Validate lesson_notes structure
        lesson_notes = data['lesson_notes']
        required_lesson_fields = ['lesson_summary', 'quiz_performance', 'interaction_analysis']
        for field in required_lesson_fields:
            if field not in lesson_notes:
                logger.warning(f"Expected field '{field}' missing in lesson_notes")
        
        # Validate blooms_taxonomy_analysis structure
        blooms = data['blooms_taxonomy_analysis']
        required_blooms_fields = ['cognitive_complexity_score', 'lesson_cognitive_levels', 'learning_objectives_met']
        for field in required_blooms_fields:
            if field not in blooms:
                logger.warning(f"Expected field '{field}' missing in blooms_taxonomy_analysis")
        
        logger.info("Data structure validation completed successfully")
    
    def _extract_learning_journey_phases(self) -> List[str]:
        """
        Extract the learning journey phases from session data.
        
        Returns:
            List of learning journey phases
        """
        # Default phases based on the unified pathway
        default_phases = [
            "Introduction and Diagnostic",
            "Core Concept Teaching",
            "Knowledge Assessment",
            "Lesson Completion"
        ]
        
        # Try to extract actual phases from interactions
        phases = []
        phase_markers = {
            "introduction": "Introduction and Diagnostic",
            "diagnostic": "Introduction and Diagnostic",
            "concept": "Core Concept Teaching",
            "teach": "Core Concept Teaching",
            "quiz": "Knowledge Assessment",
            "assessment": "Knowledge Assessment",
            "summary": "Lesson Completion",
            "conclusion": "Lesson Completion"
        }
        
        # Extract phases from interactions
        for interaction in self.interactions:
            content = interaction.get('content', '').lower()
            role = interaction.get('role', '').lower()
            
            if role == 'assistant':
                for keyword, phase in phase_markers.items():
                    if keyword in content and phase not in phases:
                        phases.append(phase)
        
        # If no phases were extracted, use default phases
        if not phases:
            phases = default_phases
        
        return phases
    
    def _extract_homework_assignments(self) -> List[str]:
        """
        Extract homework assignments from session data.
        
        Returns:
            List of homework assignments
        """
        homework_assignments = []
        
        # Look for homework assignments in the last few interactions
        for interaction in reversed(self.interactions):
            content = interaction.get('content', '')
            role = interaction.get('role', '').lower()
            
            if role == 'assistant':
                # Look for homework section
                homework_section = re.search(r'(?:homework|practice|assignment)[:\s]+(.*?)(?:\n\n|\Z)', 
                                            content, re.IGNORECASE | re.DOTALL)
                
                if homework_section:
                    # Extract individual assignments
                    assignments_text = homework_section.group(1)
                    assignments = re.findall(r'(?:^|\n)[\d\.\*\-]+\s+(.*?)(?:\n|$)', assignments_text)
                    
                    if assignments:
                        homework_assignments.extend(assignments)
                        break
        
        # If no homework assignments were found, generate generic ones
        if not homework_assignments:
            homework_assignments = [
                f"Review the key concepts from today's {self.subject} lesson",
                f"Practice applying the {self.subject} skills learned today",
                f"Complete the worksheet related to {self.lesson_title}"
            ]
        
        return homework_assignments
    
    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """
        Calculate performance metrics based on quiz responses.
        
        Returns:
            Dictionary of performance metrics
        """
        # Initialize metrics
        total_questions = len(self.quiz_responses)
        correct_answers = 0
        response_times = []
        
        # Process quiz responses
        for response in self.quiz_responses:
            if response.get('is_correct', False):
                correct_answers += 1
            
            # Extract response time if available
            if 'response_time_ms' in response:
                response_times.append(response.get('response_time_ms', 0))
        
        # Calculate accuracy
        accuracy = (correct_answers / total_questions) * 100 if total_questions > 0 else 0
        
        # Calculate average response time
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # Determine proficiency level
        proficiency_level = "Advanced" if accuracy >= 90 else \
                           "Proficient" if accuracy >= 75 else \
                           "Developing" if accuracy >= 60 else \
                           "Beginning"
        
        # Compile performance metrics
        performance_metrics = {
            'accuracy': round(accuracy, 2),
            'correct_answers': correct_answers,
            'total_questions': total_questions,
            'average_response_time_ms': round(avg_response_time, 2),
            'proficiency_level': proficiency_level
        }
        
        return performance_metrics
    
    def _analyze_student_engagement(self) -> Dict[str, Any]:
        """
        Analyze student engagement based on interactions.
        
        Returns:
            Dictionary containing engagement analysis
        """
        # Initialize engagement metrics
        student_messages = [i for i in self.interactions if i.get('role', '') == 'user']
        total_student_messages = len(student_messages)
        
        # Calculate average message length
        total_length = sum(len(msg.get('content', '')) for msg in student_messages)
        avg_message_length = total_length / total_student_messages if total_student_messages > 0 else 0
        
        # Calculate response rate (percentage of AI messages that received a student response)
        ai_messages = [i for i in self.interactions if i.get('role', '') == 'assistant']
        response_count = 0
        
        for i in range(len(self.interactions) - 1):
            if self.interactions[i].get('role', '') == 'assistant' and \
               self.interactions[i+1].get('role', '') == 'user':
                response_count += 1
        
        response_rate = (response_count / len(ai_messages)) * 100 if ai_messages else 0
        
        # Determine engagement level
        engagement_level = "High" if response_rate >= 80 and avg_message_length >= 20 else \
                          "Medium" if response_rate >= 60 and avg_message_length >= 10 else \
                          "Low"
        
        # Compile engagement analysis
        engagement_analysis = {
            'total_interactions': total_student_messages,
            'average_message_length': round(avg_message_length, 2),
            'response_rate': round(response_rate, 2),
            'engagement_level': engagement_level
        }
        
        return engagement_analysis
    
    def _generate_lesson_summary(self) -> Dict[str, Any]:
        """
        Generate a summary of the lesson content.
        
        Returns:
            Dictionary containing lesson summary
        """
        # Extract key topics from interactions
        key_topics = []
        topic_pattern = re.compile(r'(?:topic|concept|key point)[:\s]+([^\.]+)', re.IGNORECASE)
        
        for interaction in self.interactions:
            if interaction.get('role', '') == 'assistant':
                content = interaction.get('content', '')
                topics = topic_pattern.findall(content)
                key_topics.extend(topics)
        
        # Remove duplicates and limit to top 5
        key_topics = list(set(key_topics))[:5]
        
        # If no topics were extracted, use generic ones based on lesson metadata
        if not key_topics:
            key_topics = [f"{self.subject} fundamentals", f"{self.lesson_title} concepts"]
        
        # Extract learning objectives
        learning_objectives = self.lesson_metadata.get('learning_objectives', [])
        if not learning_objectives:
            learning_objectives = [f"Understand key concepts in {self.lesson_title}",
                                 f"Apply {self.subject} knowledge to solve problems"]
        
        # Compile lesson summary
        lesson_summary = {
            'title': self.lesson_title,
            'subject': self.subject,
            'grade_level': self.grade_level,
            'key_topics': key_topics,
            'learning_objectives': learning_objectives,
            'duration_minutes': self.session_data.get('duration_minutes', 30)
        }
        
        return lesson_summary
    
    def _analyze_quiz_performance(self) -> Dict[str, Any]:
        """
        Analyze quiz performance based on quiz responses.
        
        Returns:
            Dictionary containing quiz performance analysis
        """
        # Initialize quiz metrics
        question_breakdown = []
        strengths = []
        areas_for_improvement = []
        
        # Process each quiz question
        for i, response in enumerate(self.quiz_responses):
            question_text = response.get('question', f"Question {i+1}")
            is_correct = response.get('is_correct', False)
            
            # Add to question breakdown
            question_breakdown.append({
                'question_number': i + 1,
                'question_text': question_text[:100] + ('...' if len(question_text) > 100 else ''),
                'is_correct': is_correct,
                'student_answer': response.get('student_answer', ''),
                'correct_answer': response.get('correct_answer', '')
            })
            
            # Identify topic from question
            topic = self._extract_topic_from_question(question_text)
            
            # Add to strengths or areas for improvement
            if is_correct:
                if topic and topic not in strengths:
                    strengths.append(topic)
            else:
                if topic and topic not in areas_for_improvement:
                    areas_for_improvement.append(topic)
        
        # Ensure we have at least some strengths and areas for improvement
        if not strengths:
            strengths = ["General understanding of concepts"]
        
        if not areas_for_improvement:
            areas_for_improvement = ["Detailed application of concepts"]
        
        # Compile quiz performance analysis
        quiz_performance = {
            'question_breakdown': question_breakdown,
            'strengths': strengths[:3],  # Limit to top 3
            'areas_for_improvement': areas_for_improvement[:3],  # Limit to top 3
            'overall_score': self._calculate_performance_metrics().get('accuracy', 0)
        }
        
        return quiz_performance
    
    def _extract_topic_from_question(self, question_text: str) -> str:
        """
        Extract the topic from a quiz question.
        
        Args:
            question_text: The text of the quiz question
            
        Returns:
            Extracted topic or empty string if none found
        """
        # Try to extract topic based on common patterns
        topic_patterns = [
            r'(?:about|regarding|on|concerning)\s+([^\.?!]+)',
            r'(?:in|with)\s+([^\.?!]+)'
        ]
        
        for pattern in topic_patterns:
            match = re.search(pattern, question_text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        # If no topic found, return first few words of question
        words = question_text.split()
        if len(words) > 3:
            return ' '.join(words[1:4]) + '...'
        
        return ""
    
    def _analyze_interactions(self) -> Dict[str, Any]:
        """
        Analyze student-AI interactions.
        
        Returns:
            Dictionary containing interaction analysis
        """
        # Count interactions by phase
        phase_interactions = {
            'teaching_start': 0,
            'teaching': 0,
            'quiz_initiate': 0,
            'quiz_questions': 0,
            'quiz_results': 0,
            'final_report_inprogress': 0,
            'complete': 0
        }
        
        # Track current phase
        current_phase = 'teaching_start'
        
        # Analyze interactions
        for interaction in self.interactions:
            # Update phase if phase information is available
            if 'phase' in interaction:
                current_phase = interaction['phase']
            
            # Count interaction in current phase
            if current_phase in phase_interactions:
                phase_interactions[current_phase] += 1
        
        # Calculate interaction quality metrics
        student_messages = [i for i in self.interactions if i.get('role', '') == 'user']
        substantive_responses = 0
        
        for msg in student_messages:
            content = msg.get('content', '')
            # Consider responses with more than 15 characters as substantive
            if len(content) > 15 and not content.lower().strip() in ['yes', 'no', 'ok', 'i understand']:
                substantive_responses += 1
        
        substantive_percentage = (substantive_responses / len(student_messages)) * 100 if student_messages else 0
        
        # Compile interaction analysis
        interaction_analysis = {
            'total_interactions': len(self.interactions),
            'student_messages': len(student_messages),
            'ai_messages': len(self.interactions) - len(student_messages),
            'phase_distribution': phase_interactions,
            'substantive_responses': substantive_responses,
            'substantive_percentage': round(substantive_percentage, 2)
        }
        
        return interaction_analysis
    
    def _evaluate_teaching_effectiveness(self) -> Dict[str, Any]:
        """
        Evaluate teaching effectiveness based on interactions and quiz performance.
        
        Returns:
            Dictionary containing teaching effectiveness metrics
        """
        # Get quiz performance
        quiz_performance = self._calculate_performance_metrics()
        accuracy = quiz_performance.get('accuracy', 0)
        
        # Calculate engagement metrics
        engagement = self._analyze_student_engagement()
        engagement_level = engagement.get('engagement_level', 'Medium')
        
        # Determine teaching effectiveness score (0-100)
        # Weight: 70% quiz accuracy, 30% engagement
        effectiveness_score = (accuracy * 0.7) + \
                             (100 if engagement_level == 'High' else 
                              70 if engagement_level == 'Medium' else 40) * 0.3
        
        # Determine areas of success and improvement
        areas_of_success = []
        areas_for_improvement = []
        
        # Based on quiz performance
        if accuracy >= 80:
            areas_of_success.append("Clear explanation of concepts")
        else:
            areas_for_improvement.append("More detailed explanation of concepts")
        
        # Based on engagement
        if engagement_level == 'High':
            areas_of_success.append("Strong student engagement")
        elif engagement_level == 'Low':
            areas_for_improvement.append("Improve student engagement strategies")
        
        # Ensure we have at least some areas in each category
        if not areas_of_success:
            areas_of_success = ["Structured lesson progression"]
        
        if not areas_for_improvement:
            areas_for_improvement = ["More interactive teaching approaches"]
        
        # Compile teaching effectiveness metrics
        teaching_effectiveness = {
            'effectiveness_score': round(effectiveness_score, 2),
            'rating': "Excellent" if effectiveness_score >= 90 else
                     "Good" if effectiveness_score >= 75 else
                     "Satisfactory" if effectiveness_score >= 60 else
                     "Needs Improvement",
            'areas_of_success': areas_of_success,
            'areas_for_improvement': areas_for_improvement
        }
        
        return teaching_effectiveness
    
    def _calculate_cognitive_complexity(self) -> float:
        """
        Calculate the cognitive complexity score based on Bloom's taxonomy.
        
        Returns:
            Cognitive complexity score (0-100)
        """
        # Define Bloom's taxonomy levels and their weights
        blooms_levels = {
            'remember': 1,
            'understand': 2,
            'apply': 3,
            'analyze': 4,
            'evaluate': 5,
            'create': 6
        }
        
        # Initialize level counters
        level_counts = {level: 0 for level in blooms_levels}
        
        # Analyze quiz questions for cognitive levels
        for response in self.quiz_responses:
            question_text = response.get('question', '').lower()
            
            # Check for keywords associated with each level
            for level, keywords in self._get_blooms_keywords().items():
                for keyword in keywords:
                    if keyword in question_text:
                        level_counts[level] += 1
                        break
        
        # Calculate weighted score
        total_questions = len(self.quiz_responses)
        if total_questions == 0:
            return 50.0  # Default score if no questions
        
        weighted_sum = sum(level_counts[level] * blooms_levels[level] for level in blooms_levels)
        max_possible = total_questions * 6  # Maximum possible score (all at 'create' level)
        
        # Convert to 0-100 scale
        complexity_score = (weighted_sum / max_possible) * 100 if max_possible > 0 else 50.0
        
        return round(complexity_score, 2)
    
    def _get_blooms_keywords(self) -> Dict[str, List[str]]:
        """
        Get keywords associated with each Bloom's taxonomy level.
        
        Returns:
            Dictionary mapping Bloom's levels to keyword lists
        """
        return {
            'remember': ['recall', 'define', 'list', 'name', 'identify', 'state', 'select', 'match', 'recognize'],
            'understand': ['explain', 'interpret', 'describe', 'summarize', 'classify', 'compare', 'contrast', 'discuss'],
            'apply': ['solve', 'use', 'demonstrate', 'calculate', 'complete', 'illustrate', 'show', 'implement'],
            'analyze': ['analyze', 'distinguish', 'examine', 'compare', 'contrast', 'investigate', 'categorize'],
            'evaluate': ['evaluate', 'judge', 'critique', 'justify', 'defend', 'determine', 'support', 'convince'],
            'create': ['create', 'design', 'develop', 'formulate', 'construct', 'invent', 'produce', 'compose']
        }
    
    def _determine_cognitive_levels(self) -> Dict[str, int]:
        """
        Determine the distribution of cognitive levels in the lesson.
        
        Returns:
            Dictionary mapping Bloom's levels to counts
        """
        # Initialize level counters
        levels = {
            'remember': 0,
            'understand': 0,
            'apply': 0,
            'analyze': 0,
            'evaluate': 0,
            'create': 0
        }
        
        # Analyze quiz questions
        blooms_keywords = self._get_blooms_keywords()
        for response in self.quiz_responses:
            question_text = response.get('question', '').lower()
            
            # Check for keywords associated with each level
            for level, keywords in blooms_keywords.items():
                for keyword in keywords:
                    if keyword in question_text:
                        levels[level] += 1
                        break
        
        # Analyze teaching interactions
        for interaction in self.interactions:
            if interaction.get('role', '') == 'assistant':
                content = interaction.get('content', '').lower()
                
                # Check for keywords associated with each level
                for level, keywords in blooms_keywords.items():
                    for keyword in keywords:
                        if keyword in content:
                            levels[level] += 1
                            break
        
        return levels
    
    def _extract_learning_objectives(self) -> List[str]:
        """
        Extract learning objectives met during the lesson.
        
        Returns:
            List of learning objectives
        """
        # Try to get learning objectives from lesson metadata
        objectives = self.lesson_metadata.get('learning_objectives', [])
        
        # If no objectives found, generate them based on lesson content
        if not objectives:
            objectives = []
            
            # Look for objective statements in interactions
            objective_patterns = [
                r'(?:objective|goal|aim)[:\s]+([^\.]+)',
                r'(?:learn|understand|master)[:\s]+([^\.]+)'
            ]
            
            for interaction in self.interactions:
                if interaction.get('role', '') == 'assistant':
                    content = interaction.get('content', '')
                    
                    for pattern in objective_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        objectives.extend(matches)
            
            # Remove duplicates and limit to top 5
            objectives = list(set(objectives))[:5]
            
            # If still no objectives, generate generic ones
            if not objectives:
                objectives = [
                    f"Understand key concepts in {self.subject}",
                    f"Apply {self.subject} knowledge to solve problems",
                    f"Demonstrate comprehension of {self.lesson_title}"
                ]
        
        return objectives
    
    def _analyze_cognitive_progression(self) -> Dict[str, Any]:
        """
        Analyze the cognitive progression throughout the lesson.
        
        Returns:
            Dictionary containing cognitive progression analysis
        """
        # Initialize phase tracking
        phases = ['teaching_start', 'teaching', 'quiz_initiate', 'quiz_questions']
        phase_cognitive_levels = {phase: {} for phase in phases}
        
        # Track current phase
        current_phase = 'teaching_start'
        
        # Analyze interactions by phase
        blooms_keywords = self._get_blooms_keywords()
        for interaction in self.interactions:
            # Update phase if phase information is available
            if 'phase' in interaction:
                current_phase = interaction['phase']
                if current_phase not in phase_cognitive_levels:
                    phase_cognitive_levels[current_phase] = {}
            
            content = interaction.get('content', '').lower()
            
            # Check for keywords associated with each level
            for level, keywords in blooms_keywords.items():
                for keyword in keywords:
                    if keyword in content:
                        if level not in phase_cognitive_levels[current_phase]:
                            phase_cognitive_levels[current_phase][level] = 0
                        phase_cognitive_levels[current_phase][level] += 1
        
        # Calculate progression metrics
        progression_trend = "Ascending"  # Default
        
        # Check if higher cognitive levels appear later in the lesson
        early_phases = ['teaching_start', 'teaching']
        late_phases = ['quiz_initiate', 'quiz_questions']
        
        early_complexity = 0
        late_complexity = 0
        
        for phase in early_phases:
            for level, count in phase_cognitive_levels.get(phase, {}).items():
                level_weight = list(blooms_keywords.keys()).index(level) + 1
                early_complexity += count * level_weight
        
        for phase in late_phases:
            for level, count in phase_cognitive_levels.get(phase, {}).items():
                level_weight = list(blooms_keywords.keys()).index(level) + 1
                late_complexity += count * level_weight
        
        if early_complexity > late_complexity:
            progression_trend = "Descending"
        elif early_complexity == late_complexity:
            progression_trend = "Consistent"
        
        # Compile cognitive progression analysis
        cognitive_progression = {
            'phase_cognitive_levels': phase_cognitive_levels,
            'progression_trend': progression_trend,
            'peak_cognitive_level': self._determine_peak_cognitive_level()
        }
        
        return cognitive_progression
    
    def _determine_peak_cognitive_level(self) -> str:
        """
        Determine the peak cognitive level reached during the lesson.
        
        Returns:
            Peak cognitive level name
        """
        # Get cognitive levels
        levels = self._determine_cognitive_levels()
        
        # Find the highest level with a non-zero count
        ordered_levels = ['create', 'evaluate', 'analyze', 'apply', 'understand', 'remember']
        
        for level in ordered_levels:
            if levels.get(level, 0) > 0:
                return level
        
        return "understand"  # Default if no levels found


def generate_structured_data(session_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """
    Generate all structured data for Firestore persistence.
    
    Args:
        session_data: Dictionary containing all session information
        
    Returns:
        Dictionary containing all structured data maps:
        - student_summary
        - lesson_notes
        - blooms_taxonomy_analysis
    """
    logger.info("Generating structured data for Firestore persistence")
    
    try:
        # Initialize data persistence manager
        data_manager = DataPersistenceManager(session_data)
        
        # Generate all data structures
        student_summary = data_manager.generate_student_summary()
        lesson_notes = data_manager.generate_lesson_notes()
        blooms_taxonomy_analysis = data_manager.generate_blooms_taxonomy_analysis()
        
        # Compile all data
        structured_data = {
            'student_summary': student_summary,
            'lesson_notes': lesson_notes,
            'blooms_taxonomy_analysis': blooms_taxonomy_analysis
        }
        
        logger.info("Structured data generation complete")
        return structured_data
        
    except Exception as e:
        logger.error(f"Error generating structured data: {str(e)}")
        # Return partial data if possible
        return {
            'student_summary': {
                'error': f"Data generation failed: {str(e)}",
                'generated_at': SERVER_TIMESTAMP
            },
            'lesson_notes': {
                'error': f"Data generation failed: {str(e)}",
                'generated_at': SERVER_TIMESTAMP
            },
            'blooms_taxonomy_analysis': {
                'error': f"Data generation failed: {str(e)}",
                'generated_at': SERVER_TIMESTAMP
            }
        }