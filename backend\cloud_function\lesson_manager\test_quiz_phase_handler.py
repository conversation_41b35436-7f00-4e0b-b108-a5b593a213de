# -*- coding: utf-8 -*-
"""
Test Quiz Phase Handler

This module tests the quiz phase handler implementation for the unified lesson flow state machine.
"""

import unittest
import logging
import json
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock

# Import the quiz phase handler
from quiz_phase_handler import Quiz<PERSON>has<PERSON><PERSON><PERSON><PERSON>, quiz_phase_handler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestQuizPhaseHandler(unittest.TestCase):
    """Test cases for the QuizPhaseHandler class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.handler = QuizPhaseHandler()
        self.request_id = "test-request-123"
        
        # Sample session data
        self.session_data = {
            'session_id': 'test-session-123',
            'student_id': 'test-student-123',
            'lesson_ref': 'test-lesson-123',
            'current_phase': 'quiz_initiate',
            'teaching_complete': True,
            'assigned_level_for_teaching': 5
        }
        
        # Sample context data
        self.context = {
            'student_name': 'Test Student',
            'topic': 'Test Topic',
            'instructional_content': 'This is test instructional content about Test Topic.',
            'learning_objectives': [
                'Understand the key concepts of Test Topic',
                'Apply Test Topic principles to solve problems'
            ]
        }
        
        # Sample quiz questions
        self.quiz_questions = [
            {
                'question': 'What is Test Topic?',
                'options': [
                    'A. The correct definition',
                    'B. An incorrect definition',
                    'C. Another incorrect definition',
                    'D. Yet another incorrect definition'
                ],
                'correct_answer': 'A. The correct definition',
                'explanation': 'This is the correct definition of Test Topic.'
            },
            {
                'question': 'How do you apply Test Topic?',
                'options': [
                    'A. The correct application',
                    'B. An incorrect application',
                    'C. Another incorrect application',
                    'D. Yet another incorrect application'
                ],
                'correct_answer': 'A. The correct application',
                'explanation': 'This is the correct way to apply Test Topic.'
            }
        ]
    
    def test_quiz_initiate_new_session(self):
        """Test quiz_initiate handler with a new session"""
        # Mock the _generate_quiz_questions method
        with patch.object(self.handler, '_generate_quiz_questions', return_value=self.quiz_questions):
            # Call the handler
            response, state_updates, next_phase = self.handler.handle_quiz_initiate(
                self.session_data, "Let's start the quiz", self.context, self.request_id
            )
            
            # Verify the response
            self.assertIn("Quiz Time!", response)
            self.assertIn("Question 1", response)
            self.assertIn("Test Student", response)
            
            # Verify the state updates
            self.assertIn('quiz_start_time', state_updates)
            self.assertEqual(state_updates['quiz_interactions'], 0)
            self.assertEqual(state_updates['current_question_index'], 0)
            self.assertEqual(state_updates['quiz_complete'], False)
            self.assertEqual(state_updates['current_phase'], 'quiz_initiate')
            self.assertEqual(len(state_updates['quiz_questions_generated']), 2)
            
            # Verify the next phase
            self.assertEqual(next_phase, 'quiz_questions')
    
    def test_quiz_initiate_existing_session(self):
        """Test quiz_initiate handler with an existing session"""
        # Add quiz data to session
        session_data = self.session_data.copy()
        session_data.update({
            'quiz_start_time': datetime.now(timezone.utc).isoformat(),
            'quiz_interactions': 0,
            'quiz_questions_generated': self.quiz_questions,
            'current_question_index': 0,
            'quiz_complete': False
        })
        
        # Call the handler
        response, state_updates, next_phase = self.handler.handle_quiz_initiate(
            session_data, "Let's continue", self.context, self.request_id
        )
        
        # Verify the response
        self.assertIn("Quiz Time!", response)
        self.assertIn("Question 1", response)
        
        # Verify the state updates
        self.assertEqual(state_updates['current_phase'], 'quiz_questions')
        
        # Verify the next phase
        self.assertEqual(next_phase, 'quiz_questions')
    
    def test_quiz_questions_first_answer(self):
        """Test quiz_questions handler with the first answer"""
        # Add quiz data to session
        session_data = self.session_data.copy()
        session_data.update({
            'quiz_start_time': datetime.now(timezone.utc).isoformat(),
            'quiz_interactions': 0,
            'quiz_questions_generated': self.quiz_questions,
            'current_question_index': 0,
            'quiz_answers': [],
            'quiz_complete': False,
            'current_phase': 'quiz_questions'
        })
        
        # Call the handler with a correct answer
        response, state_updates, next_phase = self.handler.handle_quiz_questions(
            session_data, "A", self.context, self.request_id
        )
        
        # Verify the response
        self.assertIn("Question 2", response)
        self.assertIn("Correct!", response)
        
        # Verify the state updates
        self.assertEqual(state_updates['quiz_interactions'], 1)
        self.assertEqual(state_updates['current_question_index'], 1)
        self.assertEqual(len(state_updates['quiz_answers']), 1)
        self.assertEqual(state_updates['quiz_answers'][0]['is_correct'], True)
        self.assertEqual(state_updates['current_phase'], 'quiz_questions')
        
        # Verify the next phase
        self.assertEqual(next_phase, 'quiz_questions')
    
    def test_quiz_questions_last_answer(self):
        """Test quiz_questions handler with the last answer"""
        # Add quiz data to session
        session_data = self.session_data.copy()
        session_data.update({
            'quiz_start_time': datetime.now(timezone.utc).isoformat(),
            'quiz_interactions': 1,
            'quiz_questions_generated': self.quiz_questions,
            'current_question_index': 1,  # Last question
            'quiz_answers': [
                {
                    'question_index': 0,
                    'question_text': self.quiz_questions[0]['question'],
                    'user_answer': 'A',
                    'correct_answer': self.quiz_questions[0]['correct_answer'],
                    'options': self.quiz_questions[0]['options'],
                    'is_correct': True,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
            ],
            'quiz_complete': False,
            'current_phase': 'quiz_questions'
        })
        
        # Call the handler with an incorrect answer
        response, state_updates, next_phase = self.handler.handle_quiz_questions(
            session_data, "B", self.context, self.request_id
        )
        
        # Verify the response
        self.assertIn("Quiz Complete!", response)
        self.assertIn("Not quite", response)
        self.assertIn("Score", response)
        
        # Verify the state updates
        self.assertEqual(state_updates['quiz_interactions'], 2)
        self.assertEqual(state_updates['current_question_index'], 2)
        self.assertEqual(len(state_updates['quiz_answers']), 2)
        self.assertEqual(state_updates['quiz_answers'][1]['is_correct'], False)
        self.assertEqual(state_updates['quiz_complete'], True)
        self.assertEqual(state_updates['current_phase'], 'quiz_results')
        self.assertIn('quiz_score', state_updates)
        self.assertEqual(state_updates['quiz_correct_answers'], 1)
        self.assertEqual(state_updates['quiz_total_questions'], 2)
        
        # Verify the next phase
        self.assertEqual(next_phase, 'quiz_results')
    
    def test_quiz_questions_error_handling(self):
        """Test quiz_questions handler error handling"""
        # Add quiz data to session with invalid state
        session_data = self.session_data.copy()
        session_data.update({
            'quiz_start_time': datetime.now(timezone.utc).isoformat(),
            'quiz_interactions': 0,
            'quiz_questions_generated': [],  # Empty questions list
            'current_question_index': 0,
            'quiz_answers': [],
            'quiz_complete': False,
            'current_phase': 'quiz_questions'
        })
        
        # Call the handler
        response, state_updates, next_phase = self.handler.handle_quiz_questions(
            session_data, "A", self.context, self.request_id
        )
        
        # Verify the response contains an error message
        self.assertIn("I'm having trouble", response)
        
        # Verify the state updates contain an error
        self.assertIn('error', state_updates)
        self.assertEqual(state_updates['current_phase'], 'quiz_questions')
        
        # Verify the next phase
        self.assertEqual(next_phase, 'quiz_questions')
    
    def test_evaluate_answer_correct(self):
        """Test the _evaluate_answer method with correct answers"""
        question = self.quiz_questions[0]
        
        # Test exact match
        self.assertTrue(self.handler._evaluate_answer(
            "A. The correct definition", question, self.request_id
        ))
        
        # Test option letter only
        self.assertTrue(self.handler._evaluate_answer(
            "A", question, self.request_id
        ))
        
        # Test case insensitivity
        self.assertTrue(self.handler._evaluate_answer(
            "a", question, self.request_id
        ))
        
        # Test with extra text
        self.assertTrue(self.handler._evaluate_answer(
            "I think the answer is A. The correct definition", question, self.request_id
        ))
    
    def test_evaluate_answer_incorrect(self):
        """Test the _evaluate_answer method with incorrect answers"""
        question = self.quiz_questions[0]
        
        # Test wrong option
        self.assertFalse(self.handler._evaluate_answer(
            "B. An incorrect definition", question, self.request_id
        ))
        
        # Test wrong option letter
        self.assertFalse(self.handler._evaluate_answer(
            "B", question, self.request_id
        ))
        
        # Test invalid input
        self.assertFalse(self.handler._evaluate_answer(
            "I don't know", question, self.request_id
        ))

if __name__ == '__main__':
    unittest.main()