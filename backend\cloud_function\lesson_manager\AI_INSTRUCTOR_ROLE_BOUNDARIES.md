# AI Instructor Role Boundaries

## Overview

This document defines the strict role boundaries for the AI instructor within the unified lesson flow. The AI instructor's role is limited to specific phases, with a clear handoff point to backend-controlled phases.

## Unified Lesson Pathway

The system enforces a single, non-negotiable lesson flow pathway:

```
teaching_start/teaching → quiz_initiate → quiz_questions → quiz_results → final_report_inprogress → complete
```

## AI Instructor Role Definition

The AI instructor's role is strictly limited to:

1. **teaching_start phase**: Initial lesson introduction and beginning of teaching
2. **teaching phase**: Core lesson content delivery and interaction with student
3. **quiz_initiate phase**: Handoff to backend for quiz processing

## Backend-Controlled Phases

The following phases are exclusively controlled by the backend system:

1. **quiz_questions**: Programmatic quiz question delivery and answer processing
2. **quiz_results**: Quiz score calculation and results presentation
3. **final_report_inprogress**: Data generation and Firestore persistence
4. **complete**: Terminal state indicating successful lesson completion

## Handoff Mechanism

The handoff from AI instructor to backend occurs at the transition from teaching to quiz_initiate phase:

1. When teaching completion criteria are met (85%+ objective coverage at 8+ interactions), the AI instructor must execute the handoff protocol
2. The handoff message must explicitly state that control is being transferred to the assessment system
3. The state update block must include `"handoff_to_backend": true` to signal the transition
4. After handoff, the backend takes complete control of the lesson flow

## Enforcement Mechanisms

The system enforces these role boundaries through multiple layers:

1. **AI Instructions**: The BASE_INSTRUCTOR_RULES template explicitly defines role boundaries
2. **Teaching Rules Engine**: The `enforce_ai_instructor_role_boundaries` method validates AI responses
3. **Backend Validation**: The state machine rejects invalid phase transitions
4. **Monitoring**: Comprehensive logging of all role boundary violations

## Implementation Details

### Teaching Rules Engine

The `TeachingRulesEngine` class includes methods to:

- Validate teaching completion criteria
- Enforce phase consistency between content and phase
- Enforce AI instructor role boundaries
- Prevent quiz content generation during teaching phase
- Ensure proper handoff execution at quiz_initiate phase

### BASE_INSTRUCTOR_RULES Template

The template includes explicit instructions:

- Clear definition of AI instructor role boundaries
- Explicit handoff protocol requirements
- Prohibition of AI involvement in backend-controlled phases
- Mandatory state update block format for handoff

## Testing and Verification

The system includes comprehensive tests to verify role boundary enforcement:

1. **Unit Tests**: Validate individual boundary enforcement rules
2. **Simulation Tests**: Verify behavior across the entire lesson flow
3. **Integration Tests**: Ensure proper handoff between AI and backend

## Benefits

This strict role boundary enforcement ensures:

1. **Consistency**: All lessons follow the exact same flow
2. **Reliability**: Backend-controlled phases operate predictably
3. **Data Integrity**: Structured data generation and persistence is consistent
4. **User Experience**: Students receive a seamless experience through the handoff