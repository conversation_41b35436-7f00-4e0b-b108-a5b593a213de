2025-07-22 19:53:23,515 - INFO - [main.py:7143] - Incoming request: {"request_id": "f42c82b2-e85e-458f-9c52-f9f1a8178ffd", "timestamp": "2025-07-22T18:53:23.515660+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-********-a9d2-44b1-a778-78455bae1f78", "chat_history": []}}
2025-07-22 19:53:23,516 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 19:53:23,516 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FojRArwf7TCv4y3JoZYASQTKKeemWG0Zxr2u8rLS_3HvNj0ZMYI-0G7Xbxx3Yhp6l5qhe14xG2DAhf0vXXcHgss9zySkSFdJqJhUCVL5I9WvN8l9G6LqHsOIOxXc3rKImNtmJ6U38HeqI3grXrFtqbS8AGZA8gVhKt97seDdqmUoIJxTvRa9B_CoDHpRgoPtma8eSC-2Z6xEepuhE0cmy8pqmRCRODvn7pgZ_TnOTK0AiA-OiH-PcV0sU9jLg2wdFacFrdCvYzgL_lTphao7Yrm-W1XQgBYZ4WRMLvIsy6AYZpEbXBcM2vfuQRBl3sqVAw2Ji5b4XU77_645ujHrDw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '301', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 19:53:23,517 - INFO - [auth_decorator.py:70] - 🔒 Request ID: f42c82b2-e85e-458f-9c52-f9f1a8178ffd
2025-07-22 19:53:23,517 - INFO - [auth_decorator.py:74] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 19:53:23,518 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 19:53:23,519 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 19:53:23,521 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 19:53:23,521 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 19:53:23,523 - INFO - [auth_decorator.py:95] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd][require_auth] Development mode detected - bypassing authentication
2025-07-22 19:53:23,523 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 19:53:23,524 - INFO - [auth_decorator.py:121] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 19:53:34,892 - INFO - [main.py:71] - ============================================================
2025-07-22 19:53:34,892 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-22 19:53:34,892 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 19:53:34,893 - INFO - [main.py:74] - Platform: win32
2025-07-22 19:53:34,893 - INFO - [main.py:75] - ============================================================
2025-07-22 19:53:34,902 - INFO - [main.py:216] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-22 19:53:34,903 - INFO - [main.py:536] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-22 19:53:34,904 - INFO - [main.py:817] - ================================================================================
2025-07-22 19:53:34,905 - INFO - [main.py:818] - LESSON MANAGER BACKEND STARTING UP
2025-07-22 19:53:34,905 - INFO - [main.py:819] - ================================================================================
2025-07-22 19:53:34,905 - INFO - [main.py:820] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 19:53:34,906 - INFO - [main.py:821] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager
2025-07-22 19:53:34,906 - INFO - [main.py:822] - Log level: DEBUG
2025-07-22 19:53:34,906 - INFO - [main.py:823] - ================================================================================
2025-07-22 19:53:34,906 - INFO - [main.py:825] - Logging configuration complete with immediate console output
2025-07-22 19:53:34,907 - INFO - [main.py:826] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-22 19:53:34,908 - INFO - [main.py:1522] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-22 19:53:34,909 - INFO - [main.py:1529] - [OK] Enhanced state and auth managers initialized successfully
2025-07-22 19:53:34,912 - INFO - [main.py:1761] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-22 19:53:34,912 - INFO - [main.py:1790] - Phase transition fixes imported successfully
2025-07-22 19:53:34,916 - INFO - [main.py:5274] - Successfully imported utils functions
2025-07-22 19:53:34,916 - INFO - [main.py:5282] - Successfully imported extract_ai_state functions
2025-07-22 19:53:34,921 - INFO - [main.py:5773] - FLASK: Using unified Firebase initialization approach...
2025-07-22 19:53:34,921 - INFO - [unified_firebase_init.py:42] - Firebase already initialized
2025-07-22 19:53:34,921 - INFO - [main.py:5781] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-22 19:53:34,921 - INFO - [main.py:5871] - Gemini API will be initialized on first use (lazy loading).
2025-07-22 19:53:34,935 - INFO - [main.py:20736] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-22 19:53:34,936 - INFO - [main.py:20779] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-22 19:53:34,944 - INFO - [main.py:2249] - Successfully imported timetable_generator functions
2025-07-22 19:53:34,954 - INFO - [main.py:27008] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-22 19:53:35,068 - INFO - [main.py:27011] - Google Cloud Storage client initialized successfully.
2025-07-22 19:53:35,359 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:53:35,360 - INFO - [auth_decorator.py:164] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:53:35,361 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 19:53:35,366 - INFO - [main.py:7327] -
================================================================================
2025-07-22 19:53:35,366 - WARNING - [main.py:7328] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 19:53:35,367 - WARNING - [main.py:7329] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 19:53:35,367 - DEBUG - [main.py:7337] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Console output error: I/O operation on closed file.
2025-07-22 19:53:35,367 - INFO - [main.py:8179] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"Start diagnostic assessment","country":"Nigeria","curriculum":"National Curriculum","grade":"primary-5","subject":"Artificial Intelligence","session_id":"fallback-********-a9d2-44b1-a778-78455bae1f78","chat_history":[]}...
2025-07-22 19:53:35,368 - INFO - [main.py:8181] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': 'Start diagnostic assessment', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-********-a9d2-44b1-a778-78455bae1f78', 'chat_history': []}
2025-07-22 19:53:35,369 - INFO - [main.py:8183] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍  - Session ID from payload: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:53:35,369 - INFO - [main.py:8184] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 19:53:35,370 - INFO - [main.py:8185] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 19:53:35,370 - DEBUG - [main.py:8223] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:53:35,370 - INFO - [main.py:8224] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:53:35,371 - INFO - [main.py:8264] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Level not provided, determined from grade 'primary-5': 5
2025-07-22 19:53:35,839 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-22 19:53:36,254 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-22 19:53:36,543 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 19:53:36,543 - INFO - [main.py:8281] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 19:53:36,543 - INFO - [main.py:8282] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 19:53:36,544 - INFO - [main.py:2639] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 PARAMETER MAPPING:
2025-07-22 19:53:36,544 - INFO - [main.py:2640] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📥 Original: country=Nigeria, curriculum=National Curriculum, subject=Artificial Intelligence, level=5
2025-07-22 19:53:36,544 - INFO - [main.py:2641] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📤 Mapped: country=Nigeria, curriculum=National Curriculum, subject=Artificial Intelligence, level=P5
2025-07-22 19:53:36,545 - INFO - [main.py:2646] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🎓 Grade mapping: primary-5 → Primary 5
2025-07-22 19:53:36,545 - INFO - [main.py:2648] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] fetch_lesson_data: Fetching lesson with parameters:
2025-07-22 19:53:36,545 - INFO - [main.py:2649] -   • Country (original): Nigeria
2025-07-22 19:53:36,546 - INFO - [main.py:2650] -   • Country (for Firestore): Nigeria
2025-07-22 19:53:36,546 - INFO - [main.py:2651] -   • Curriculum (original): National Curriculum
2025-07-22 19:53:36,546 - INFO - [main.py:2652] -   • Curriculum (for Firestore): National Curriculum
2025-07-22 19:53:36,547 - INFO - [main.py:2653] -   • Grade (original): primary-5
2025-07-22 19:53:36,547 - INFO - [main.py:2654] -   • Grade (normalized for Firestore): Primary 5
2025-07-22 19:53:36,547 - INFO - [main.py:2655] -   • Level (original): 5
2025-07-22 19:53:36,548 - INFO - [main.py:2656] -   • Level (for Firestore): P5
2025-07-22 19:53:36,548 - INFO - [main.py:2657] -   • Subject (original): Artificial Intelligence
2025-07-22 19:53:36,548 - INFO - [main.py:2658] -   • Subject (for Firestore): Artificial Intelligence
2025-07-22 19:53:36,549 - INFO - [main.py:2659] -   • Lesson ID: P5-AI-089
2025-07-22 19:53:36,549 - INFO - [main.py:2668] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 PATH CONSTRUCTION:
2025-07-22 19:53:36,549 - INFO - [main.py:2669] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]    └─ countries/Nigeria
2025-07-22 19:53:36,550 - INFO - [main.py:2670] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]       └─ curriculums/National Curriculum
2025-07-22 19:53:36,550 - INFO - [main.py:2671] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]          └─ grades/Primary 5
2025-07-22 19:53:36,550 - INFO - [main.py:2672] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]             └─ levels/P5
2025-07-22 19:53:36,551 - INFO - [main.py:2673] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]                └─ subjects/Artificial Intelligence
2025-07-22 19:53:36,551 - INFO - [main.py:2674] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]                   └─ lessonRef/P5-AI-089
2025-07-22 19:53:36,551 - INFO - [main.py:2675] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📍 FULL PATH: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Artificial Intelligence/lessonRef/P5-AI-089
2025-07-22 19:53:36,551 - INFO - [main.py:2676] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🆔 LESSON REF: P5-AI-089
2025-07-22 19:53:36,552 - INFO - [main.py:2689] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Attempting primary path: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Artificial Intelligence/lessonRef/P5-AI-089
2025-07-22 19:53:37,082 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,082 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:37,083 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,083 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,083 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:37,083 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,084 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,084 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,085 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,085 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:37,086 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,087 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:37,088 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:37,089 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:37,089 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:37,090 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:37,090 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,091 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:37,091 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:37,091 - INFO - [main.py:2758] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Successfully retrieved and sanitized document with keys: ['additionalNotes', 'content', 'subject', 'existingAssessments', 'id', 'instructionalSteps', 'lessonTimeLength', 'learningObjectives', 'metadata', 'digitalMaterials', 'gradeLevel', 'lessonRef', 'lessonTitle', 'topic', 'country', 'extensionActivities', 'theme', 'adaptiveStrategies', 'conclusion', 'curriculumType', 'quizzesAndAssessments', 'introduction']
2025-07-22 19:53:37,091 - INFO - [main.py:17551] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 Enhancing lesson data compatibility
2025-07-22 19:53:37,091 - INFO - [main.py:17596] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Set grade: Primary 5
2025-07-22 19:53:37,092 - INFO - [main.py:17664] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Lesson data enhancement complete
2025-07-22 19:53:37,092 - INFO - [main.py:2847] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🗺️ Starting robust field mapping for lesson P5-AI-089
2025-07-22 19:53:37,093 - INFO - [main.py:2875] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Core fields mapped: subject=Artificial Intelligence, topic=AI in Healthcare, grade=Primary 5
2025-07-22 19:53:37,093 - INFO - [main.py:3045] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 Extracting key concepts from lesson data
2025-07-22 19:53:37,093 - INFO - [main.py:3052] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Found 0 existing key concepts
2025-07-22 19:53:37,094 - INFO - [main.py:3068] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Extracted concepts from 2 learning objectives
2025-07-22 19:53:37,094 - INFO - [main.py:3094] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Extracted concepts from instructional steps
2025-07-22 19:53:37,094 - INFO - [main.py:3112] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Added topic and subject-based concepts
2025-07-22 19:53:37,094 - INFO - [main.py:3150] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Key concepts extraction complete: 10 unique concepts
2025-07-22 19:53:37,095 - INFO - [main.py:3324] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Universal content extraction: 1020 characters from 4 steps
2025-07-22 19:53:37,095 - INFO - [main.py:3432] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Universal conversion: 4 steps → 4 sections
2025-07-22 19:53:37,095 - INFO - [main.py:2987] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Field mapping completed successfully:
2025-07-22 19:53:37,095 - INFO - [main.py:2988] -   - Subject: Artificial Intelligence
2025-07-22 19:53:37,096 - INFO - [main.py:2989] -   - Topic: AI in Healthcare
2025-07-22 19:53:37,096 - INFO - [main.py:2990] -   - Grade: Primary 5
2025-07-22 19:53:37,096 - INFO - [main.py:2991] -   - Key Concepts: 10 extracted
2025-07-22 19:53:37,096 - INFO - [main.py:2992] -   - Instructional Steps: 4
2025-07-22 19:53:37,096 - INFO - [main.py:2993] -   - Total Fields: 38
2025-07-22 19:53:37,097 - INFO - [main.py:3479] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Universal content structure recognized: instructionalSteps (4 steps)
2025-07-22 19:53:37,097 - INFO - [main.py:3500] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ All required fields present after universal mapping
2025-07-22 19:53:37,097 - INFO - [main.py:3509] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📊 Lesson validation complete: 38 fields available for processing
2025-07-22 19:53:37,097 - INFO - [main.py:2773] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Successfully mapped lesson fields for AI inference
2025-07-22 19:53:37,098 - DEBUG - [main.py:865] - Cached result for fetch_lesson_data
2025-07-22 19:53:37,387 - INFO - [main.py:8375] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 19:53:37,387 - INFO - [main.py:8500] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Successfully retrieved lesson from primary path
2025-07-22 19:53:37,387 - INFO - [main.py:8511] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 19:53:37,387 - INFO - [main.py:8550] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 19:53:37,388 - INFO - [main.py:4456] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 19:53:37,389 - INFO - [main.py:5862] - Gemini API configured successfully with models/gemini-2.5-flash-lite-preview-06-17 and safety filters disabled.
2025-07-22 19:53:37,895 - INFO - [main.py:4522] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 19:53:37,895 - INFO - [main.py:4522] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 19:53:37,896 - INFO - [main.py:4522] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 19:53:37,896 - INFO - [main.py:4522] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 19:53:37,896 - INFO - [main.py:4522] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 19:53:37,896 - INFO - [main.py:4591] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 19:53:37,897 - DEBUG - [main.py:4605] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 19:53:37,897 - DEBUG - [main.py:4608] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 19:53:37,897 - DEBUG - [main.py:4609] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 19:53:37,898 - DEBUG - [main.py:4610] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 19:53:37,898 - INFO - [main.py:4614] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Calling Gemini API for module inference...
2025-07-22 19:53:39,296 - INFO - [main.py:4624] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Gemini API call completed in 1.40s. Raw response: 'ai_tools_and_applications'
2025-07-22 19:53:39,297 - DEBUG - [main.py:4646] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 19:53:39,298 - INFO - [main.py:4651] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 19:53:39,298 - INFO - [main.py:8584] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 19:53:39,299 - INFO - [main.py:4700] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] CACHE MISS or fetch: Getting GS levels for subject 'artificial_intelligence', module 'ai_tools_and_applications'.
2025-07-22 19:53:39,583 - INFO - [main.py:4723] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Fetched metadata for module: 'AI Tools & Applications'
2025-07-22 19:53:40,116 - INFO - [main.py:4755] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Successfully fetched 10 levels for module 'ai_tools_and_applications'.
2025-07-22 19:53:40,116 - INFO - [main.py:8621] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 19:53:40,412 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 19:53:40,899 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 19:53:40,900 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:53:40,900 - DEBUG - [main.py:8676] - 🔍   - Document Exists: False
2025-07-22 19:53:40,900 - DEBUG - [main.py:8677] - 🔍   - Current Phase: Not found
2025-07-22 19:53:40,900 - DEBUG - [main.py:8678] - 🔍   - Probing Level: Not found
2025-07-22 19:53:40,901 - DEBUG - [main.py:8679] - 🔍   - Question Index: Not found
2025-07-22 19:53:40,901 - WARNING - [main.py:8685] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 SESSION STATE DEBUG:
2025-07-22 19:53:40,901 - WARNING - [main.py:8686] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   - Session exists: False
2025-07-22 19:53:40,901 - WARNING - [main.py:8687] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   - Current phase: None
2025-07-22 19:53:40,902 - WARNING - [main.py:8688] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   - State data keys: []
2025-07-22 19:53:40,902 - DEBUG - [main.py:8706] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 19:53:40,902 - DEBUG - [main.py:8707] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Retrieved Phase: 'None'
2025-07-22 19:53:40,902 - DEBUG - [main.py:8708] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Diagnostic Completed: False
2025-07-22 19:53:40,903 - DEBUG - [main.py:8709] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Assigned Level: None
2025-07-22 19:53:40,903 - WARNING - [main.py:8710] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔒 STATE PROTECTION: phase='None', diagnostic_done=False, level=None
2025-07-22 19:53:40,904 - INFO - [main.py:8742] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 19:53:40,904 - INFO - [main.py:8743] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] State protection not triggered
2025-07-22 19:53:40,904 - INFO - [main.py:8791] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 19:53:40,904 - INFO - [main.py:8792] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   assigned_level_for_teaching (session): None
2025-07-22 19:53:40,904 - INFO - [main.py:8793] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   latest_assessed_level (profile): None
2025-07-22 19:53:40,905 - INFO - [main.py:8794] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   teaching_level_for_returning_student: None
2025-07-22 19:53:40,905 - INFO - [main.py:8795] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   has_completed_diagnostic_before: False
2025-07-22 19:53:40,905 - INFO - [main.py:8796] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   is_first_encounter_for_module: True
2025-07-22 19:53:40,905 - WARNING - [main.py:8801] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 19:53:40,905 - INFO - [main.py:8809] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 PHASE INVESTIGATION:
2025-07-22 19:53:40,906 - INFO - [main.py:8810] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Retrieved from Firestore: 'None'
2025-07-22 19:53:40,906 - INFO - [main.py:8811] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 19:53:40,906 - INFO - [main.py:8812] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Is first encounter: True
2025-07-22 19:53:40,906 - INFO - [main.py:8813] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Diagnostic completed: False
2025-07-22 19:53:40,906 - INFO - [main.py:8826] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   No stored phase found, starting with: 'smart_diagnostic_start'
2025-07-22 19:53:40,906 - INFO - [main.py:8833] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 19:53:40,907 - INFO - [main.py:8835] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Final phase for AI logic: smart_diagnostic_start
2025-07-22 19:53:40,907 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 19:53:40,907 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 19:53:40,907 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 19:53:40,907 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 19:53:40,907 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 19:53:40,908 - WARNING - [main.py:4150] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] State key 'current_probing_level_number' had non-numeric value 'None', using default 2.
2025-07-22 19:53:40,908 - INFO - [main.py:8855] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] NEW SESSION: Forcing question_index to 0 (was: N/A)
2025-07-22 19:53:40,908 - INFO - [main.py:6144] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Diagnostic context validation passed
2025-07-22 19:53:40,908 - WARNING - [main.py:9007] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_start' for first encounter
2025-07-22 19:53:40,909 - INFO - [main.py:9030] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_start
2025-07-22 19:53:40,909 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_start'
2025-07-22 19:53:40,909 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_start'
2025-07-22 19:53:40,909 - INFO - [main.py:9042] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Robust context prepared successfully. Phase: smart_diagnostic_start
2025-07-22 19:53:40,910 - DEBUG - [main.py:9043] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 19:53:40,910 - INFO - [main.py:9285] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_start
2025-07-22 19:53:40,910 - INFO - [main.py:9286] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🎯 UNIFIED STATE MACHINE: Processing user query: Start diagnostic assessment...
2025-07-22 19:53:40,911 - WARNING - [main.py:9328] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ⚠️ Unknown phase 'smart_diagnostic_start', enforcing unified pathway
2025-07-22 19:53:40,911 - WARNING - [main.py:9329] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ⚠️ Defaulting to teaching_start phase
2025-07-22 19:53:40,911 - INFO - [main.py:7513] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📚 TEACHING PHASE HANDLER: Processing teaching content
2025-07-22 19:53:40,911 - INFO - [main.py:7545] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🤖 AI INSTRUCTOR: Continuing teaching phase
2025-07-22 19:53:40,962 - INFO - [main.py:11163] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:53:40,963 - DEBUG - [main.py:11172] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Console output error: I/O operation on closed file.
2025-07-22 19:53:40,965 - INFO - [main.py:11176] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🚀 First interaction - initializing chat session
2025-07-22 19:53:40,966 - INFO - [main.py:5921] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🚀 Initializing chat session for lesson: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:53:40,968 - INFO - [main.py:6005] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Creating system prompt for Andrea, Grade primary-5, Topic: AI in Healthcare
2025-07-22 19:53:40,969 - INFO - [main.py:5936] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 💰 Making SINGLE API call to initialize lesson session
2025-07-22 19:53:42,046 - INFO - [main.py:5944] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ Lesson session fallback-********-a9d2-44b1-a778-78455bae1f78 initialized successfully
2025-07-22 19:53:42,046 - INFO - [main.py:5945] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 💰 COST OPTIMIZATION: All subsequent interactions will use NO additional API calls
2025-07-22 19:53:42,046 - INFO - [main.py:7573] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📊 TEACHING PROGRESS: 1 interactions
2025-07-22 19:53:42,047 - INFO - [main.py:7574] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📊 OBJECTIVES COVERAGE: 0.0%
2025-07-22 19:53:42,047 - INFO - [main.py:9342] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 19:53:42,047 - INFO - [main.py:5710] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔄 PHASE TRANSITION: smart_diagnostic_start → teaching_start
2025-07-22 19:53:42,047 - INFO - [phase_transition_integrity.py:153] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_start → teaching_start
2025-07-22 19:53:42,048 - WARNING - [phase_transition_integrity.py:191] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 DATA RECOVERY APPLIED: Recovered assigned_level_for_teaching as 5
2025-07-22 19:53:42,049 - ERROR - [phase_transition_integrity.py:719] - 🔍 STATE CONSISTENCY ERRORS DETECTED:
2025-07-22 19:53:42,049 - ERROR - [phase_transition_integrity.py:721] - 🔍   1. Cannot enter teaching phase without completing diagnostic
2025-07-22 19:53:42,050 - ERROR - [phase_transition_integrity.py:722] - 🔍 SESSION DATA DEBUG:
2025-07-22 19:53:42,050 - ERROR - [phase_transition_integrity.py:723] - 🔍   - diagnostic_complete: False
2025-07-22 19:53:42,051 - ERROR - [phase_transition_integrity.py:724] - 🔍   - assigned_level_for_teaching: None
2025-07-22 19:53:42,051 - ERROR - [phase_transition_integrity.py:725] - 🔍   - teaching_complete: False
2025-07-22 19:53:42,052 - ERROR - [phase_transition_integrity.py:726] - 🔍   - teaching_interactions: 1
2025-07-22 19:53:42,052 - ERROR - [phase_transition_integrity.py:727] - 🔍   - from_phase: smart_diagnostic_start, to_phase: teaching_start
2025-07-22 19:53:42,052 - ERROR - [phase_transition_integrity.py:741] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 STATE CORRECTION ATTEMPT: smart_diagnostic_start → teaching_start
2025-07-22 19:53:42,052 - ERROR - [phase_transition_integrity.py:748] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 CORRECTION CHECK: diagnostic_complete=False, assigned_level=None
2025-07-22 19:53:42,053 - ERROR - [phase_transition_integrity.py:781] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 CORRECTION RESULT: {'corrected': False, 'corrected_phase': 'teaching_start', 'corrected_data': {}, 'message': 'No corrections applied'}
2025-07-22 19:53:42,053 - WARNING - [phase_transition_integrity.py:224] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 AUTO-CORRECTION: diagnostic_complete=True, assigned_level=1 for transition to teaching
2025-07-22 19:53:42,053 - INFO - [phase_transition_integrity.py:235] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ TRANSITION VALIDATED: smart_diagnostic_start → teaching_start
2025-07-22 19:53:42,053 - INFO - [main.py:5718] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ VALID TRANSITION: smart_diagnostic_start → teaching_start
2025-07-22 19:53:42,054 - INFO - [main.py:9358] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_start → teaching_start
2025-07-22 19:53:42,054 - INFO - [main.py:9359] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 19:53:42,346 - INFO - [main.py:9383] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 19:53:42,347 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:53:42,347 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:53:42,347 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:53:42,348 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:53:42,348 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:53:42,348 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:53:42,348 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:53:42,348 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:53:42,349 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:53:42,349 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:53:42,349 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:53:42,350 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:53:42,351 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:53:42,351 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:53:42,351 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:53:42,351 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:53:42,351 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:53:42,351 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:53:42,352 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:53:42,352 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:53:42,352 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:53:42,352 - INFO - [intelligent_guardrails.py:130] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:53:42,353 - INFO - [intelligent_guardrails.py:183] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:53:42,353 - INFO - [intelligent_guardrails.py:675] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 19:53:42,353 - INFO - [main.py:9515] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 19:53:42,354 - WARNING - [main.py:9563] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🤖 AI RESPONSE RECEIVED:
2025-07-22 19:53:42,354 - WARNING - [main.py:9564] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🤖   - Content length: 289 chars
2025-07-22 19:53:42,354 - WARNING - [main.py:9565] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q1', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:53:42,354 - WARNING - [main.py:9566] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🤖   - Raw state block: None...
2025-07-22 19:53:42,355 - DEBUG - [main.py:10099] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 19:53:42,355 - DEBUG - [main.py:10100] - 🤖   Content: Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions t...
2025-07-22 19:53:42,355 - DEBUG - [main.py:10101] - 🤖   State: {'new_phase': 'smart_diagnostic_q1', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:53:42,356 - INFO - [main.py:10127] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 19:53:42,356 - INFO - [main.py:10128] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q1, Session=smart_diagnostic_start, Final=smart_diagnostic_q1
2025-07-22 19:53:42,841 - WARNING - [main.py:10230] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_start', new_phase='smart_diagnostic_q1'
2025-07-22 19:53:42,841 - INFO - [main.py:6365] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI state update validation passed: smart_diagnostic_start → smart_diagnostic_q1
2025-07-22 19:53:42,841 - WARNING - [main.py:10239] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 19:53:42,842 - WARNING - [main.py:10258] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔄 PHASE TRANSITION: smart_diagnostic_start → smart_diagnostic_q1
2025-07-22 19:53:42,842 - INFO - [main.py:10289] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 19:53:42,843 - INFO - [main.py:10294] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 19:53:42,843 - INFO - [phase_transition_integrity.py:330] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_start → teaching_start
2025-07-22 19:53:42,844 - INFO - [phase_transition_integrity.py:293] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📸 DATA SNAPSHOT CREATED: Session fallback-********-a9d2-44b1-a778-78455bae1f78, Phase smart_diagnostic_start
2025-07-22 19:53:42,844 - WARNING - [phase_transition_integrity.py:342] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 19:53:42,845 - WARNING - [phase_transition_integrity.py:343] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 19:53:42,845 - WARNING - [phase_transition_integrity.py:344] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 19:53:42,846 - WARNING - [phase_transition_integrity.py:345] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 19:53:42,846 - WARNING - [phase_transition_integrity.py:347] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   - assigned_level_for_teaching value: None
2025-07-22 19:53:42,847 - WARNING - [phase_transition_integrity.py:348] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   - state_updates keys: ['new_phase', 'teaching_interactions', 'current_phase', 'unified_pathway_enforced']
2025-07-22 19:53:42,847 - WARNING - [phase_transition_integrity.py:349] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 19:53:42,848 - INFO - [phase_transition_integrity.py:153] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_start → teaching_start
2025-07-22 19:53:42,848 - ERROR - [phase_transition_integrity.py:196] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ❌ DATA REQUIREMENTS NOT MET: Data validation for phase 'teaching_start': 1 missing fields
2025-07-22 19:53:42,849 - ERROR - [phase_transition_integrity.py:389] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 TRANSITION RECOVERY: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 19:53:42,849 - DEBUG - [phase_transition_integrity.py:847] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 19:53:42,850 - DEBUG - [phase_transition_integrity.py:880] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 📝 TRANSITION RECORDED: smart_diagnostic_start → smart_diagnostic_start (invalid)
2025-07-22 19:53:42,850 - INFO - [phase_transition_integrity.py:406] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 19:53:42,850 - WARNING - [main.py:10336] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q1
2025-07-22 19:53:42,851 - WARNING - [main.py:10363] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 TRANSITION RECOVERED: Missing required data for phase teaching_start: ['assigned_level_for_teaching']
2025-07-22 19:53:42,851 - WARNING - [main.py:10372] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 19:53:42,851 - WARNING - [main.py:10373] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   1. Input phase: 'smart_diagnostic_start'
2025-07-22 19:53:42,852 - WARNING - [main.py:10374] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 19:53:42,852 - WARNING - [main.py:10375] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 19:53:42,852 - WARNING - [main.py:10376] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 19:53:42,853 - WARNING - [main.py:10377] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍   5. Final phase to save: 'smart_diagnostic_q1'
2025-07-22 19:53:42,853 - WARNING - [main.py:10380] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 💾 FINAL STATE APPLICATION:
2025-07-22 19:53:42,853 - WARNING - [main.py:10381] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 💾   - Current phase input: 'smart_diagnostic_start'
2025-07-22 19:53:42,854 - WARNING - [main.py:10382] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 💾   - Validated state updates: 27 fields
2025-07-22 19:53:42,854 - WARNING - [main.py:10383] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 💾   - Final phase to save: 'smart_diagnostic_q1'
2025-07-22 19:53:42,854 - WARNING - [main.py:10384] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 💾   - Phase change: True
2025-07-22 19:53:42,855 - WARNING - [main.py:10385] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 💾   - Integrity applied: True
2025-07-22 19:53:42,855 - INFO - [main.py:6397] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 19:53:42,855 - INFO - [main.py:6398] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Phase transition: smart_diagnostic_start -> smart_diagnostic_start
2025-07-22 19:53:42,855 - INFO - [main.py:6399] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Current level: 2
2025-07-22 19:53:42,855 - INFO - [main.py:6400] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Question index: 0
2025-07-22 19:53:42,855 - INFO - [main.py:6401] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   First encounter: True
2025-07-22 19:53:42,856 - INFO - [main.py:6406] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Answers collected: 0
2025-07-22 19:53:42,856 - INFO - [main.py:6407] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Levels failed: 0
2025-07-22 19:53:42,856 - INFO - [main.py:6365] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI state update validation passed: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 19:53:42,856 - INFO - [main.py:6411] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   State update valid: True
2025-07-22 19:53:42,857 - INFO - [main.py:6418] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   Diagnostic complete: False
2025-07-22 19:53:42,857 - WARNING - [main.py:10403] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 19:53:42,857 - INFO - [main.py:10412] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 DEBUG state_updates_from_ai teaching_interactions: 1
2025-07-22 19:53:42,858 - INFO - [main.py:10413] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 19:53:42,858 - WARNING - [main.py:10416] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ INTERACTION COUNT PROTECTION: AI tried to set 1, backend preserving 0
2025-07-22 19:53:42,858 - WARNING - [main.py:10417] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd]   This prevents teaching interaction count resets and infinite loops
2025-07-22 19:53:43,183 - WARNING - [main.py:10463] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 19:53:43,183 - WARNING - [main.py:10464] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅   - Phase: smart_diagnostic_q1
2025-07-22 19:53:43,184 - WARNING - [main.py:10465] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅   - Probing Level: 2
2025-07-22 19:53:43,184 - WARNING - [main.py:10466] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅   - Question Index: 0
2025-07-22 19:53:43,184 - WARNING - [main.py:10467] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅   - Diagnostic Complete: False
2025-07-22 19:53:43,185 - WARNING - [main.py:10474] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅   - Quiz Questions Saved: 0
2025-07-22 19:53:43,185 - WARNING - [main.py:10475] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅   - Quiz Answers Saved: 0
2025-07-22 19:53:43,186 - WARNING - [main.py:10476] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅   - Quiz Started: False
2025-07-22 19:53:43,186 - DEBUG - [main.py:10525] - 🔥 STATE SAVED - Session: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: smart_diagnostic_q1
2025-07-22 19:53:43,187 - DEBUG - [main.py:10526] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 19:53:44,009 - INFO - [main.py:10570] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Created new session document: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:53:44,010 - INFO - [main.py:18505] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 19:53:44,010 - DEBUG - [main.py:4943] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 19:53:44,011 - DEBUG - [main.py:10678] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] No final assessment data found in AI response
2025-07-22 19:53:44,011 - DEBUG - [main.py:10701] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 19:53:44,012 - DEBUG - [main.py:10725] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 19:53:44,012 - DEBUG - [main.py:10726] - 🔒   Current Phase: smart_diagnostic_start
2025-07-22 19:53:44,013 - DEBUG - [main.py:10727] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 19:53:44,013 - DEBUG - [main.py:10728] - 🔒   Diagnostic Complete: False
2025-07-22 19:53:44,014 - DEBUG - [main.py:10729] - 🔒   Assigned Level: None
2025-07-22 19:53:44,015 - INFO - [main.py:10802] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 19:53:44,015 - INFO - [main.py:10836] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 19:53:44,016 - INFO - [main.py:10844] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 19:53:44,016 - INFO - [main.py:10849] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 19:53:44,017 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:53:44,017 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:53:44,017 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:53:44,017 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:53:44,018 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:53:44,018 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:53:44,018 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:53:44,018 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:53:44,019 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:53:44,019 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:53:44,019 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:53:44,020 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:53:44,020 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:53:44,020 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:53:44,020 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:53:44,021 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:53:44,021 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:53:44,021 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:53:44,021 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:53:44,022 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:53:44,022 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:53:44,022 - INFO - [main.py:10858] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 19:53:44,022 - INFO - [intelligent_guardrails.py:130] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:53:44,023 - INFO - [intelligent_guardrails.py:183] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:53:44,023 - INFO - [main.py:10920] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 19:53:44,023 - INFO - [main.py:10948] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 19:53:44,023 - DEBUG - [main.py:11005] - 🎯 RESPONSE READY:
2025-07-22 19:53:44,024 - DEBUG - [main.py:11006] - 🎯   Session: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:53:44,024 - DEBUG - [main.py:11007] - 🎯   Phase: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 19:53:44,024 - DEBUG - [main.py:11008] - 🎯   Content: Hello Andrea! Welcome to your lesson on AI in Heal...
2025-07-22 19:53:44,024 - DEBUG - [main.py:11009] - 🎯   Request ID: f42c82b2-e85e-458f-9c52-f9f1a8178ffd
2025-07-22 19:53:44,025 - INFO - [main.py:11015] - [f42c82b2-e85e-458f-9c52-f9f1a8178ffd] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 19:53:44,025 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,025 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,025 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,026 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:44,026 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:44,026 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:44,026 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:44,026 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,026 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:53:44,027 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,027 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,027 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,027 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,027 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,027 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:53:44,028 - WARNING - [main.py:888] - High response time detected: 8.67s for enhance_content_api
2025-07-22 19:54:27,910 - INFO - [main.py:7143] - Incoming request: {"request_id": "9cc2b700-144a-4ed1-961d-5cd9d6bded0f", "timestamp": "2025-07-22T18:54:27.909516+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-********-a9d2-44b1-a778-78455bae1f78", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-22T18:53:44.075Z"}]}}
2025-07-22 19:54:27,911 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 19:54:27,911 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FojRArwf7TCv4y3JoZYASQTKKeemWG0Zxr2u8rLS_3HvNj0ZMYI-0G7Xbxx3Yhp6l5qhe14xG2DAhf0vXXcHgss9zySkSFdJqJhUCVL5I9WvN8l9G6LqHsOIOxXc3rKImNtmJ6U38HeqI3grXrFtqbS8AGZA8gVhKt97seDdqmUoIJxTvRa9B_CoDHpRgoPtma8eSC-2Z6xEepuhE0cmy8pqmRCRODvn7pgZ_TnOTK0AiA-OiH-PcV0sU9jLg2wdFacFrdCvYzgL_lTphao7Yrm-W1XQgBYZ4WRMLvIsy6AYZpEbXBcM2vfuQRBl3sqVAw2Ji5b4XU77_645ujHrDw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '1098', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 19:54:27,912 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 9cc2b700-144a-4ed1-961d-5cd9d6bded0f
2025-07-22 19:54:27,912 - INFO - [auth_decorator.py:74] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 19:54:27,912 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 19:54:27,913 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 19:54:27,913 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 19:54:27,913 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 19:54:27,913 - INFO - [auth_decorator.py:95] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f][require_auth] Development mode detected - bypassing authentication
2025-07-22 19:54:27,914 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 19:54:27,916 - INFO - [auth_decorator.py:121] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 19:54:28,274 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:54:28,274 - INFO - [auth_decorator.py:164] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:54:28,275 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 19:54:28,278 - INFO - [main.py:7327] -
================================================================================
2025-07-22 19:54:28,279 - WARNING - [main.py:7328] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 19:54:28,280 - WARNING - [main.py:7329] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 19:54:28,281 - DEBUG - [main.py:7337] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Console output error: I/O operation on closed file.
2025-07-22 19:54:28,287 - INFO - [main.py:8179] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making peo...
2025-07-22 19:54:28,288 - INFO - [main.py:8181] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-********-a9d2-44b1-a778-78455bae1f78', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-22T18:53:44.075Z'}]}
2025-07-22 19:54:28,289 - INFO - [main.py:8183] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍  - Session ID from payload: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:54:28,289 - INFO - [main.py:8184] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 19:54:28,289 - INFO - [main.py:8185] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 19:54:28,290 - DEBUG - [main.py:8223] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:54:28,290 - INFO - [main.py:8224] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:54:28,290 - INFO - [main.py:8264] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Level not provided, determined from grade 'primary-5': 5
2025-07-22 19:54:28,594 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 19:54:28,594 - INFO - [main.py:8281] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 19:54:28,594 - INFO - [main.py:8282] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 19:54:28,595 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 19:54:29,095 - INFO - [main.py:8312] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 19:54:29,096 - INFO - [main.py:8375] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 19:54:29,096 - INFO - [main.py:8500] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ Successfully retrieved lesson from primary path
2025-07-22 19:54:29,097 - INFO - [main.py:8511] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 19:54:29,097 - INFO - [main.py:8550] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 19:54:29,098 - INFO - [main.py:4456] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 19:54:29,436 - INFO - [main.py:4522] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 19:54:29,437 - INFO - [main.py:4522] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 19:54:29,437 - INFO - [main.py:4522] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 19:54:29,437 - INFO - [main.py:4522] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 19:54:29,437 - INFO - [main.py:4522] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 19:54:29,438 - INFO - [main.py:4591] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 19:54:29,438 - DEBUG - [main.py:4605] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 19:54:29,438 - DEBUG - [main.py:4608] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 19:54:29,438 - DEBUG - [main.py:4609] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 19:54:29,439 - DEBUG - [main.py:4610] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 19:54:29,439 - INFO - [main.py:4614] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Calling Gemini API for module inference...
2025-07-22 19:54:29,877 - INFO - [main.py:4624] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Gemini API call completed in 0.44s. Raw response: 'ai_tools_and_applications'
2025-07-22 19:54:29,877 - DEBUG - [main.py:4646] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 19:54:29,877 - INFO - [main.py:4651] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 19:54:29,878 - INFO - [main.py:8584] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 19:54:29,878 - INFO - [main.py:8621] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 19:54:30,153 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 19:54:30,669 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 19:54:30,669 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:54:30,669 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 19:54:30,670 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q1
2025-07-22 19:54:30,670 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 19:54:30,670 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 19:54:30,670 - WARNING - [main.py:8685] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 SESSION STATE DEBUG:
2025-07-22 19:54:30,670 - WARNING - [main.py:8686] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   - Session exists: True
2025-07-22 19:54:30,671 - WARNING - [main.py:8687] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   - Current phase: smart_diagnostic_q1
2025-07-22 19:54:30,671 - WARNING - [main.py:8688] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_phase', 'quiz_performance', 'diagnostic_completed_this_session', 'is_first_encounter_for_module', 'current_probing_level_number', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'teaching_interactions', 'quiz_started', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'quiz_answers', 'current_session_working_level']
2025-07-22 19:54:30,671 - DEBUG - [main.py:8706] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 19:54:30,672 - DEBUG - [main.py:8707] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Retrieved Phase: 'smart_diagnostic_q1'
2025-07-22 19:54:30,672 - DEBUG - [main.py:8708] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Diagnostic Completed: False
2025-07-22 19:54:30,673 - DEBUG - [main.py:8709] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Assigned Level: None
2025-07-22 19:54:30,673 - WARNING - [main.py:8710] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔒 STATE PROTECTION: phase='smart_diagnostic_q1', diagnostic_done=False, level=None
2025-07-22 19:54:30,674 - INFO - [main.py:8742] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 19:54:30,675 - INFO - [main.py:8743] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] State protection not triggered
2025-07-22 19:54:30,675 - INFO - [main.py:8791] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 19:54:30,676 - INFO - [main.py:8792] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   assigned_level_for_teaching (session): None
2025-07-22 19:54:30,676 - INFO - [main.py:8793] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   latest_assessed_level (profile): None
2025-07-22 19:54:30,677 - INFO - [main.py:8794] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   teaching_level_for_returning_student: None
2025-07-22 19:54:30,677 - INFO - [main.py:8795] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   has_completed_diagnostic_before: False
2025-07-22 19:54:30,677 - INFO - [main.py:8796] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   is_first_encounter_for_module: True
2025-07-22 19:54:30,678 - WARNING - [main.py:8801] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 19:54:30,678 - INFO - [main.py:8809] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 PHASE INVESTIGATION:
2025-07-22 19:54:30,678 - INFO - [main.py:8810] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Retrieved from Firestore: 'smart_diagnostic_q1'
2025-07-22 19:54:30,679 - INFO - [main.py:8811] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 19:54:30,679 - INFO - [main.py:8812] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Is first encounter: True
2025-07-22 19:54:30,679 - INFO - [main.py:8813] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Diagnostic completed: False
2025-07-22 19:54:30,679 - INFO - [main.py:8819] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ Using stored phase from Firestore: 'smart_diagnostic_q1'
2025-07-22 19:54:30,680 - INFO - [main.py:8833] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 19:54:30,680 - INFO - [main.py:8835] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Final phase for AI logic: smart_diagnostic_q1
2025-07-22 19:54:30,680 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 19:54:30,680 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 19:54:30,681 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 19:54:30,681 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 19:54:30,681 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 19:54:30,681 - INFO - [main.py:8855] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 19:54:30,683 - INFO - [main.py:6144] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Diagnostic context validation passed
2025-07-22 19:54:30,684 - WARNING - [main.py:9007] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q1' for first encounter
2025-07-22 19:54:30,684 - INFO - [main.py:9030] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q1
2025-07-22 19:54:30,685 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q1'
2025-07-22 19:54:30,685 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q1'
2025-07-22 19:54:30,686 - INFO - [main.py:9042] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Robust context prepared successfully. Phase: smart_diagnostic_q1
2025-07-22 19:54:30,686 - DEBUG - [main.py:9043] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 19:54:30,686 - INFO - [main.py:9285] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q1
2025-07-22 19:54:30,686 - INFO - [main.py:9286] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🎯 UNIFIED STATE MACHINE: Processing user query: Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurse...
2025-07-22 19:54:30,687 - WARNING - [main.py:9328] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ⚠️ Unknown phase 'smart_diagnostic_q1', enforcing unified pathway
2025-07-22 19:54:30,687 - WARNING - [main.py:9329] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ⚠️ Defaulting to teaching_start phase
2025-07-22 19:54:30,687 - INFO - [main.py:7513] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📚 TEACHING PHASE HANDLER: Processing teaching content
2025-07-22 19:54:30,688 - INFO - [main.py:7545] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🤖 AI INSTRUCTOR: Continuing teaching phase
2025-07-22 19:54:30,688 - INFO - [main.py:11163] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:54:30,688 - DEBUG - [main.py:11172] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Console output error: I/O operation on closed file.
2025-07-22 19:54:30,688 - INFO - [main.py:11205] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📤 Sending message to existing session: Hello! I'm ready for my lesson!  AI in healthcare ...
2025-07-22 19:54:30,689 - INFO - [main.py:5965] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 💰 Sending message to existing session (NO API CALL)
2025-07-22 19:54:31,496 - INFO - [main.py:5970] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ Response received from session (NO API CALL COST)
2025-07-22 19:54:31,497 - INFO - [main.py:11212] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📥 Received response from session: That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in hea...
2025-07-22 19:54:31,508 - INFO - [main.py:11303] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q1
2025-07-22 19:54:31,509 - INFO - [main.py:11361] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📝 Diagnostic in progress, checking for question progression
2025-07-22 19:54:31,509 - INFO - [main.py:11392] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-22 19:54:31,509 - INFO - [main.py:7573] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📊 TEACHING PROGRESS: 1 interactions
2025-07-22 19:54:31,510 - INFO - [main.py:7574] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📊 OBJECTIVES COVERAGE: 0.0%
2025-07-22 19:54:31,510 - INFO - [main.py:9342] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 19:54:31,510 - INFO - [main.py:5710] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔄 PHASE TRANSITION: smart_diagnostic_q1 → teaching_start
2025-07-22 19:54:31,511 - INFO - [phase_transition_integrity.py:153] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q1 → teaching_start
2025-07-22 19:54:31,511 - WARNING - [phase_transition_integrity.py:169] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ❌ INVALID TRANSITION: smart_diagnostic_q1 → teaching_start
2025-07-22 19:54:31,511 - ERROR - [main.py:5733] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ❌ INVALID TRANSITION: smart_diagnostic_q1 → teaching_start, using smart_diagnostic_q1
2025-07-22 19:54:31,511 - INFO - [main.py:9358] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q1 → smart_diagnostic_q1
2025-07-22 19:54:31,512 - INFO - [main.py:9359] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 19:54:32,305 - INFO - [main.py:9373] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ UNIFIED STATE MACHINE: Session state updated with phase smart_diagnostic_q1
2025-07-22 19:54:32,306 - INFO - [main.py:9383] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 19:54:32,306 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:54:32,306 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:54:32,306 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:54:32,306 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:54:32,307 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:54:32,307 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:54:32,307 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:54:32,307 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:54:32,308 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:54:32,308 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:54:32,308 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:54:32,308 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:54:32,308 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:54:32,308 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:54:32,308 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:54:32,309 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:54:32,309 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:54:32,310 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:54:32,310 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:54:32,310 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:54:32,311 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:54:32,311 - INFO - [intelligent_guardrails.py:130] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:54:32,312 - INFO - [intelligent_guardrails.py:183] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:54:32,312 - INFO - [intelligent_guardrails.py:675] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 19:54:32,312 - INFO - [main.py:9515] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 19:54:32,313 - WARNING - [main.py:9563] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🤖 AI RESPONSE RECEIVED:
2025-07-22 19:54:32,313 - WARNING - [main.py:9564] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🤖   - Content length: 464 chars
2025-07-22 19:54:32,313 - WARNING - [main.py:9565] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q2', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:54:32,313 - WARNING - [main.py:9566] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🤖   - Raw state block: None...
2025-07-22 19:54:32,313 - DEBUG - [main.py:10099] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 19:54:32,314 - DEBUG - [main.py:10100] - 🤖   Content: That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in hea...
2025-07-22 19:54:32,314 - DEBUG - [main.py:10101] - 🤖   State: {'new_phase': 'smart_diagnostic_q2', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:54:32,314 - INFO - [main.py:10127] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 19:54:32,315 - INFO - [main.py:10128] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q2, Session=smart_diagnostic_q1, Final=smart_diagnostic_q2
2025-07-22 19:54:32,612 - WARNING - [main.py:10230] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q1', new_phase='smart_diagnostic_q2'
2025-07-22 19:54:32,612 - INFO - [main.py:6365] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI state update validation passed: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-22 19:54:32,613 - WARNING - [main.py:10239] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 19:54:32,613 - WARNING - [main.py:10258] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔄 PHASE TRANSITION: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-22 19:54:32,613 - INFO - [main.py:10289] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 19:54:32,614 - INFO - [main.py:10294] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 19:54:32,614 - INFO - [phase_transition_integrity.py:330] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q1 → teaching_start
2025-07-22 19:54:32,614 - INFO - [phase_transition_integrity.py:293] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📸 DATA SNAPSHOT CREATED: Session fallback-********-a9d2-44b1-a778-78455bae1f78, Phase smart_diagnostic_q1
2025-07-22 19:54:32,615 - WARNING - [phase_transition_integrity.py:342] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 19:54:32,615 - WARNING - [phase_transition_integrity.py:343] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 19:54:32,616 - WARNING - [phase_transition_integrity.py:344] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 19:54:32,616 - WARNING - [phase_transition_integrity.py:345] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 19:54:32,617 - WARNING - [phase_transition_integrity.py:347] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   - assigned_level_for_teaching value: None
2025-07-22 19:54:32,617 - WARNING - [phase_transition_integrity.py:348] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   - state_updates keys: ['new_phase', 'teaching_interactions', 'current_phase', 'unified_pathway_enforced']
2025-07-22 19:54:32,618 - WARNING - [phase_transition_integrity.py:349] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 19:54:32,619 - INFO - [phase_transition_integrity.py:153] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q1 → teaching_start
2025-07-22 19:54:32,620 - WARNING - [phase_transition_integrity.py:169] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ❌ INVALID TRANSITION: smart_diagnostic_q1 → teaching_start
2025-07-22 19:54:32,620 - ERROR - [phase_transition_integrity.py:389] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔧 TRANSITION RECOVERY: smart_diagnostic_q1 → smart_diagnostic_start
2025-07-22 19:54:32,621 - DEBUG - [phase_transition_integrity.py:847] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 19:54:32,621 - DEBUG - [phase_transition_integrity.py:880] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 📝 TRANSITION RECORDED: smart_diagnostic_q1 → smart_diagnostic_start (invalid)
2025-07-22 19:54:32,622 - INFO - [phase_transition_integrity.py:406] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 19:54:32,622 - WARNING - [main.py:10336] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q2
2025-07-22 19:54:32,623 - WARNING - [main.py:10363] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔧 TRANSITION RECOVERED: Invalid transition: smart_diagnostic_q1 → teaching_start not allowed
2025-07-22 19:54:32,623 - WARNING - [main.py:10372] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 19:54:32,623 - WARNING - [main.py:10373] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   1. Input phase: 'smart_diagnostic_q1'
2025-07-22 19:54:32,624 - WARNING - [main.py:10374] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 19:54:32,624 - WARNING - [main.py:10375] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 19:54:32,624 - WARNING - [main.py:10376] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 19:54:32,625 - WARNING - [main.py:10377] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍   5. Final phase to save: 'smart_diagnostic_q2'
2025-07-22 19:54:32,625 - WARNING - [main.py:10380] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 💾 FINAL STATE APPLICATION:
2025-07-22 19:54:32,625 - WARNING - [main.py:10381] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 💾   - Current phase input: 'smart_diagnostic_q1'
2025-07-22 19:54:32,626 - WARNING - [main.py:10382] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 💾   - Validated state updates: 27 fields
2025-07-22 19:54:32,626 - WARNING - [main.py:10383] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 💾   - Final phase to save: 'smart_diagnostic_q2'
2025-07-22 19:54:32,626 - WARNING - [main.py:10384] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 💾   - Phase change: True
2025-07-22 19:54:32,627 - WARNING - [main.py:10385] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 💾   - Integrity applied: True
2025-07-22 19:54:32,627 - INFO - [main.py:6397] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 19:54:32,627 - INFO - [main.py:6398] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Phase transition: smart_diagnostic_q1 -> smart_diagnostic_start
2025-07-22 19:54:32,628 - INFO - [main.py:6399] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Current level: 2
2025-07-22 19:54:32,628 - INFO - [main.py:6400] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Question index: 0
2025-07-22 19:54:32,628 - INFO - [main.py:6401] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   First encounter: True
2025-07-22 19:54:32,628 - INFO - [main.py:6406] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Answers collected: 0
2025-07-22 19:54:32,629 - INFO - [main.py:6407] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Levels failed: 0
2025-07-22 19:54:32,629 - INFO - [main.py:6365] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI state update validation passed: smart_diagnostic_q1 → smart_diagnostic_start
2025-07-22 19:54:32,629 - INFO - [main.py:6411] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   State update valid: True
2025-07-22 19:54:32,629 - INFO - [main.py:6418] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   Diagnostic complete: False
2025-07-22 19:54:32,630 - WARNING - [main.py:10403] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 19:54:32,630 - INFO - [main.py:10412] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 DEBUG state_updates_from_ai teaching_interactions: 1
2025-07-22 19:54:32,630 - INFO - [main.py:10413] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 19:54:32,631 - WARNING - [main.py:10416] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ INTERACTION COUNT PROTECTION: AI tried to set 1, backend preserving 0
2025-07-22 19:54:32,631 - WARNING - [main.py:10417] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f]   This prevents teaching interaction count resets and infinite loops
2025-07-22 19:54:33,144 - WARNING - [main.py:10463] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 19:54:33,144 - WARNING - [main.py:10464] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅   - Phase: smart_diagnostic_q2
2025-07-22 19:54:33,145 - WARNING - [main.py:10465] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅   - Probing Level: 2
2025-07-22 19:54:33,145 - WARNING - [main.py:10466] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅   - Question Index: 0
2025-07-22 19:54:33,145 - WARNING - [main.py:10467] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅   - Diagnostic Complete: False
2025-07-22 19:54:33,145 - WARNING - [main.py:10474] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅   - Quiz Questions Saved: 0
2025-07-22 19:54:33,146 - WARNING - [main.py:10475] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅   - Quiz Answers Saved: 0
2025-07-22 19:54:33,146 - WARNING - [main.py:10476] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅   - Quiz Started: False
2025-07-22 19:54:33,146 - DEBUG - [main.py:10525] - 🔥 STATE SAVED - Session: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: smart_diagnostic_q2
2025-07-22 19:54:33,146 - DEBUG - [main.py:10526] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 19:54:33,958 - DEBUG - [main.py:10584] - ✅ SESSION UPDATED - ID: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: smart_diagnostic_q2
2025-07-22 19:54:33,975 - DEBUG - [main.py:10585] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-22 19:54:33,977 - INFO - [main.py:10591] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ Updated existing session document: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:54:33,980 - INFO - [main.py:18505] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 19:54:33,980 - DEBUG - [main.py:4943] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 19:54:33,980 - DEBUG - [main.py:10678] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] No final assessment data found in AI response
2025-07-22 19:54:33,981 - DEBUG - [main.py:10701] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 19:54:33,981 - DEBUG - [main.py:10725] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 19:54:33,986 - DEBUG - [main.py:10726] - 🔒   Current Phase: smart_diagnostic_q1
2025-07-22 19:54:34,011 - DEBUG - [main.py:10727] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 19:54:34,013 - DEBUG - [main.py:10728] - 🔒   Diagnostic Complete: False
2025-07-22 19:54:34,023 - DEBUG - [main.py:10729] - 🔒   Assigned Level: None
2025-07-22 19:54:34,035 - INFO - [main.py:10802] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 19:54:34,036 - INFO - [main.py:10836] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 19:54:34,036 - INFO - [main.py:10844] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 19:54:34,037 - INFO - [main.py:10849] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 19:54:34,037 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:54:34,038 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:54:34,038 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:54:34,038 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:54:34,039 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:54:34,039 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:54:34,040 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:54:34,040 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:54:34,040 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:54:34,040 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:54:34,040 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:54:34,041 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:54:34,041 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:54:34,041 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:54:34,041 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:54:34,042 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:54:34,042 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:54:34,042 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:54:34,043 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:54:34,043 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:54:34,043 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:54:34,044 - INFO - [main.py:10858] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 19:54:34,044 - INFO - [intelligent_guardrails.py:130] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:54:34,044 - INFO - [intelligent_guardrails.py:183] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:54:34,045 - INFO - [main.py:10920] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 19:54:34,045 - INFO - [main.py:10948] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 19:54:34,046 - DEBUG - [main.py:11005] - 🎯 RESPONSE READY:
2025-07-22 19:54:34,046 - DEBUG - [main.py:11006] - 🎯   Session: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:54:34,046 - DEBUG - [main.py:11007] - 🎯   Phase: smart_diagnostic_q1 → smart_diagnostic_start
2025-07-22 19:54:34,047 - DEBUG - [main.py:11008] - 🎯   Content: That's a fantastic start, Andrea! You've already g...
2025-07-22 19:54:34,047 - DEBUG - [main.py:11009] - 🎯   Request ID: 9cc2b700-144a-4ed1-961d-5cd9d6bded0f
2025-07-22 19:54:34,047 - INFO - [main.py:11015] - [9cc2b700-144a-4ed1-961d-5cd9d6bded0f] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 19:54:34,047 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,048 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,048 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,049 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:54:34,052 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:54:34,053 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:54:34,053 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:54:34,053 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,053 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:54:34,054 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,054 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,054 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,054 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,055 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,055 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:54:34,056 - WARNING - [main.py:888] - High response time detected: 5.78s for enhance_content_api
2025-07-22 19:55:03,208 - INFO - [main.py:7143] - Incoming request: {"request_id": "b633f1de-8346-4176-90d3-f69e5ea7b63a", "timestamp": "2025-07-22T18:55:03.208195+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-********-a9d2-44b1-a778-78455bae1f78", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-22T18:53:44.075Z"}, {"role": "user", "content": "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", "timestamp": "2025-07-22T18:54:27.704Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in healthcare, like how it helps doctors find sicknesses and choose medicines. It's like you're saying AI is a super-tool that makes healthcare even better!\n\nNow, let's move on to the next question.\n\n**Question 2 of 5 (Level 3 Concept Exploration):** Can you think of any specific ways AI might help doctors figure out what's wrong with someone if they are feeling sick?", "timestamp": "2025-07-22T18:54:34.097Z"}]}}
2025-07-22 19:55:03,209 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 19:55:03,210 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FojRArwf7TCv4y3JoZYASQTKKeemWG0Zxr2u8rLS_3HvNj0ZMYI-0G7Xbxx3Yhp6l5qhe14xG2DAhf0vXXcHgss9zySkSFdJqJhUCVL5I9WvN8l9G6LqHsOIOxXc3rKImNtmJ6U38HeqI3grXrFtqbS8AGZA8gVhKt97seDdqmUoIJxTvRa9B_CoDHpRgoPtma8eSC-2Z6xEepuhE0cmy8pqmRCRODvn7pgZ_TnOTK0AiA-OiH-PcV0sU9jLg2wdFacFrdCvYzgL_lTphao7Yrm-W1XQgBYZ4WRMLvIsy6AYZpEbXBcM2vfuQRBl3sqVAw2Ji5b4XU77_645ujHrDw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '2379', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 19:55:03,210 - INFO - [auth_decorator.py:70] - 🔒 Request ID: b633f1de-8346-4176-90d3-f69e5ea7b63a
2025-07-22 19:55:03,210 - INFO - [auth_decorator.py:74] - [b633f1de-8346-4176-90d3-f69e5ea7b63a][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 19:55:03,210 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 19:55:03,211 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 19:55:03,211 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 19:55:03,211 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 19:55:03,211 - INFO - [auth_decorator.py:95] - [b633f1de-8346-4176-90d3-f69e5ea7b63a][require_auth] Development mode detected - bypassing authentication
2025-07-22 19:55:03,211 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 19:55:03,211 - INFO - [auth_decorator.py:121] - [b633f1de-8346-4176-90d3-f69e5ea7b63a][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 19:55:03,554 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:55:03,554 - INFO - [auth_decorator.py:164] - [b633f1de-8346-4176-90d3-f69e5ea7b63a][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:55:03,555 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 19:55:03,555 - INFO - [main.py:7327] -
================================================================================
2025-07-22 19:55:03,556 - WARNING - [main.py:7328] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 19:55:03,556 - WARNING - [main.py:7329] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 19:55:03,556 - DEBUG - [main.py:7337] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Console output error: I/O operation on closed file.
2025-07-22 19:55:03,557 - INFO - [main.py:8179] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, ...
2025-07-22 19:55:03,557 - INFO - [main.py:8181] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-********-a9d2-44b1-a778-78455bae1f78', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-22T18:53:44.075Z'}, {'role': 'user', 'content': "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", 'timestamp': '2025-07-22T18:54:27.704Z'}, {'role': 'assistant', 'content': "That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in healthcare, like how it helps doctors find sicknesses and choose medicines. It's like you're saying AI is a super-tool that makes healthcare even better!\n\nNow, let's move on to the next question.\n\n**Question 2 of 5 (Level 3 Concept Exploration):** Can you think of any specific ways AI might help doctors figure out what's wrong with someone if they are feeling sick?", 'timestamp': '2025-07-22T18:54:34.097Z'}]}
2025-07-22 19:55:03,558 - INFO - [main.py:8183] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍  - Session ID from payload: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:55:03,559 - INFO - [main.py:8184] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 19:55:03,559 - INFO - [main.py:8185] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 19:55:03,560 - DEBUG - [main.py:8223] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:55:03,561 - INFO - [main.py:8224] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:55:03,561 - INFO - [main.py:8264] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Level not provided, determined from grade 'primary-5': 5
2025-07-22 19:55:03,917 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 19:55:03,918 - INFO - [main.py:8281] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 19:55:03,919 - INFO - [main.py:8282] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 19:55:03,920 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 19:55:04,409 - INFO - [main.py:8312] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 19:55:04,409 - INFO - [main.py:8375] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 19:55:04,410 - INFO - [main.py:8500] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ Successfully retrieved lesson from primary path
2025-07-22 19:55:04,410 - INFO - [main.py:8511] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 19:55:04,410 - INFO - [main.py:8550] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 19:55:04,411 - INFO - [main.py:4456] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 19:55:04,776 - INFO - [main.py:4522] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 19:55:04,777 - INFO - [main.py:4522] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 19:55:04,777 - INFO - [main.py:4522] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 19:55:04,778 - INFO - [main.py:4522] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 19:55:04,778 - INFO - [main.py:4522] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 19:55:04,779 - INFO - [main.py:4591] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 19:55:04,779 - DEBUG - [main.py:4605] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 19:55:04,779 - DEBUG - [main.py:4608] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 19:55:04,779 - DEBUG - [main.py:4609] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 19:55:04,780 - DEBUG - [main.py:4610] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 19:55:04,780 - INFO - [main.py:4614] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Calling Gemini API for module inference...
2025-07-22 19:55:05,376 - INFO - [main.py:4624] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Gemini API call completed in 0.60s. Raw response: 'ai_tools_and_applications'
2025-07-22 19:55:05,376 - DEBUG - [main.py:4646] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 19:55:05,376 - INFO - [main.py:4651] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 19:55:05,377 - INFO - [main.py:8584] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 19:55:05,377 - INFO - [main.py:8621] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 19:55:05,712 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 19:55:06,222 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 19:55:06,223 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:55:06,223 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 19:55:06,223 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q2
2025-07-22 19:55:06,223 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 19:55:06,223 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 19:55:06,224 - WARNING - [main.py:8685] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 SESSION STATE DEBUG:
2025-07-22 19:55:06,224 - WARNING - [main.py:8686] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   - Session exists: True
2025-07-22 19:55:06,224 - WARNING - [main.py:8687] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   - Current phase: smart_diagnostic_q2
2025-07-22 19:55:06,225 - WARNING - [main.py:8688] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'new_phase', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 19:55:06,225 - DEBUG - [main.py:8706] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 19:55:06,226 - DEBUG - [main.py:8707] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Retrieved Phase: 'smart_diagnostic_q2'
2025-07-22 19:55:06,226 - DEBUG - [main.py:8708] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Diagnostic Completed: False
2025-07-22 19:55:06,227 - DEBUG - [main.py:8709] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Assigned Level: None
2025-07-22 19:55:06,227 - WARNING - [main.py:8710] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔒 STATE PROTECTION: phase='smart_diagnostic_q2', diagnostic_done=False, level=None
2025-07-22 19:55:06,227 - INFO - [main.py:8742] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 19:55:06,228 - INFO - [main.py:8743] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] State protection not triggered
2025-07-22 19:55:06,228 - INFO - [main.py:8791] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 19:55:06,229 - INFO - [main.py:8792] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   assigned_level_for_teaching (session): None
2025-07-22 19:55:06,229 - INFO - [main.py:8793] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   latest_assessed_level (profile): None
2025-07-22 19:55:06,230 - INFO - [main.py:8794] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   teaching_level_for_returning_student: None
2025-07-22 19:55:06,230 - INFO - [main.py:8795] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   has_completed_diagnostic_before: False
2025-07-22 19:55:06,231 - INFO - [main.py:8796] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   is_first_encounter_for_module: True
2025-07-22 19:55:06,232 - WARNING - [main.py:8801] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 19:55:06,232 - INFO - [main.py:8809] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 PHASE INVESTIGATION:
2025-07-22 19:55:06,232 - INFO - [main.py:8810] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Retrieved from Firestore: 'smart_diagnostic_q2'
2025-07-22 19:55:06,232 - INFO - [main.py:8811] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 19:55:06,233 - INFO - [main.py:8812] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Is first encounter: True
2025-07-22 19:55:06,233 - INFO - [main.py:8813] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Diagnostic completed: False
2025-07-22 19:55:06,234 - INFO - [main.py:8819] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ Using stored phase from Firestore: 'smart_diagnostic_q2'
2025-07-22 19:55:06,234 - INFO - [main.py:8833] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 19:55:06,235 - INFO - [main.py:8835] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Final phase for AI logic: smart_diagnostic_q2
2025-07-22 19:55:06,235 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 19:55:06,235 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 19:55:06,236 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 19:55:06,237 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 19:55:06,238 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 19:55:06,238 - INFO - [main.py:8855] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 19:55:06,238 - INFO - [main.py:6144] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Diagnostic context validation passed
2025-07-22 19:55:06,238 - WARNING - [main.py:9007] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q2' for first encounter
2025-07-22 19:55:06,239 - INFO - [main.py:9030] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q2
2025-07-22 19:55:06,239 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q2'
2025-07-22 19:55:06,239 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q2'
2025-07-22 19:55:06,239 - INFO - [main.py:9042] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Robust context prepared successfully. Phase: smart_diagnostic_q2
2025-07-22 19:55:06,240 - DEBUG - [main.py:9043] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 19:55:06,240 - INFO - [main.py:9285] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q2
2025-07-22 19:55:06,240 - INFO - [main.py:9286] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🎯 UNIFIED STATE MACHINE: Processing user query: Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at specia...
2025-07-22 19:55:06,240 - WARNING - [main.py:9328] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ⚠️ Unknown phase 'smart_diagnostic_q2', enforcing unified pathway
2025-07-22 19:55:06,241 - WARNING - [main.py:9329] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ⚠️ Defaulting to teaching_start phase
2025-07-22 19:55:06,241 - INFO - [main.py:7513] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📚 TEACHING PHASE HANDLER: Processing teaching content
2025-07-22 19:55:06,241 - INFO - [main.py:7545] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🤖 AI INSTRUCTOR: Continuing teaching phase
2025-07-22 19:55:06,242 - INFO - [main.py:11163] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:55:06,242 - DEBUG - [main.py:11172] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Console output error: I/O operation on closed file.
2025-07-22 19:55:06,242 - INFO - [main.py:11205] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📤 Sending message to existing session: Absolutely! AI can be like a super detective for d...
2025-07-22 19:55:06,242 - INFO - [main.py:5965] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 💰 Sending message to existing session (NO API CALL)
2025-07-22 19:55:07,213 - INFO - [main.py:5970] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ Response received from session (NO API CALL COST)
2025-07-22 19:55:07,213 - INFO - [main.py:11212] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📥 Received response from session: That's a brilliant way to put it, Andrea! "Super detective" and "super-vision" are perfect descripti...
2025-07-22 19:55:07,214 - INFO - [main.py:11303] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q2
2025-07-22 19:55:07,214 - INFO - [main.py:11361] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📝 Diagnostic in progress, checking for question progression
2025-07-22 19:55:07,214 - INFO - [main.py:11392] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-22 19:55:07,215 - INFO - [main.py:7573] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📊 TEACHING PROGRESS: 1 interactions
2025-07-22 19:55:07,215 - INFO - [main.py:7574] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📊 OBJECTIVES COVERAGE: 0.0%
2025-07-22 19:55:07,215 - INFO - [main.py:9342] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 19:55:07,216 - INFO - [main.py:5710] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔄 PHASE TRANSITION: smart_diagnostic_q2 → teaching_start
2025-07-22 19:55:07,216 - INFO - [phase_transition_integrity.py:153] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q2 → teaching_start
2025-07-22 19:55:07,216 - WARNING - [phase_transition_integrity.py:169] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ❌ INVALID TRANSITION: smart_diagnostic_q2 → teaching_start
2025-07-22 19:55:07,217 - ERROR - [main.py:5733] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ❌ INVALID TRANSITION: smart_diagnostic_q2 → teaching_start, using smart_diagnostic_q2
2025-07-22 19:55:07,217 - INFO - [main.py:9358] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q2 → smart_diagnostic_q2
2025-07-22 19:55:07,218 - INFO - [main.py:9359] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 19:55:08,056 - INFO - [main.py:9373] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ UNIFIED STATE MACHINE: Session state updated with phase smart_diagnostic_q2
2025-07-22 19:55:08,056 - INFO - [main.py:9383] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 19:55:08,057 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:55:08,057 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:55:08,057 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:55:08,057 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:55:08,058 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:55:08,058 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:55:08,058 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:55:08,058 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:55:08,058 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:55:08,059 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:55:08,059 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:55:08,060 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:55:08,060 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:55:08,061 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:55:08,061 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:55:08,062 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:55:08,062 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:55:08,063 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:55:08,063 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:55:08,063 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:55:08,063 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:55:08,064 - INFO - [intelligent_guardrails.py:130] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:55:08,064 - INFO - [intelligent_guardrails.py:183] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:55:08,064 - INFO - [intelligent_guardrails.py:675] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 19:55:08,065 - INFO - [main.py:9515] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 19:55:08,065 - WARNING - [main.py:9563] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🤖 AI RESPONSE RECEIVED:
2025-07-22 19:55:08,065 - WARNING - [main.py:9564] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🤖   - Content length: 533 chars
2025-07-22 19:55:08,066 - WARNING - [main.py:9565] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q3', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:55:08,067 - WARNING - [main.py:9566] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🤖   - Raw state block: None...
2025-07-22 19:55:08,067 - DEBUG - [main.py:10099] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 19:55:08,068 - DEBUG - [main.py:10100] - 🤖   Content: That's a brilliant way to put it, Andrea! "Super detective" and "super-vision" are perfect descripti...
2025-07-22 19:55:08,068 - DEBUG - [main.py:10101] - 🤖   State: {'new_phase': 'smart_diagnostic_q3', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:55:08,069 - INFO - [main.py:10127] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 19:55:08,070 - INFO - [main.py:10128] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q3, Session=smart_diagnostic_q2, Final=smart_diagnostic_q3
2025-07-22 19:55:08,404 - WARNING - [main.py:10230] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q2', new_phase='smart_diagnostic_q3'
2025-07-22 19:55:08,404 - INFO - [main.py:6365] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI state update validation passed: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-22 19:55:08,405 - WARNING - [main.py:10239] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 19:55:08,405 - WARNING - [main.py:10258] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔄 PHASE TRANSITION: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-22 19:55:08,406 - INFO - [main.py:10289] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 19:55:08,406 - INFO - [main.py:10294] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 19:55:08,406 - INFO - [phase_transition_integrity.py:330] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q2 → teaching_start
2025-07-22 19:55:08,407 - INFO - [phase_transition_integrity.py:293] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📸 DATA SNAPSHOT CREATED: Session fallback-********-a9d2-44b1-a778-78455bae1f78, Phase smart_diagnostic_q2
2025-07-22 19:55:08,407 - WARNING - [phase_transition_integrity.py:342] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 19:55:08,407 - WARNING - [phase_transition_integrity.py:343] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 19:55:08,407 - WARNING - [phase_transition_integrity.py:344] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 19:55:08,408 - WARNING - [phase_transition_integrity.py:345] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 19:55:08,408 - WARNING - [phase_transition_integrity.py:347] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   - assigned_level_for_teaching value: None
2025-07-22 19:55:08,408 - WARNING - [phase_transition_integrity.py:348] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   - state_updates keys: ['new_phase', 'teaching_interactions', 'current_phase', 'unified_pathway_enforced']
2025-07-22 19:55:08,409 - WARNING - [phase_transition_integrity.py:349] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 19:55:08,409 - INFO - [phase_transition_integrity.py:153] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q2 → teaching_start
2025-07-22 19:55:08,410 - WARNING - [phase_transition_integrity.py:169] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ❌ INVALID TRANSITION: smart_diagnostic_q2 → teaching_start
2025-07-22 19:55:08,410 - ERROR - [phase_transition_integrity.py:389] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔧 TRANSITION RECOVERY: smart_diagnostic_q2 → smart_diagnostic_start
2025-07-22 19:55:08,410 - DEBUG - [phase_transition_integrity.py:847] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 19:55:08,410 - DEBUG - [phase_transition_integrity.py:880] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 📝 TRANSITION RECORDED: smart_diagnostic_q2 → smart_diagnostic_start (invalid)
2025-07-22 19:55:08,411 - INFO - [phase_transition_integrity.py:406] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 19:55:08,411 - WARNING - [main.py:10336] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q3
2025-07-22 19:55:08,412 - WARNING - [main.py:10363] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔧 TRANSITION RECOVERED: Invalid transition: smart_diagnostic_q2 → teaching_start not allowed
2025-07-22 19:55:08,412 - WARNING - [main.py:10372] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 19:55:08,413 - WARNING - [main.py:10373] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   1. Input phase: 'smart_diagnostic_q2'
2025-07-22 19:55:08,414 - WARNING - [main.py:10374] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 19:55:08,414 - WARNING - [main.py:10375] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 19:55:08,414 - WARNING - [main.py:10376] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 19:55:08,415 - WARNING - [main.py:10377] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍   5. Final phase to save: 'smart_diagnostic_q3'
2025-07-22 19:55:08,415 - WARNING - [main.py:10380] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 💾 FINAL STATE APPLICATION:
2025-07-22 19:55:08,416 - WARNING - [main.py:10381] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 💾   - Current phase input: 'smart_diagnostic_q2'
2025-07-22 19:55:08,417 - WARNING - [main.py:10382] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 💾   - Validated state updates: 27 fields
2025-07-22 19:55:08,417 - WARNING - [main.py:10383] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 💾   - Final phase to save: 'smart_diagnostic_q3'
2025-07-22 19:55:08,418 - WARNING - [main.py:10384] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 💾   - Phase change: True
2025-07-22 19:55:08,418 - WARNING - [main.py:10385] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 💾   - Integrity applied: True
2025-07-22 19:55:08,419 - INFO - [main.py:6397] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 19:55:08,419 - INFO - [main.py:6398] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Phase transition: smart_diagnostic_q2 -> smart_diagnostic_start
2025-07-22 19:55:08,420 - INFO - [main.py:6399] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Current level: 2
2025-07-22 19:55:08,420 - INFO - [main.py:6400] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Question index: 0
2025-07-22 19:55:08,421 - INFO - [main.py:6401] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   First encounter: True
2025-07-22 19:55:08,421 - INFO - [main.py:6406] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Answers collected: 0
2025-07-22 19:55:08,421 - INFO - [main.py:6407] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Levels failed: 0
2025-07-22 19:55:08,421 - INFO - [main.py:6365] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI state update validation passed: smart_diagnostic_q2 → smart_diagnostic_start
2025-07-22 19:55:08,422 - INFO - [main.py:6411] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   State update valid: True
2025-07-22 19:55:08,422 - INFO - [main.py:6418] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   Diagnostic complete: False
2025-07-22 19:55:08,422 - WARNING - [main.py:10403] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 19:55:08,423 - INFO - [main.py:10412] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 DEBUG state_updates_from_ai teaching_interactions: 1
2025-07-22 19:55:08,423 - INFO - [main.py:10413] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 19:55:08,424 - WARNING - [main.py:10416] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ INTERACTION COUNT PROTECTION: AI tried to set 1, backend preserving 0
2025-07-22 19:55:08,424 - WARNING - [main.py:10417] - [b633f1de-8346-4176-90d3-f69e5ea7b63a]   This prevents teaching interaction count resets and infinite loops
2025-07-22 19:55:08,920 - WARNING - [main.py:10463] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 19:55:08,921 - WARNING - [main.py:10464] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅   - Phase: smart_diagnostic_q3
2025-07-22 19:55:08,921 - WARNING - [main.py:10465] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅   - Probing Level: 2
2025-07-22 19:55:08,921 - WARNING - [main.py:10466] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅   - Question Index: 0
2025-07-22 19:55:08,922 - WARNING - [main.py:10467] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅   - Diagnostic Complete: False
2025-07-22 19:55:08,922 - WARNING - [main.py:10474] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅   - Quiz Questions Saved: 0
2025-07-22 19:55:08,922 - WARNING - [main.py:10475] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅   - Quiz Answers Saved: 0
2025-07-22 19:55:08,922 - WARNING - [main.py:10476] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅   - Quiz Started: False
2025-07-22 19:55:08,923 - DEBUG - [main.py:10525] - 🔥 STATE SAVED - Session: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: smart_diagnostic_q3
2025-07-22 19:55:08,923 - DEBUG - [main.py:10526] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 19:55:09,780 - DEBUG - [main.py:10584] - ✅ SESSION UPDATED - ID: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: smart_diagnostic_q3
2025-07-22 19:55:09,781 - DEBUG - [main.py:10585] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-22 19:55:09,781 - INFO - [main.py:10591] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ Updated existing session document: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:55:09,782 - INFO - [main.py:18505] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 19:55:09,782 - DEBUG - [main.py:4943] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 19:55:09,782 - DEBUG - [main.py:10678] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] No final assessment data found in AI response
2025-07-22 19:55:09,783 - DEBUG - [main.py:10701] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 19:55:09,783 - DEBUG - [main.py:10725] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 19:55:09,783 - DEBUG - [main.py:10726] - 🔒   Current Phase: smart_diagnostic_q2
2025-07-22 19:55:09,784 - DEBUG - [main.py:10727] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 19:55:09,785 - DEBUG - [main.py:10728] - 🔒   Diagnostic Complete: False
2025-07-22 19:55:09,785 - DEBUG - [main.py:10729] - 🔒   Assigned Level: None
2025-07-22 19:55:09,786 - INFO - [main.py:10802] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 19:55:09,787 - INFO - [main.py:10836] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 19:55:09,788 - INFO - [main.py:10844] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 19:55:09,788 - INFO - [main.py:10849] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 19:55:09,788 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:55:09,788 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:55:09,789 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:55:09,789 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:55:09,789 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:55:09,790 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:55:09,790 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:55:09,791 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:55:09,791 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:55:09,791 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:55:09,792 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:55:09,792 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:55:09,792 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:55:09,792 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:55:09,793 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:55:09,793 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:55:09,793 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:55:09,793 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:55:09,793 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:55:09,793 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:55:09,794 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:55:09,794 - INFO - [main.py:10858] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 19:55:09,794 - INFO - [intelligent_guardrails.py:130] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:55:09,794 - INFO - [intelligent_guardrails.py:183] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:55:09,795 - INFO - [main.py:10920] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 19:55:09,795 - INFO - [main.py:10948] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 19:55:09,795 - DEBUG - [main.py:11005] - 🎯 RESPONSE READY:
2025-07-22 19:55:09,795 - DEBUG - [main.py:11006] - 🎯   Session: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:55:09,796 - DEBUG - [main.py:11007] - 🎯   Phase: smart_diagnostic_q2 → smart_diagnostic_start
2025-07-22 19:55:09,796 - DEBUG - [main.py:11008] - 🎯   Content: That's a brilliant way to put it, Andrea! "Super d...
2025-07-22 19:55:09,796 - DEBUG - [main.py:11009] - 🎯   Request ID: b633f1de-8346-4176-90d3-f69e5ea7b63a
2025-07-22 19:55:09,796 - INFO - [main.py:11015] - [b633f1de-8346-4176-90d3-f69e5ea7b63a] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 19:55:09,797 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,797 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,797 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,797 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:55:09,797 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:55:09,797 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:55:09,797 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:55:09,798 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,798 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:55:09,798 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,798 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,798 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,798 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,799 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,799 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:55:09,802 - WARNING - [main.py:888] - High response time detected: 6.25s for enhance_content_api
2025-07-22 19:56:27,143 - INFO - [main.py:7143] - Incoming request: {"request_id": "cac2061a-8a71-4bad-b867-e02a5bf8ee96", "timestamp": "2025-07-22T18:56:27.139536+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-********-a9d2-44b1-a778-78455bae1f78", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-22T18:53:44.075Z"}, {"role": "user", "content": "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", "timestamp": "2025-07-22T18:54:27.704Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in healthcare, like how it helps doctors find sicknesses and choose medicines. It's like you're saying AI is a super-tool that makes healthcare even better!\n\nNow, let's move on to the next question.\n\n**Question 2 of 5 (Level 3 Concept Exploration):** Can you think of any specific ways AI might help doctors figure out what's wrong with someone if they are feeling sick?", "timestamp": "2025-07-22T18:54:34.097Z"}, {"role": "user", "content": "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", "timestamp": "2025-07-22T18:55:02.823Z"}, {"role": "assistant", "content": "That's a brilliant way to put it, Andrea! \"Super detective\" and \"super-vision\" are perfect descriptions. You've nailed the idea of AI helping to spot things in medical images and by looking at lots of past patient information. It's exactly like a detective gathering clues to solve a case!\n\nLet's keep that detective spirit going for our next question.\n\n**Question 3 of 5 (Level 4 Application Scenario):** Imagine a doctor has a new patient who has a cough and a fever. How might AI help the doctor decide what medicine to give them?", "timestamp": "2025-07-22T18:55:09.815Z"}]}}
2025-07-22 19:56:27,147 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 19:56:27,149 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FojRArwf7TCv4y3JoZYASQTKKeemWG0Zxr2u8rLS_3HvNj0ZMYI-0G7Xbxx3Yhp6l5qhe14xG2DAhf0vXXcHgss9zySkSFdJqJhUCVL5I9WvN8l9G6LqHsOIOxXc3rKImNtmJ6U38HeqI3grXrFtqbS8AGZA8gVhKt97seDdqmUoIJxTvRa9B_CoDHpRgoPtma8eSC-2Z6xEepuhE0cmy8pqmRCRODvn7pgZ_TnOTK0AiA-OiH-PcV0sU9jLg2wdFacFrdCvYzgL_lTphao7Yrm-W1XQgBYZ4WRMLvIsy6AYZpEbXBcM2vfuQRBl3sqVAw2Ji5b4XU77_645ujHrDw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '3801', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 19:56:27,150 - INFO - [auth_decorator.py:70] - 🔒 Request ID: cac2061a-8a71-4bad-b867-e02a5bf8ee96
2025-07-22 19:56:27,151 - INFO - [auth_decorator.py:74] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 19:56:27,152 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 19:56:27,152 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 19:56:27,153 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 19:56:27,153 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 19:56:27,153 - INFO - [auth_decorator.py:95] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96][require_auth] Development mode detected - bypassing authentication
2025-07-22 19:56:27,154 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 19:56:27,154 - INFO - [auth_decorator.py:121] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 19:56:27,462 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:56:27,462 - INFO - [auth_decorator.py:164] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:56:27,464 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 19:56:27,470 - INFO - [main.py:7327] -
================================================================================
2025-07-22 19:56:27,471 - WARNING - [main.py:7328] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 19:56:27,472 - WARNING - [main.py:7329] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 19:56:27,472 - DEBUG - [main.py:7337] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Console output error: I/O operation on closed file.
2025-07-22 19:56:27,474 - INFO - [main.py:8179] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for...
2025-07-22 19:56:27,474 - INFO - [main.py:8181] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-********-a9d2-44b1-a778-78455bae1f78', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-22T18:53:44.075Z'}, {'role': 'user', 'content': "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", 'timestamp': '2025-07-22T18:54:27.704Z'}, {'role': 'assistant', 'content': "That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in healthcare, like how it helps doctors find sicknesses and choose medicines. It's like you're saying AI is a super-tool that makes healthcare even better!\n\nNow, let's move on to the next question.\n\n**Question 2 of 5 (Level 3 Concept Exploration):** Can you think of any specific ways AI might help doctors figure out what's wrong with someone if they are feeling sick?", 'timestamp': '2025-07-22T18:54:34.097Z'}, {'role': 'user', 'content': "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", 'timestamp': '2025-07-22T18:55:02.823Z'}, {'role': 'assistant', 'content': 'That\'s a brilliant way to put it, Andrea! "Super detective" and "super-vision" are perfect descriptions. You\'ve nailed the idea of AI helping to spot things in medical images and by looking at lots of past patient information. It\'s exactly like a detective gathering clues to solve a case!\n\nLet\'s keep that detective spirit going for our next question.\n\n**Question 3 of 5 (Level 4 Application Scenario):** Imagine a doctor has a new patient who has a cough and a fever. How might AI help the doctor decide what medicine to give them?', 'timestamp': '2025-07-22T18:55:09.815Z'}]}
2025-07-22 19:56:27,475 - INFO - [main.py:8183] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍  - Session ID from payload: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:56:27,476 - INFO - [main.py:8184] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 19:56:27,476 - INFO - [main.py:8185] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 19:56:27,476 - DEBUG - [main.py:8223] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:56:27,477 - INFO - [main.py:8224] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:56:27,477 - INFO - [main.py:8264] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Level not provided, determined from grade 'primary-5': 5
2025-07-22 19:56:28,738 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 19:56:28,738 - INFO - [main.py:8281] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 19:56:28,738 - INFO - [main.py:8282] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 19:56:28,739 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 19:56:29,054 - INFO - [main.py:8312] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 19:56:29,054 - INFO - [main.py:8375] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 19:56:29,054 - INFO - [main.py:8500] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ Successfully retrieved lesson from primary path
2025-07-22 19:56:29,055 - INFO - [main.py:8511] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 19:56:29,055 - INFO - [main.py:8550] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 19:56:29,055 - INFO - [main.py:4456] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 19:56:29,547 - INFO - [main.py:4522] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 19:56:29,547 - INFO - [main.py:4522] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 19:56:29,547 - INFO - [main.py:4522] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 19:56:29,547 - INFO - [main.py:4522] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 19:56:29,548 - INFO - [main.py:4522] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 19:56:29,548 - INFO - [main.py:4591] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 19:56:29,549 - DEBUG - [main.py:4605] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 19:56:29,549 - DEBUG - [main.py:4608] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 19:56:29,549 - DEBUG - [main.py:4609] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 19:56:29,550 - DEBUG - [main.py:4610] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 19:56:29,551 - INFO - [main.py:4614] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Calling Gemini API for module inference...
2025-07-22 19:56:30,041 - INFO - [main.py:4624] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Gemini API call completed in 0.49s. Raw response: 'ai_tools_and_applications'
2025-07-22 19:56:30,041 - DEBUG - [main.py:4646] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 19:56:30,041 - INFO - [main.py:4651] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 19:56:30,042 - INFO - [main.py:8584] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 19:56:30,042 - INFO - [main.py:8621] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 19:56:30,340 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 19:56:30,843 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 19:56:30,844 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:56:30,844 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 19:56:30,845 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q3
2025-07-22 19:56:30,845 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 19:56:30,845 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 19:56:30,845 - WARNING - [main.py:8685] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 SESSION STATE DEBUG:
2025-07-22 19:56:30,846 - WARNING - [main.py:8686] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   - Session exists: True
2025-07-22 19:56:30,846 - WARNING - [main.py:8687] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   - Current phase: smart_diagnostic_q3
2025-07-22 19:56:30,847 - WARNING - [main.py:8688] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'new_phase', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 19:56:30,848 - DEBUG - [main.py:8706] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 19:56:30,848 - DEBUG - [main.py:8707] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Retrieved Phase: 'smart_diagnostic_q3'
2025-07-22 19:56:30,849 - DEBUG - [main.py:8708] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Diagnostic Completed: False
2025-07-22 19:56:30,851 - DEBUG - [main.py:8709] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Assigned Level: None
2025-07-22 19:56:30,852 - WARNING - [main.py:8710] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔒 STATE PROTECTION: phase='smart_diagnostic_q3', diagnostic_done=False, level=None
2025-07-22 19:56:30,854 - INFO - [main.py:8742] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 19:56:30,854 - INFO - [main.py:8743] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] State protection not triggered
2025-07-22 19:56:30,855 - INFO - [main.py:8791] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 19:56:30,855 - INFO - [main.py:8792] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   assigned_level_for_teaching (session): None
2025-07-22 19:56:30,855 - INFO - [main.py:8793] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   latest_assessed_level (profile): None
2025-07-22 19:56:30,855 - INFO - [main.py:8794] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   teaching_level_for_returning_student: None
2025-07-22 19:56:30,856 - INFO - [main.py:8795] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   has_completed_diagnostic_before: False
2025-07-22 19:56:30,856 - INFO - [main.py:8796] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   is_first_encounter_for_module: True
2025-07-22 19:56:30,857 - WARNING - [main.py:8801] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 19:56:30,857 - INFO - [main.py:8809] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 PHASE INVESTIGATION:
2025-07-22 19:56:30,858 - INFO - [main.py:8810] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Retrieved from Firestore: 'smart_diagnostic_q3'
2025-07-22 19:56:30,858 - INFO - [main.py:8811] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 19:56:30,858 - INFO - [main.py:8812] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Is first encounter: True
2025-07-22 19:56:30,858 - INFO - [main.py:8813] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Diagnostic completed: False
2025-07-22 19:56:30,859 - INFO - [main.py:8819] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ Using stored phase from Firestore: 'smart_diagnostic_q3'
2025-07-22 19:56:30,859 - INFO - [main.py:8833] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 19:56:30,859 - INFO - [main.py:8835] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Final phase for AI logic: smart_diagnostic_q3
2025-07-22 19:56:30,859 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 19:56:30,860 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 19:56:30,860 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 19:56:30,860 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 19:56:30,860 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 19:56:30,861 - INFO - [main.py:8855] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 19:56:30,861 - INFO - [main.py:6144] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Diagnostic context validation passed
2025-07-22 19:56:30,861 - WARNING - [main.py:9007] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q3' for first encounter
2025-07-22 19:56:30,862 - INFO - [main.py:9030] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q3
2025-07-22 19:56:30,863 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q3'
2025-07-22 19:56:30,863 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q3'
2025-07-22 19:56:30,863 - INFO - [main.py:9042] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Robust context prepared successfully. Phase: smart_diagnostic_q3
2025-07-22 19:56:30,864 - DEBUG - [main.py:9043] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 19:56:30,864 - INFO - [main.py:9285] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q3
2025-07-22 19:56:30,864 - INFO - [main.py:9286] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🎯 UNIFIED STATE MACHINE: Processing user query: The AI can be like a super-smart assistant for the doctor! First, it can look at all the information...
2025-07-22 19:56:30,865 - WARNING - [main.py:9328] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ⚠️ Unknown phase 'smart_diagnostic_q3', enforcing unified pathway
2025-07-22 19:56:30,865 - WARNING - [main.py:9329] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ⚠️ Defaulting to teaching_start phase
2025-07-22 19:56:30,866 - INFO - [main.py:7513] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📚 TEACHING PHASE HANDLER: Processing teaching content
2025-07-22 19:56:30,867 - INFO - [main.py:7545] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🤖 AI INSTRUCTOR: Continuing teaching phase
2025-07-22 19:56:30,869 - INFO - [main.py:11163] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:56:30,870 - DEBUG - [main.py:11172] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Console output error: I/O operation on closed file.
2025-07-22 19:56:30,871 - INFO - [main.py:11205] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📤 Sending message to existing session: The AI can be like a super-smart assistant for the...
2025-07-22 19:56:30,871 - INFO - [main.py:5965] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 💰 Sending message to existing session (NO API CALL)
2025-07-22 19:56:31,762 - INFO - [main.py:5970] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ Response received from session (NO API CALL COST)
2025-07-22 19:56:31,763 - INFO - [main.py:11212] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📥 Received response from session: Wow, Andrea, you're on fire with these analogies! "Super-smart assistant" and comparing to "lots of ...
2025-07-22 19:56:31,763 - INFO - [main.py:11303] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q3
2025-07-22 19:56:31,763 - INFO - [main.py:11361] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📝 Diagnostic in progress, checking for question progression
2025-07-22 19:56:31,763 - INFO - [main.py:11392] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-22 19:56:31,765 - INFO - [main.py:7573] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📊 TEACHING PROGRESS: 1 interactions
2025-07-22 19:56:31,766 - INFO - [main.py:7574] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📊 OBJECTIVES COVERAGE: 0.0%
2025-07-22 19:56:31,766 - INFO - [main.py:9342] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 19:56:31,767 - INFO - [main.py:5710] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔄 PHASE TRANSITION: smart_diagnostic_q3 → teaching_start
2025-07-22 19:56:31,769 - INFO - [phase_transition_integrity.py:153] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q3 → teaching_start
2025-07-22 19:56:31,770 - WARNING - [phase_transition_integrity.py:169] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ❌ INVALID TRANSITION: smart_diagnostic_q3 → teaching_start
2025-07-22 19:56:31,771 - ERROR - [main.py:5733] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ❌ INVALID TRANSITION: smart_diagnostic_q3 → teaching_start, using smart_diagnostic_q3
2025-07-22 19:56:31,772 - INFO - [main.py:9358] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q3 → smart_diagnostic_q3
2025-07-22 19:56:31,772 - INFO - [main.py:9359] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 19:56:32,587 - INFO - [main.py:9373] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ UNIFIED STATE MACHINE: Session state updated with phase smart_diagnostic_q3
2025-07-22 19:56:32,587 - INFO - [main.py:9383] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 19:56:32,588 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:56:32,589 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:56:32,589 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:56:32,589 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:56:32,590 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:56:32,590 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:56:32,590 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:56:32,591 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:56:32,592 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:56:32,592 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:56:32,593 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:56:32,594 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:56:32,594 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:56:32,595 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:56:32,595 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:56:32,595 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:56:32,595 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:56:32,595 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:56:32,596 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:56:32,596 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:56:32,596 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:56:32,597 - INFO - [intelligent_guardrails.py:130] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:56:32,598 - INFO - [intelligent_guardrails.py:183] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:56:32,598 - INFO - [intelligent_guardrails.py:675] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 19:56:32,598 - INFO - [main.py:9515] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 19:56:32,599 - WARNING - [main.py:9563] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🤖 AI RESPONSE RECEIVED:
2025-07-22 19:56:32,599 - WARNING - [main.py:9564] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🤖   - Content length: 572 chars
2025-07-22 19:56:32,599 - WARNING - [main.py:9565] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q4', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:56:32,600 - WARNING - [main.py:9566] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🤖   - Raw state block: None...
2025-07-22 19:56:32,601 - DEBUG - [main.py:10099] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 19:56:32,602 - DEBUG - [main.py:10100] - 🤖   Content: Wow, Andrea, you're on fire with these analogies! "Super-smart assistant" and comparing to "lots of ...
2025-07-22 19:56:32,603 - DEBUG - [main.py:10101] - 🤖   State: {'new_phase': 'smart_diagnostic_q4', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:56:32,604 - INFO - [main.py:10127] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 19:56:32,604 - INFO - [main.py:10128] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q4, Session=smart_diagnostic_q3, Final=smart_diagnostic_q4
2025-07-22 19:56:32,910 - WARNING - [main.py:10230] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q3', new_phase='smart_diagnostic_q4'
2025-07-22 19:56:32,911 - INFO - [main.py:6365] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI state update validation passed: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-22 19:56:32,912 - WARNING - [main.py:10239] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 19:56:32,912 - WARNING - [main.py:10258] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔄 PHASE TRANSITION: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-22 19:56:32,912 - INFO - [main.py:10289] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 19:56:32,913 - INFO - [main.py:10294] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 19:56:32,913 - INFO - [phase_transition_integrity.py:330] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q3 → teaching_start
2025-07-22 19:56:32,914 - INFO - [phase_transition_integrity.py:293] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📸 DATA SNAPSHOT CREATED: Session fallback-********-a9d2-44b1-a778-78455bae1f78, Phase smart_diagnostic_q3
2025-07-22 19:56:32,914 - WARNING - [phase_transition_integrity.py:342] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 19:56:32,914 - WARNING - [phase_transition_integrity.py:343] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 19:56:32,915 - WARNING - [phase_transition_integrity.py:344] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 19:56:32,915 - WARNING - [phase_transition_integrity.py:345] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 19:56:32,916 - WARNING - [phase_transition_integrity.py:347] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   - assigned_level_for_teaching value: None
2025-07-22 19:56:32,916 - WARNING - [phase_transition_integrity.py:348] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   - state_updates keys: ['new_phase', 'teaching_interactions', 'current_phase', 'unified_pathway_enforced']
2025-07-22 19:56:32,918 - WARNING - [phase_transition_integrity.py:349] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 19:56:32,919 - INFO - [phase_transition_integrity.py:153] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q3 → teaching_start
2025-07-22 19:56:32,920 - WARNING - [phase_transition_integrity.py:169] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ❌ INVALID TRANSITION: smart_diagnostic_q3 → teaching_start
2025-07-22 19:56:32,921 - ERROR - [phase_transition_integrity.py:389] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔧 TRANSITION RECOVERY: smart_diagnostic_q3 → smart_diagnostic_start
2025-07-22 19:56:32,922 - DEBUG - [phase_transition_integrity.py:847] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 19:56:32,922 - DEBUG - [phase_transition_integrity.py:880] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 📝 TRANSITION RECORDED: smart_diagnostic_q3 → smart_diagnostic_start (invalid)
2025-07-22 19:56:32,922 - INFO - [phase_transition_integrity.py:406] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 19:56:32,923 - WARNING - [main.py:10336] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q4
2025-07-22 19:56:32,923 - WARNING - [main.py:10363] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔧 TRANSITION RECOVERED: Invalid transition: smart_diagnostic_q3 → teaching_start not allowed
2025-07-22 19:56:32,923 - WARNING - [main.py:10372] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 19:56:32,924 - WARNING - [main.py:10373] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   1. Input phase: 'smart_diagnostic_q3'
2025-07-22 19:56:32,924 - WARNING - [main.py:10374] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 19:56:32,924 - WARNING - [main.py:10375] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 19:56:32,925 - WARNING - [main.py:10376] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 19:56:32,925 - WARNING - [main.py:10377] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍   5. Final phase to save: 'smart_diagnostic_q4'
2025-07-22 19:56:32,925 - WARNING - [main.py:10380] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 💾 FINAL STATE APPLICATION:
2025-07-22 19:56:32,926 - WARNING - [main.py:10381] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 💾   - Current phase input: 'smart_diagnostic_q3'
2025-07-22 19:56:32,926 - WARNING - [main.py:10382] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 💾   - Validated state updates: 27 fields
2025-07-22 19:56:32,927 - WARNING - [main.py:10383] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 💾   - Final phase to save: 'smart_diagnostic_q4'
2025-07-22 19:56:32,927 - WARNING - [main.py:10384] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 💾   - Phase change: True
2025-07-22 19:56:32,928 - WARNING - [main.py:10385] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 💾   - Integrity applied: True
2025-07-22 19:56:32,928 - INFO - [main.py:6397] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 19:56:32,928 - INFO - [main.py:6398] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Phase transition: smart_diagnostic_q3 -> smart_diagnostic_start
2025-07-22 19:56:32,928 - INFO - [main.py:6399] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Current level: 2
2025-07-22 19:56:32,929 - INFO - [main.py:6400] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Question index: 0
2025-07-22 19:56:32,929 - INFO - [main.py:6401] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   First encounter: True
2025-07-22 19:56:32,929 - INFO - [main.py:6406] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Answers collected: 0
2025-07-22 19:56:32,929 - INFO - [main.py:6407] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Levels failed: 0
2025-07-22 19:56:32,930 - INFO - [main.py:6365] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI state update validation passed: smart_diagnostic_q3 → smart_diagnostic_start
2025-07-22 19:56:32,930 - INFO - [main.py:6411] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   State update valid: True
2025-07-22 19:56:32,930 - INFO - [main.py:6418] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   Diagnostic complete: False
2025-07-22 19:56:32,930 - WARNING - [main.py:10403] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 19:56:32,931 - INFO - [main.py:10412] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 DEBUG state_updates_from_ai teaching_interactions: 1
2025-07-22 19:56:32,931 - INFO - [main.py:10413] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 19:56:32,932 - WARNING - [main.py:10416] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ INTERACTION COUNT PROTECTION: AI tried to set 1, backend preserving 0
2025-07-22 19:56:32,932 - WARNING - [main.py:10417] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96]   This prevents teaching interaction count resets and infinite loops
2025-07-22 19:56:33,423 - WARNING - [main.py:10463] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 19:56:33,423 - WARNING - [main.py:10464] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅   - Phase: smart_diagnostic_q4
2025-07-22 19:56:33,424 - WARNING - [main.py:10465] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅   - Probing Level: 2
2025-07-22 19:56:33,424 - WARNING - [main.py:10466] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅   - Question Index: 0
2025-07-22 19:56:33,424 - WARNING - [main.py:10467] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅   - Diagnostic Complete: False
2025-07-22 19:56:33,425 - WARNING - [main.py:10474] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅   - Quiz Questions Saved: 0
2025-07-22 19:56:33,425 - WARNING - [main.py:10475] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅   - Quiz Answers Saved: 0
2025-07-22 19:56:33,425 - WARNING - [main.py:10476] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅   - Quiz Started: False
2025-07-22 19:56:33,426 - DEBUG - [main.py:10525] - 🔥 STATE SAVED - Session: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: smart_diagnostic_q4
2025-07-22 19:56:33,427 - DEBUG - [main.py:10526] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 19:56:34,264 - DEBUG - [main.py:10584] - ✅ SESSION UPDATED - ID: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: smart_diagnostic_q4
2025-07-22 19:56:34,265 - DEBUG - [main.py:10585] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-22 19:56:34,265 - INFO - [main.py:10591] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ Updated existing session document: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:56:34,266 - INFO - [main.py:18505] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 19:56:34,266 - DEBUG - [main.py:4943] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 19:56:34,266 - DEBUG - [main.py:10678] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] No final assessment data found in AI response
2025-07-22 19:56:34,268 - DEBUG - [main.py:10701] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 19:56:34,269 - DEBUG - [main.py:10725] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 19:56:34,270 - DEBUG - [main.py:10726] - 🔒   Current Phase: smart_diagnostic_q3
2025-07-22 19:56:34,271 - DEBUG - [main.py:10727] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 19:56:34,272 - DEBUG - [main.py:10728] - 🔒   Diagnostic Complete: False
2025-07-22 19:56:34,272 - DEBUG - [main.py:10729] - 🔒   Assigned Level: None
2025-07-22 19:56:34,273 - INFO - [main.py:10802] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 19:56:34,273 - INFO - [main.py:10836] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 19:56:34,274 - INFO - [main.py:10844] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 19:56:34,274 - INFO - [main.py:10849] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 19:56:34,275 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:56:34,276 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:56:34,276 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:56:34,276 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:56:34,277 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:56:34,277 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:56:34,277 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:56:34,277 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:56:34,278 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:56:34,278 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:56:34,278 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:56:34,278 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:56:34,279 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:56:34,279 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:56:34,279 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:56:34,279 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:56:34,280 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:56:34,280 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:56:34,280 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:56:34,280 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:56:34,281 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:56:34,281 - INFO - [main.py:10858] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 19:56:34,281 - INFO - [intelligent_guardrails.py:130] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:56:34,283 - INFO - [intelligent_guardrails.py:183] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:56:34,284 - INFO - [main.py:10920] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 19:56:34,286 - INFO - [main.py:10948] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 19:56:34,287 - DEBUG - [main.py:11005] - 🎯 RESPONSE READY:
2025-07-22 19:56:34,288 - DEBUG - [main.py:11006] - 🎯   Session: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:56:34,288 - DEBUG - [main.py:11007] - 🎯   Phase: smart_diagnostic_q3 → smart_diagnostic_start
2025-07-22 19:56:34,289 - DEBUG - [main.py:11008] - 🎯   Content: Wow, Andrea, you're on fire with these analogies! ...
2025-07-22 19:56:34,289 - DEBUG - [main.py:11009] - 🎯   Request ID: cac2061a-8a71-4bad-b867-e02a5bf8ee96
2025-07-22 19:56:34,290 - INFO - [main.py:11015] - [cac2061a-8a71-4bad-b867-e02a5bf8ee96] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 19:56:34,290 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,291 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,291 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,291 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:56:34,291 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:56:34,291 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:56:34,292 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:56:34,292 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,292 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:56:34,292 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,293 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,293 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,293 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,293 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,294 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:56:34,296 - WARNING - [main.py:888] - High response time detected: 6.83s for enhance_content_api
2025-07-22 19:57:06,390 - INFO - [main.py:7143] - Incoming request: {"request_id": "c4db746b-50ab-4d0f-864a-fe91dcaf1276", "timestamp": "2025-07-22T18:57:06.388211+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to give, AI can do lots of other cool things! It can help make sure patients get the right medicine at the right time, like a helpful reminder system. AI can also help doctors keep track of lots of patient information really well, almost like a super-organized filing cabinet. It can even help invent new medicines by looking at tiny details about how sicknesses work. And sometimes, AI can help robots do very careful jobs, like helping a surgeon during an operation, making it even steadier. It can also help people who need to stay home by chatting with them and checking how they are feeling. It's like having many helpers all working together to make people better!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-********-a9d2-44b1-a778-78455bae1f78", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-22T18:53:44.075Z"}, {"role": "user", "content": "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", "timestamp": "2025-07-22T18:54:27.704Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in healthcare, like how it helps doctors find sicknesses and choose medicines. It's like you're saying AI is a super-tool that makes healthcare even better!\n\nNow, let's move on to the next question.\n\n**Question 2 of 5 (Level 3 Concept Exploration):** Can you think of any specific ways AI might help doctors figure out what's wrong with someone if they are feeling sick?", "timestamp": "2025-07-22T18:54:34.097Z"}, {"role": "user", "content": "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", "timestamp": "2025-07-22T18:55:02.823Z"}, {"role": "assistant", "content": "That's a brilliant way to put it, Andrea! \"Super detective\" and \"super-vision\" are perfect descriptions. You've nailed the idea of AI helping to spot things in medical images and by looking at lots of past patient information. It's exactly like a detective gathering clues to solve a case!\n\nLet's keep that detective spirit going for our next question.\n\n**Question 3 of 5 (Level 4 Application Scenario):** Imagine a doctor has a new patient who has a cough and a fever. How might AI help the doctor decide what medicine to give them?", "timestamp": "2025-07-22T18:55:09.815Z"}, {"role": "user", "content": "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", "timestamp": "2025-07-22T18:56:26.985Z"}, {"role": "assistant", "content": "Wow, Andrea, you're on fire with these analogies! \"Super-smart assistant\" and comparing to \"lots of other people\" is exactly right. And the idea of a \"giant library of medicines\" is a fantastic way to think about how AI can find the best treatment. You're really understanding how AI can help doctors make informed decisions!\n\nWe're almost there! Let's try this next one.\n\n**Question 4 of 5 (Level 5 Deeper Understanding):** Besides helping doctors find out what's wrong and what medicine to use, what else do you think AI could do in hospitals or for people who are sick?", "timestamp": "2025-07-22T18:56:34.331Z"}]}}
2025-07-22 19:57:06,391 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 19:57:06,391 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FojRArwf7TCv4y3JoZYASQTKKeemWG0Zxr2u8rLS_3HvNj0ZMYI-0G7Xbxx3Yhp6l5qhe14xG2DAhf0vXXcHgss9zySkSFdJqJhUCVL5I9WvN8l9G6LqHsOIOxXc3rKImNtmJ6U38HeqI3grXrFtqbS8AGZA8gVhKt97seDdqmUoIJxTvRa9B_CoDHpRgoPtma8eSC-2Z6xEepuhE0cmy8pqmRCRODvn7pgZ_TnOTK0AiA-OiH-PcV0sU9jLg2wdFacFrdCvYzgL_lTphao7Yrm-W1XQgBYZ4WRMLvIsy6AYZpEbXBcM2vfuQRBl3sqVAw2Ji5b4XU77_645ujHrDw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '5290', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 19:57:06,392 - INFO - [auth_decorator.py:70] - 🔒 Request ID: c4db746b-50ab-4d0f-864a-fe91dcaf1276
2025-07-22 19:57:06,392 - INFO - [auth_decorator.py:74] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 19:57:06,393 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 19:57:06,393 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 19:57:06,393 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 19:57:06,393 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 19:57:06,394 - INFO - [auth_decorator.py:95] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276][require_auth] Development mode detected - bypassing authentication
2025-07-22 19:57:06,395 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 19:57:06,395 - INFO - [auth_decorator.py:121] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 19:57:06,704 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:57:06,704 - INFO - [auth_decorator.py:164] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:57:06,704 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 19:57:06,706 - INFO - [main.py:7327] -
================================================================================
2025-07-22 19:57:06,706 - WARNING - [main.py:7328] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 19:57:06,706 - WARNING - [main.py:7329] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 19:57:06,707 - DEBUG - [main.py:7337] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Console output error: I/O operation on closed file.
2025-07-22 19:57:06,708 - INFO - [main.py:8179] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to give, AI can do lots of other cool things! It can help make sure patients get the right medicine at the right time, like a helpful reminder system. AI can also help doctors keep track of lots of patient information really well, almost like a super-organized filing cabinet. It can even help invent new medicines by look...
2025-07-22 19:57:06,709 - INFO - [main.py:8181] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to give, AI can do lots of other cool things! It can help make sure patients get the right medicine at the right time, like a helpful reminder system. AI can also help doctors keep track of lots of patient information really well, almost like a super-organized filing cabinet. It can even help invent new medicines by looking at tiny details about how sicknesses work. And sometimes, AI can help robots do very careful jobs, like helping a surgeon during an operation, making it even steadier. It can also help people who need to stay home by chatting with them and checking how they are feeling. It's like having many helpers all working together to make people better!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-********-a9d2-44b1-a778-78455bae1f78', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-22T18:53:44.075Z'}, {'role': 'user', 'content': "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", 'timestamp': '2025-07-22T18:54:27.704Z'}, {'role': 'assistant', 'content': "That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in healthcare, like how it helps doctors find sicknesses and choose medicines. It's like you're saying AI is a super-tool that makes healthcare even better!\n\nNow, let's move on to the next question.\n\n**Question 2 of 5 (Level 3 Concept Exploration):** Can you think of any specific ways AI might help doctors figure out what's wrong with someone if they are feeling sick?", 'timestamp': '2025-07-22T18:54:34.097Z'}, {'role': 'user', 'content': "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", 'timestamp': '2025-07-22T18:55:02.823Z'}, {'role': 'assistant', 'content': 'That\'s a brilliant way to put it, Andrea! "Super detective" and "super-vision" are perfect descriptions. You\'ve nailed the idea of AI helping to spot things in medical images and by looking at lots of past patient information. It\'s exactly like a detective gathering clues to solve a case!\n\nLet\'s keep that detective spirit going for our next question.\n\n**Question 3 of 5 (Level 4 Application Scenario):** Imagine a doctor has a new patient who has a cough and a fever. How might AI help the doctor decide what medicine to give them?', 'timestamp': '2025-07-22T18:55:09.815Z'}, {'role': 'user', 'content': "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", 'timestamp': '2025-07-22T18:56:26.985Z'}, {'role': 'assistant', 'content': 'Wow, Andrea, you\'re on fire with these analogies! "Super-smart assistant" and comparing to "lots of other people" is exactly right. And the idea of a "giant library of medicines" is a fantastic way to think about how AI can find the best treatment. You\'re really understanding how AI can help doctors make informed decisions!\n\nWe\'re almost there! Let\'s try this next one.\n\n**Question 4 of 5 (Level 5 Deeper Understanding):** Besides helping doctors find out what\'s wrong and what medicine to use, what else do you think AI could do in hospitals or for people who are sick?', 'timestamp': '2025-07-22T18:56:34.331Z'}]}
2025-07-22 19:57:06,710 - INFO - [main.py:8183] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍  - Session ID from payload: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:06,710 - INFO - [main.py:8184] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 19:57:06,710 - INFO - [main.py:8185] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 19:57:06,711 - DEBUG - [main.py:8223] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:57:06,711 - INFO - [main.py:8224] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:57:06,712 - INFO - [main.py:8264] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Level not provided, determined from grade 'primary-5': 5
2025-07-22 19:57:06,992 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 19:57:06,993 - INFO - [main.py:8281] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 19:57:06,993 - INFO - [main.py:8282] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 19:57:06,993 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 19:57:07,527 - INFO - [main.py:8312] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 19:57:07,527 - INFO - [main.py:8375] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 19:57:07,528 - INFO - [main.py:8500] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ Successfully retrieved lesson from primary path
2025-07-22 19:57:07,528 - INFO - [main.py:8511] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 19:57:07,528 - INFO - [main.py:8550] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 19:57:07,529 - INFO - [main.py:4456] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 19:57:07,854 - INFO - [main.py:4522] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 19:57:07,855 - INFO - [main.py:4522] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 19:57:07,855 - INFO - [main.py:4522] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 19:57:07,855 - INFO - [main.py:4522] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 19:57:07,855 - INFO - [main.py:4522] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 19:57:07,855 - INFO - [main.py:4591] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 19:57:07,856 - DEBUG - [main.py:4605] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 19:57:07,856 - DEBUG - [main.py:4608] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 19:57:07,856 - DEBUG - [main.py:4609] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 19:57:07,856 - DEBUG - [main.py:4610] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 19:57:07,857 - INFO - [main.py:4614] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Calling Gemini API for module inference...
2025-07-22 19:57:08,325 - INFO - [main.py:4624] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Gemini API call completed in 0.47s. Raw response: 'ai_tools_and_applications'
2025-07-22 19:57:08,325 - DEBUG - [main.py:4646] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 19:57:08,325 - INFO - [main.py:4651] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 19:57:08,325 - INFO - [main.py:8584] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 19:57:08,326 - INFO - [main.py:8621] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 19:57:08,639 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 19:57:09,107 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 19:57:09,107 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:09,107 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 19:57:09,108 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q4
2025-07-22 19:57:09,108 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 19:57:09,108 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 19:57:09,108 - WARNING - [main.py:8685] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 SESSION STATE DEBUG:
2025-07-22 19:57:09,109 - WARNING - [main.py:8686] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   - Session exists: True
2025-07-22 19:57:09,109 - WARNING - [main.py:8687] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   - Current phase: smart_diagnostic_q4
2025-07-22 19:57:09,109 - WARNING - [main.py:8688] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'new_phase', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 19:57:09,109 - DEBUG - [main.py:8706] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 19:57:09,110 - DEBUG - [main.py:8707] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Retrieved Phase: 'smart_diagnostic_q4'
2025-07-22 19:57:09,110 - DEBUG - [main.py:8708] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Diagnostic Completed: False
2025-07-22 19:57:09,111 - DEBUG - [main.py:8709] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Assigned Level: None
2025-07-22 19:57:09,111 - WARNING - [main.py:8710] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔒 STATE PROTECTION: phase='smart_diagnostic_q4', diagnostic_done=False, level=None
2025-07-22 19:57:09,112 - INFO - [main.py:8742] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 19:57:09,112 - INFO - [main.py:8743] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] State protection not triggered
2025-07-22 19:57:09,113 - INFO - [main.py:8791] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 19:57:09,113 - INFO - [main.py:8792] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   assigned_level_for_teaching (session): None
2025-07-22 19:57:09,114 - INFO - [main.py:8793] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   latest_assessed_level (profile): None
2025-07-22 19:57:09,114 - INFO - [main.py:8794] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   teaching_level_for_returning_student: None
2025-07-22 19:57:09,115 - INFO - [main.py:8795] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   has_completed_diagnostic_before: False
2025-07-22 19:57:09,115 - INFO - [main.py:8796] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   is_first_encounter_for_module: True
2025-07-22 19:57:09,116 - WARNING - [main.py:8801] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 19:57:09,116 - INFO - [main.py:8809] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 PHASE INVESTIGATION:
2025-07-22 19:57:09,116 - INFO - [main.py:8810] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Retrieved from Firestore: 'smart_diagnostic_q4'
2025-07-22 19:57:09,118 - INFO - [main.py:8811] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 19:57:09,119 - INFO - [main.py:8812] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Is first encounter: True
2025-07-22 19:57:09,119 - INFO - [main.py:8813] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Diagnostic completed: False
2025-07-22 19:57:09,120 - INFO - [main.py:8819] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ Using stored phase from Firestore: 'smart_diagnostic_q4'
2025-07-22 19:57:09,120 - INFO - [main.py:8833] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 19:57:09,120 - INFO - [main.py:8835] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Final phase for AI logic: smart_diagnostic_q4
2025-07-22 19:57:09,120 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 19:57:09,121 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 19:57:09,121 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 19:57:09,121 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 19:57:09,122 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 19:57:09,122 - INFO - [main.py:8855] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 19:57:09,123 - INFO - [main.py:6144] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Diagnostic context validation passed
2025-07-22 19:57:09,123 - WARNING - [main.py:9007] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q4' for first encounter
2025-07-22 19:57:09,124 - INFO - [main.py:9030] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q4
2025-07-22 19:57:09,124 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q4'
2025-07-22 19:57:09,124 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q4'
2025-07-22 19:57:09,125 - INFO - [main.py:9042] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Robust context prepared successfully. Phase: smart_diagnostic_q4
2025-07-22 19:57:09,125 - DEBUG - [main.py:9043] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 19:57:09,125 - INFO - [main.py:9285] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q4
2025-07-22 19:57:09,125 - INFO - [main.py:9286] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🎯 UNIFIED STATE MACHINE: Processing user query: Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to g...
2025-07-22 19:57:09,126 - WARNING - [main.py:9328] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ⚠️ Unknown phase 'smart_diagnostic_q4', enforcing unified pathway
2025-07-22 19:57:09,126 - WARNING - [main.py:9329] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ⚠️ Defaulting to teaching_start phase
2025-07-22 19:57:09,126 - INFO - [main.py:7513] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📚 TEACHING PHASE HANDLER: Processing teaching content
2025-07-22 19:57:09,126 - INFO - [main.py:7545] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🤖 AI INSTRUCTOR: Continuing teaching phase
2025-07-22 19:57:09,127 - INFO - [main.py:11163] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:09,127 - DEBUG - [main.py:11172] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Console output error: I/O operation on closed file.
2025-07-22 19:57:09,127 - INFO - [main.py:11205] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📤 Sending message to existing session: Wow, that's a great question! Besides helping doct...
2025-07-22 19:57:09,127 - INFO - [main.py:5965] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 💰 Sending message to existing session (NO API CALL)
2025-07-22 19:57:10,306 - INFO - [main.py:5970] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ Response received from session (NO API CALL COST)
2025-07-22 19:57:10,306 - INFO - [main.py:11212] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📥 Received response from session: That's an absolutely brilliant summary, Andrea! You've hit on so many important areas: keeping track...
2025-07-22 19:57:10,307 - INFO - [main.py:11303] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q4
2025-07-22 19:57:10,307 - INFO - [main.py:11361] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📝 Diagnostic in progress, checking for question progression
2025-07-22 19:57:10,307 - INFO - [main.py:11392] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-22 19:57:10,308 - INFO - [main.py:7573] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📊 TEACHING PROGRESS: 1 interactions
2025-07-22 19:57:10,308 - INFO - [main.py:7574] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📊 OBJECTIVES COVERAGE: 0.0%
2025-07-22 19:57:10,308 - INFO - [main.py:9342] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 19:57:10,308 - INFO - [main.py:5710] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔄 PHASE TRANSITION: smart_diagnostic_q4 → teaching_start
2025-07-22 19:57:10,309 - INFO - [phase_transition_integrity.py:153] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q4 → teaching_start
2025-07-22 19:57:10,309 - WARNING - [phase_transition_integrity.py:169] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ❌ INVALID TRANSITION: smart_diagnostic_q4 → teaching_start
2025-07-22 19:57:10,309 - ERROR - [main.py:5733] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ❌ INVALID TRANSITION: smart_diagnostic_q4 → teaching_start, using smart_diagnostic_q4
2025-07-22 19:57:10,310 - INFO - [main.py:9358] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q4 → smart_diagnostic_q4
2025-07-22 19:57:10,311 - INFO - [main.py:9359] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 19:57:11,116 - INFO - [main.py:9373] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ UNIFIED STATE MACHINE: Session state updated with phase smart_diagnostic_q4
2025-07-22 19:57:11,116 - INFO - [main.py:9383] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 19:57:11,117 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:57:11,118 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:57:11,119 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:57:11,119 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:57:11,120 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:57:11,120 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:57:11,120 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:57:11,121 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:57:11,121 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:57:11,122 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:57:11,122 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:57:11,122 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:57:11,122 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:57:11,122 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:57:11,123 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:57:11,123 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:57:11,123 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:57:11,123 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:57:11,124 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:57:11,124 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:57:11,125 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:57:11,125 - INFO - [intelligent_guardrails.py:130] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:57:11,126 - INFO - [intelligent_guardrails.py:183] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:57:11,126 - INFO - [intelligent_guardrails.py:675] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 19:57:11,126 - INFO - [main.py:9515] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 19:57:11,126 - WARNING - [main.py:9563] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🤖 AI RESPONSE RECEIVED:
2025-07-22 19:57:11,127 - WARNING - [main.py:9564] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🤖   - Content length: 572 chars
2025-07-22 19:57:11,128 - WARNING - [main.py:9565] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q5', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:57:11,128 - WARNING - [main.py:9566] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🤖   - Raw state block: None...
2025-07-22 19:57:11,128 - DEBUG - [main.py:10099] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 19:57:11,129 - DEBUG - [main.py:10100] - 🤖   Content: That's an absolutely brilliant summary, Andrea! You've hit on so many important areas: keeping track...
2025-07-22 19:57:11,129 - DEBUG - [main.py:10101] - 🤖   State: {'new_phase': 'smart_diagnostic_q5', 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:57:11,129 - INFO - [main.py:10127] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 19:57:11,129 - INFO - [main.py:10128] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q5, Session=smart_diagnostic_q4, Final=smart_diagnostic_q5
2025-07-22 19:57:11,474 - WARNING - [main.py:10230] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q4', new_phase='smart_diagnostic_q5'
2025-07-22 19:57:11,475 - INFO - [main.py:6365] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI state update validation passed: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-22 19:57:11,475 - WARNING - [main.py:10239] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 19:57:11,476 - WARNING - [main.py:10258] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔄 PHASE TRANSITION: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-22 19:57:11,477 - INFO - [main.py:10289] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 19:57:11,477 - INFO - [main.py:10294] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 19:57:11,478 - INFO - [phase_transition_integrity.py:330] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q4 → teaching_start
2025-07-22 19:57:11,478 - INFO - [phase_transition_integrity.py:293] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📸 DATA SNAPSHOT CREATED: Session fallback-********-a9d2-44b1-a778-78455bae1f78, Phase smart_diagnostic_q4
2025-07-22 19:57:11,479 - WARNING - [phase_transition_integrity.py:342] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 19:57:11,480 - WARNING - [phase_transition_integrity.py:343] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 19:57:11,480 - WARNING - [phase_transition_integrity.py:344] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 19:57:11,481 - WARNING - [phase_transition_integrity.py:345] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 19:57:11,481 - WARNING - [phase_transition_integrity.py:347] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   - assigned_level_for_teaching value: None
2025-07-22 19:57:11,482 - WARNING - [phase_transition_integrity.py:348] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   - state_updates keys: ['new_phase', 'teaching_interactions', 'current_phase', 'unified_pathway_enforced']
2025-07-22 19:57:11,482 - WARNING - [phase_transition_integrity.py:349] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 19:57:11,482 - INFO - [phase_transition_integrity.py:153] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q4 → teaching_start
2025-07-22 19:57:11,484 - WARNING - [phase_transition_integrity.py:169] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ❌ INVALID TRANSITION: smart_diagnostic_q4 → teaching_start
2025-07-22 19:57:11,487 - ERROR - [phase_transition_integrity.py:389] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔧 TRANSITION RECOVERY: smart_diagnostic_q4 → smart_diagnostic_start
2025-07-22 19:57:11,487 - DEBUG - [phase_transition_integrity.py:847] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 19:57:11,487 - DEBUG - [phase_transition_integrity.py:880] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 📝 TRANSITION RECORDED: smart_diagnostic_q4 → smart_diagnostic_start (invalid)
2025-07-22 19:57:11,488 - INFO - [phase_transition_integrity.py:406] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 19:57:11,489 - WARNING - [main.py:10336] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q5
2025-07-22 19:57:11,490 - WARNING - [main.py:10363] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔧 TRANSITION RECOVERED: Invalid transition: smart_diagnostic_q4 → teaching_start not allowed
2025-07-22 19:57:11,491 - WARNING - [main.py:10372] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 19:57:11,491 - WARNING - [main.py:10373] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   1. Input phase: 'smart_diagnostic_q4'
2025-07-22 19:57:11,492 - WARNING - [main.py:10374] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 19:57:11,492 - WARNING - [main.py:10375] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 19:57:11,492 - WARNING - [main.py:10376] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 19:57:11,493 - WARNING - [main.py:10377] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍   5. Final phase to save: 'smart_diagnostic_q5'
2025-07-22 19:57:11,493 - WARNING - [main.py:10380] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 💾 FINAL STATE APPLICATION:
2025-07-22 19:57:11,493 - WARNING - [main.py:10381] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 💾   - Current phase input: 'smart_diagnostic_q4'
2025-07-22 19:57:11,494 - WARNING - [main.py:10382] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 💾   - Validated state updates: 27 fields
2025-07-22 19:57:11,494 - WARNING - [main.py:10383] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 💾   - Final phase to save: 'smart_diagnostic_q5'
2025-07-22 19:57:11,494 - WARNING - [main.py:10384] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 💾   - Phase change: True
2025-07-22 19:57:11,495 - WARNING - [main.py:10385] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 💾   - Integrity applied: True
2025-07-22 19:57:11,495 - INFO - [main.py:6397] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 19:57:11,495 - INFO - [main.py:6398] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Phase transition: smart_diagnostic_q4 -> smart_diagnostic_start
2025-07-22 19:57:11,496 - INFO - [main.py:6399] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Current level: 2
2025-07-22 19:57:11,496 - INFO - [main.py:6400] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Question index: 0
2025-07-22 19:57:11,496 - INFO - [main.py:6401] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   First encounter: True
2025-07-22 19:57:11,496 - INFO - [main.py:6406] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Answers collected: 0
2025-07-22 19:57:11,496 - INFO - [main.py:6407] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Levels failed: 0
2025-07-22 19:57:11,497 - INFO - [main.py:6365] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI state update validation passed: smart_diagnostic_q4 → smart_diagnostic_start
2025-07-22 19:57:11,497 - INFO - [main.py:6411] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   State update valid: True
2025-07-22 19:57:11,497 - INFO - [main.py:6418] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   Diagnostic complete: False
2025-07-22 19:57:11,497 - WARNING - [main.py:10403] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 19:57:11,498 - INFO - [main.py:10412] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 DEBUG state_updates_from_ai teaching_interactions: 1
2025-07-22 19:57:11,498 - INFO - [main.py:10413] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 19:57:11,499 - WARNING - [main.py:10416] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ INTERACTION COUNT PROTECTION: AI tried to set 1, backend preserving 0
2025-07-22 19:57:11,499 - WARNING - [main.py:10417] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276]   This prevents teaching interaction count resets and infinite loops
2025-07-22 19:57:12,012 - WARNING - [main.py:10463] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 19:57:12,012 - WARNING - [main.py:10464] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅   - Phase: smart_diagnostic_q5
2025-07-22 19:57:12,012 - WARNING - [main.py:10465] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅   - Probing Level: 2
2025-07-22 19:57:12,012 - WARNING - [main.py:10466] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅   - Question Index: 0
2025-07-22 19:57:12,013 - WARNING - [main.py:10467] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅   - Diagnostic Complete: False
2025-07-22 19:57:12,013 - WARNING - [main.py:10474] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅   - Quiz Questions Saved: 0
2025-07-22 19:57:12,014 - WARNING - [main.py:10475] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅   - Quiz Answers Saved: 0
2025-07-22 19:57:12,014 - WARNING - [main.py:10476] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅   - Quiz Started: False
2025-07-22 19:57:12,014 - DEBUG - [main.py:10525] - 🔥 STATE SAVED - Session: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: smart_diagnostic_q5
2025-07-22 19:57:12,015 - DEBUG - [main.py:10526] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 19:57:12,887 - DEBUG - [main.py:10584] - ✅ SESSION UPDATED - ID: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: smart_diagnostic_q5
2025-07-22 19:57:12,888 - DEBUG - [main.py:10585] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-22 19:57:12,888 - INFO - [main.py:10591] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ Updated existing session document: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:12,888 - INFO - [main.py:18505] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 19:57:12,888 - DEBUG - [main.py:4943] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 19:57:12,889 - DEBUG - [main.py:10678] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] No final assessment data found in AI response
2025-07-22 19:57:12,889 - DEBUG - [main.py:10701] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 19:57:12,889 - DEBUG - [main.py:10725] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 19:57:12,890 - DEBUG - [main.py:10726] - 🔒   Current Phase: smart_diagnostic_q4
2025-07-22 19:57:12,890 - DEBUG - [main.py:10727] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 19:57:12,890 - DEBUG - [main.py:10728] - 🔒   Diagnostic Complete: False
2025-07-22 19:57:12,891 - DEBUG - [main.py:10729] - 🔒   Assigned Level: None
2025-07-22 19:57:12,892 - INFO - [main.py:10802] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 19:57:12,892 - INFO - [main.py:10836] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 19:57:12,893 - INFO - [main.py:10844] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 19:57:12,893 - INFO - [main.py:10849] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 19:57:12,893 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:57:12,894 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:57:12,895 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:57:12,895 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:57:12,895 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:57:12,895 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:57:12,896 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:57:12,896 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:57:12,896 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:57:12,896 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:57:12,896 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:57:12,897 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:57:12,897 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:57:12,897 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:57:12,897 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:57:12,897 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:57:12,898 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:57:12,898 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:57:12,898 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:57:12,898 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:57:12,898 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:57:12,899 - INFO - [main.py:10858] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 19:57:12,899 - INFO - [intelligent_guardrails.py:130] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:57:12,899 - INFO - [intelligent_guardrails.py:183] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 19:57:12,900 - INFO - [main.py:10920] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 19:57:12,900 - INFO - [main.py:10948] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 19:57:12,901 - DEBUG - [main.py:11005] - 🎯 RESPONSE READY:
2025-07-22 19:57:12,903 - DEBUG - [main.py:11006] - 🎯   Session: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:12,904 - DEBUG - [main.py:11007] - 🎯   Phase: smart_diagnostic_q4 → smart_diagnostic_start
2025-07-22 19:57:12,904 - DEBUG - [main.py:11008] - 🎯   Content: That's an absolutely brilliant summary, Andrea! Yo...
2025-07-22 19:57:12,905 - DEBUG - [main.py:11009] - 🎯   Request ID: c4db746b-50ab-4d0f-864a-fe91dcaf1276
2025-07-22 19:57:12,905 - INFO - [main.py:11015] - [c4db746b-50ab-4d0f-864a-fe91dcaf1276] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 19:57:12,906 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,906 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,906 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,907 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:12,907 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:12,907 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:12,907 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:12,907 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,907 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:12,908 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,908 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,908 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,908 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,908 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,909 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:12,909 - WARNING - [main.py:888] - High response time detected: 6.20s for enhance_content_api
2025-07-22 19:57:43,935 - INFO - [main.py:7143] - Incoming request: {"request_id": "1a73b49e-d48f-441e-a013-3445cb3c1550", "timestamp": "2025-07-22T18:57:43.932529+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "AI in healthcare is going to make things super different for patients in a really good way! Imagine going to the doctor and they already know a lot about you because AI helps them keep all your information super organised. This means they can spend more time actually talking to you and figuring out the best way to help you feel better, instead of spending ages writing notes.[1][2] AI can also help doctors find sicknesses really early, even before you feel sick![3][4] It's like having a secret early warning system. And for people who have long-term illnesses, AI might help them manage their health better from home, maybe with special apps or devices that check on them.[5] Plus, AI can help doctors find new medicines faster, so people can get better treatments sooner.[2] It's like having lots of super-smart helpers all working together to make everyone healthier and happier!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-********-a9d2-44b1-a778-78455bae1f78", "chat_history": [{"role": "user", "content": "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", "timestamp": "2025-07-22T18:54:27.704Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in healthcare, like how it helps doctors find sicknesses and choose medicines. It's like you're saying AI is a super-tool that makes healthcare even better!\n\nNow, let's move on to the next question.\n\n**Question 2 of 5 (Level 3 Concept Exploration):** Can you think of any specific ways AI might help doctors figure out what's wrong with someone if they are feeling sick?", "timestamp": "2025-07-22T18:54:34.097Z"}, {"role": "user", "content": "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", "timestamp": "2025-07-22T18:55:02.823Z"}, {"role": "assistant", "content": "That's a brilliant way to put it, Andrea! \"Super detective\" and \"super-vision\" are perfect descriptions. You've nailed the idea of AI helping to spot things in medical images and by looking at lots of past patient information. It's exactly like a detective gathering clues to solve a case!\n\nLet's keep that detective spirit going for our next question.\n\n**Question 3 of 5 (Level 4 Application Scenario):** Imagine a doctor has a new patient who has a cough and a fever. How might AI help the doctor decide what medicine to give them?", "timestamp": "2025-07-22T18:55:09.815Z"}, {"role": "user", "content": "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", "timestamp": "2025-07-22T18:56:26.985Z"}, {"role": "assistant", "content": "Wow, Andrea, you're on fire with these analogies! \"Super-smart assistant\" and comparing to \"lots of other people\" is exactly right. And the idea of a \"giant library of medicines\" is a fantastic way to think about how AI can find the best treatment. You're really understanding how AI can help doctors make informed decisions!\n\nWe're almost there! Let's try this next one.\n\n**Question 4 of 5 (Level 5 Deeper Understanding):** Besides helping doctors find out what's wrong and what medicine to use, what else do you think AI could do in hospitals or for people who are sick?", "timestamp": "2025-07-22T18:56:34.331Z"}, {"role": "user", "content": "Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to give, AI can do lots of other cool things! It can help make sure patients get the right medicine at the right time, like a helpful reminder system. AI can also help doctors keep track of lots of patient information really well, almost like a super-organized filing cabinet. It can even help invent new medicines by looking at tiny details about how sicknesses work. And sometimes, AI can help robots do very careful jobs, like helping a surgeon during an operation, making it even steadier. It can also help people who need to stay home by chatting with them and checking how they are feeling. It's like having many helpers all working together to make people better!", "timestamp": "2025-07-22T18:57:06.264Z"}, {"role": "assistant", "content": "That's an absolutely brilliant summary, Andrea! You've hit on so many important areas: keeping track of information, helping invent new medicines, assisting in surgeries, and even checking in on people at home. Your description of AI as \"many helpers all working together\" is spot on! You're showing a really deep understanding of how AI can impact many parts of healthcare.\n\nJust one more question to go!\n\n**Question 5 of 5 (Level 6 Synthesis):** Thinking about everything we've discussed, how do you think AI in Healthcare might change things for patients in the future?", "timestamp": "2025-07-22T18:57:12.926Z"}]}}
2025-07-22 19:57:43,939 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 19:57:43,940 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FojRArwf7TCv4y3JoZYASQTKKeemWG0Zxr2u8rLS_3HvNj0ZMYI-0G7Xbxx3Yhp6l5qhe14xG2DAhf0vXXcHgss9zySkSFdJqJhUCVL5I9WvN8l9G6LqHsOIOxXc3rKImNtmJ6U38HeqI3grXrFtqbS8AGZA8gVhKt97seDdqmUoIJxTvRa9B_CoDHpRgoPtma8eSC-2Z6xEepuhE0cmy8pqmRCRODvn7pgZ_TnOTK0AiA-OiH-PcV0sU9jLg2wdFacFrdCvYzgL_lTphao7Yrm-W1XQgBYZ4WRMLvIsy6AYZpEbXBcM2vfuQRBl3sqVAw2Ji5b4XU77_645ujHrDw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '6530', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 19:57:43,940 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 1a73b49e-d48f-441e-a013-3445cb3c1550
2025-07-22 19:57:43,941 - INFO - [auth_decorator.py:74] - [1a73b49e-d48f-441e-a013-3445cb3c1550][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 19:57:43,941 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 19:57:43,941 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 19:57:43,941 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 19:57:43,942 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 19:57:43,942 - INFO - [auth_decorator.py:95] - [1a73b49e-d48f-441e-a013-3445cb3c1550][require_auth] Development mode detected - bypassing authentication
2025-07-22 19:57:43,942 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 19:57:43,942 - INFO - [auth_decorator.py:121] - [1a73b49e-d48f-441e-a013-3445cb3c1550][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 19:57:44,247 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:57:44,247 - INFO - [auth_decorator.py:164] - [1a73b49e-d48f-441e-a013-3445cb3c1550][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:57:44,248 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 19:57:44,250 - INFO - [main.py:7327] -
================================================================================
2025-07-22 19:57:44,250 - WARNING - [main.py:7328] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 19:57:44,251 - WARNING - [main.py:7329] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 19:57:44,252 - DEBUG - [main.py:7337] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Console output error: I/O operation on closed file.
2025-07-22 19:57:44,255 - INFO - [main.py:8179] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"AI in healthcare is going to make things super different for patients in a really good way! Imagine going to the doctor and they already know a lot about you because AI helps them keep all your information super organised. This means they can spend more time actually talking to you and figuring out the best way to help you feel better, instead of spending ages writing notes.[1][2] AI can also help doctors find sick...
2025-07-22 19:57:44,257 - INFO - [main.py:8181] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "AI in healthcare is going to make things super different for patients in a really good way! Imagine going to the doctor and they already know a lot about you because AI helps them keep all your information super organised. This means they can spend more time actually talking to you and figuring out the best way to help you feel better, instead of spending ages writing notes.[1][2] AI can also help doctors find sicknesses really early, even before you feel sick![3][4] It's like having a secret early warning system. And for people who have long-term illnesses, AI might help them manage their health better from home, maybe with special apps or devices that check on them.[5] Plus, AI can help doctors find new medicines faster, so people can get better treatments sooner.[2] It's like having lots of super-smart helpers all working together to make everyone healthier and happier!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-********-a9d2-44b1-a778-78455bae1f78', 'chat_history': [{'role': 'user', 'content': "Hello! I'm ready for my lesson!  AI in healthcare is like a super-smart helper for doctors and nurses. It can help them find sicknesses faster, like spotting a tiny bug in a picture. It can also help them guess what medicine might work best for someone, like picking the right crayon for a drawing. Sometimes, it can even help robots do tricky jobs, like helping a doctor during an operation. It's all about making people healthier and helping them feel better!", 'timestamp': '2025-07-22T18:54:27.704Z'}, {'role': 'assistant', 'content': "That's a fantastic start, Andrea! You've already grasped some really important ideas about AI in healthcare, like how it helps doctors find sicknesses and choose medicines. It's like you're saying AI is a super-tool that makes healthcare even better!\n\nNow, let's move on to the next question.\n\n**Question 2 of 5 (Level 3 Concept Exploration):** Can you think of any specific ways AI might help doctors figure out what's wrong with someone if they are feeling sick?", 'timestamp': '2025-07-22T18:54:34.097Z'}, {'role': 'user', 'content': "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", 'timestamp': '2025-07-22T18:55:02.823Z'}, {'role': 'assistant', 'content': 'That\'s a brilliant way to put it, Andrea! "Super detective" and "super-vision" are perfect descriptions. You\'ve nailed the idea of AI helping to spot things in medical images and by looking at lots of past patient information. It\'s exactly like a detective gathering clues to solve a case!\n\nLet\'s keep that detective spirit going for our next question.\n\n**Question 3 of 5 (Level 4 Application Scenario):** Imagine a doctor has a new patient who has a cough and a fever. How might AI help the doctor decide what medicine to give them?', 'timestamp': '2025-07-22T18:55:09.815Z'}, {'role': 'user', 'content': "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", 'timestamp': '2025-07-22T18:56:26.985Z'}, {'role': 'assistant', 'content': 'Wow, Andrea, you\'re on fire with these analogies! "Super-smart assistant" and comparing to "lots of other people" is exactly right. And the idea of a "giant library of medicines" is a fantastic way to think about how AI can find the best treatment. You\'re really understanding how AI can help doctors make informed decisions!\n\nWe\'re almost there! Let\'s try this next one.\n\n**Question 4 of 5 (Level 5 Deeper Understanding):** Besides helping doctors find out what\'s wrong and what medicine to use, what else do you think AI could do in hospitals or for people who are sick?', 'timestamp': '2025-07-22T18:56:34.331Z'}, {'role': 'user', 'content': "Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to give, AI can do lots of other cool things! It can help make sure patients get the right medicine at the right time, like a helpful reminder system. AI can also help doctors keep track of lots of patient information really well, almost like a super-organized filing cabinet. It can even help invent new medicines by looking at tiny details about how sicknesses work. And sometimes, AI can help robots do very careful jobs, like helping a surgeon during an operation, making it even steadier. It can also help people who need to stay home by chatting with them and checking how they are feeling. It's like having many helpers all working together to make people better!", 'timestamp': '2025-07-22T18:57:06.264Z'}, {'role': 'assistant', 'content': 'That\'s an absolutely brilliant summary, Andrea! You\'ve hit on so many important areas: keeping track of information, helping invent new medicines, assisting in surgeries, and even checking in on people at home. Your description of AI as "many helpers all working together" is spot on! You\'re showing a really deep understanding of how AI can impact many parts of healthcare.\n\nJust one more question to go!\n\n**Question 5 of 5 (Level 6 Synthesis):** Thinking about everything we\'ve discussed, how do you think AI in Healthcare might change things for patients in the future?', 'timestamp': '2025-07-22T18:57:12.926Z'}]}
2025-07-22 19:57:44,258 - INFO - [main.py:8183] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍  - Session ID from payload: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:44,258 - INFO - [main.py:8184] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 19:57:44,260 - INFO - [main.py:8185] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 19:57:44,260 - DEBUG - [main.py:8223] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:57:44,260 - INFO - [main.py:8224] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:57:44,260 - INFO - [main.py:8264] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Level not provided, determined from grade 'primary-5': 5
2025-07-22 19:57:44,547 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 19:57:44,547 - INFO - [main.py:8281] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 19:57:44,548 - INFO - [main.py:8282] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 19:57:44,548 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 19:57:45,058 - INFO - [main.py:8312] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 19:57:45,059 - INFO - [main.py:8375] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 19:57:45,059 - INFO - [main.py:8500] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ Successfully retrieved lesson from primary path
2025-07-22 19:57:45,059 - INFO - [main.py:8511] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 19:57:45,060 - INFO - [main.py:8550] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 19:57:45,060 - INFO - [main.py:4456] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 19:57:45,360 - INFO - [main.py:4522] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 19:57:45,360 - INFO - [main.py:4522] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 19:57:45,361 - INFO - [main.py:4522] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 19:57:45,361 - INFO - [main.py:4522] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 19:57:45,361 - INFO - [main.py:4522] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 19:57:45,362 - INFO - [main.py:4591] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 19:57:45,362 - DEBUG - [main.py:4605] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 19:57:45,363 - DEBUG - [main.py:4608] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 19:57:45,363 - DEBUG - [main.py:4609] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 19:57:45,363 - DEBUG - [main.py:4610] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 19:57:45,364 - INFO - [main.py:4614] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Calling Gemini API for module inference...
2025-07-22 19:57:45,996 - INFO - [main.py:4624] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Gemini API call completed in 0.63s. Raw response: 'ai_tools_and_applications'
2025-07-22 19:57:45,996 - DEBUG - [main.py:4646] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 19:57:45,997 - INFO - [main.py:4651] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 19:57:45,997 - INFO - [main.py:8584] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 19:57:45,997 - INFO - [main.py:8621] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 19:57:46,337 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 19:57:46,789 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 19:57:46,790 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:46,791 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 19:57:46,798 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q5
2025-07-22 19:57:46,801 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 19:57:46,802 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 19:57:46,802 - WARNING - [main.py:8685] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 SESSION STATE DEBUG:
2025-07-22 19:57:46,803 - WARNING - [main.py:8686] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   - Session exists: True
2025-07-22 19:57:46,803 - WARNING - [main.py:8687] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   - Current phase: smart_diagnostic_q5
2025-07-22 19:57:46,803 - WARNING - [main.py:8688] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'new_phase', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 19:57:46,805 - DEBUG - [main.py:8706] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 19:57:46,813 - DEBUG - [main.py:8707] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Retrieved Phase: 'smart_diagnostic_q5'
2025-07-22 19:57:46,813 - DEBUG - [main.py:8708] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Diagnostic Completed: False
2025-07-22 19:57:46,813 - DEBUG - [main.py:8709] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Assigned Level: None
2025-07-22 19:57:46,813 - WARNING - [main.py:8710] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔒 STATE PROTECTION: phase='smart_diagnostic_q5', diagnostic_done=False, level=None
2025-07-22 19:57:46,814 - INFO - [main.py:8742] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 19:57:46,814 - INFO - [main.py:8743] - [1a73b49e-d48f-441e-a013-3445cb3c1550] State protection not triggered
2025-07-22 19:57:46,814 - INFO - [main.py:8791] - [1a73b49e-d48f-441e-a013-3445cb3c1550]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 19:57:46,815 - INFO - [main.py:8792] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   assigned_level_for_teaching (session): None
2025-07-22 19:57:46,815 - INFO - [main.py:8793] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   latest_assessed_level (profile): None
2025-07-22 19:57:46,815 - INFO - [main.py:8794] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   teaching_level_for_returning_student: None
2025-07-22 19:57:46,816 - INFO - [main.py:8795] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   has_completed_diagnostic_before: False
2025-07-22 19:57:46,816 - INFO - [main.py:8796] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   is_first_encounter_for_module: True
2025-07-22 19:57:46,816 - WARNING - [main.py:8801] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 19:57:46,816 - INFO - [main.py:8809] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 PHASE INVESTIGATION:
2025-07-22 19:57:46,817 - INFO - [main.py:8810] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Retrieved from Firestore: 'smart_diagnostic_q5'
2025-07-22 19:57:46,817 - INFO - [main.py:8811] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 19:57:46,821 - INFO - [main.py:8812] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Is first encounter: True
2025-07-22 19:57:46,821 - INFO - [main.py:8813] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Diagnostic completed: False
2025-07-22 19:57:46,821 - INFO - [main.py:8819] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ Using stored phase from Firestore: 'smart_diagnostic_q5'
2025-07-22 19:57:46,822 - INFO - [main.py:8833] - [1a73b49e-d48f-441e-a013-3445cb3c1550] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 19:57:46,822 - INFO - [main.py:8835] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Final phase for AI logic: smart_diagnostic_q5
2025-07-22 19:57:46,823 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 19:57:46,825 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 19:57:46,826 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 19:57:46,826 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 19:57:46,826 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 19:57:46,827 - INFO - [main.py:8855] - [1a73b49e-d48f-441e-a013-3445cb3c1550] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 19:57:46,827 - INFO - [main.py:6144] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Diagnostic context validation passed
2025-07-22 19:57:46,827 - WARNING - [main.py:9007] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q5' for first encounter
2025-07-22 19:57:46,828 - INFO - [main.py:9030] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q5
2025-07-22 19:57:46,828 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q5'
2025-07-22 19:57:46,829 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q5'
2025-07-22 19:57:46,829 - INFO - [main.py:9042] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Robust context prepared successfully. Phase: smart_diagnostic_q5
2025-07-22 19:57:46,830 - DEBUG - [main.py:9043] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 19:57:46,830 - INFO - [main.py:9285] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q5
2025-07-22 19:57:46,831 - INFO - [main.py:9286] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎯 UNIFIED STATE MACHINE: Processing user query: AI in healthcare is going to make things super different for patients in a really good way! Imagine ...
2025-07-22 19:57:46,832 - WARNING - [main.py:9328] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ⚠️ Unknown phase 'smart_diagnostic_q5', enforcing unified pathway
2025-07-22 19:57:46,833 - WARNING - [main.py:9329] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ⚠️ Defaulting to teaching_start phase
2025-07-22 19:57:46,833 - INFO - [main.py:7513] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📚 TEACHING PHASE HANDLER: Processing teaching content
2025-07-22 19:57:46,834 - INFO - [main.py:7545] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🤖 AI INSTRUCTOR: Continuing teaching phase
2025-07-22 19:57:46,838 - INFO - [main.py:11163] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:46,839 - DEBUG - [main.py:11172] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Console output error: I/O operation on closed file.
2025-07-22 19:57:46,839 - INFO - [main.py:11205] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📤 Sending message to existing session: AI in healthcare is going to make things super dif...
2025-07-22 19:57:46,839 - INFO - [main.py:5965] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 💰 Sending message to existing session (NO API CALL)
2025-07-22 19:57:49,197 - INFO - [main.py:5970] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ Response received from session (NO API CALL COST)
2025-07-22 19:57:49,197 - INFO - [main.py:11212] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📥 Received response from session: Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉

**Diagnostic Resu...
2025-07-22 19:57:49,200 - INFO - [main.py:11303] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q5
2025-07-22 19:57:49,200 - INFO - [main.py:11321] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ DIAGNOSTIC COMPLETION DETECTED
2025-07-22 19:57:49,214 - INFO - [main.py:11346] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎯 EXTRACTED TEACHING LEVEL: 7
2025-07-22 19:57:49,215 - INFO - [main.py:11358] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🚀 DIAGNOSTIC → TEACHING TRANSITION PREPARED
2025-07-22 19:57:49,215 - INFO - [main.py:7573] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📊 TEACHING PROGRESS: 1 interactions
2025-07-22 19:57:49,215 - INFO - [main.py:7574] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📊 OBJECTIVES COVERAGE: 0.0%
2025-07-22 19:57:49,215 - INFO - [main.py:9342] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 19:57:49,215 - INFO - [main.py:5710] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔄 PHASE TRANSITION: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:49,215 - INFO - [phase_transition_integrity.py:153] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:49,218 - INFO - [phase_transition_integrity.py:235] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ TRANSITION VALIDATED: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:49,219 - INFO - [main.py:5718] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ VALID TRANSITION: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:49,219 - INFO - [main.py:9358] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:49,220 - INFO - [main.py:9359] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 19:57:50,041 - INFO - [main.py:9373] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ UNIFIED STATE MACHINE: Session state updated with phase teaching_start
2025-07-22 19:57:50,041 - INFO - [main.py:9383] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 19:57:50,042 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:57:50,042 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:57:50,042 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:57:50,042 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:57:50,043 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:57:50,043 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:57:50,043 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:57:50,043 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:57:50,043 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:57:50,044 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:57:50,044 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:57:50,044 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:57:50,044 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:57:50,045 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:57:50,045 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:57:50,046 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:57:50,046 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:57:50,046 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:57:50,047 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:57:50,047 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:57:50,047 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:57:50,048 - INFO - [intelligent_guardrails.py:130] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:57:50,049 - WARNING - [intelligent_guardrails.py:146] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🚨 QUIZ REQUEST BLOCKED: Teaching incomplete
2025-07-22 19:57:50,056 - INFO - [intelligent_guardrails.py:451] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ BLOCKING VIOLATIONS: Preventing quiz transition
2025-07-22 19:57:50,060 - INFO - [intelligent_guardrails.py:464] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ BACKEND INSTRUCTION (INTERNAL ONLY): Continue teaching until all completion criteria are met
2025-07-22 19:57:50,072 - INFO - [intelligent_guardrails.py:512] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📝 STUDENT-FRIENDLY CONTINUATION: Generated encouraging message instead of backend instruction
2025-07-22 19:57:50,073 - INFO - [intelligent_guardrails.py:183] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ GUARDRAILS RESULT: Valid=False, Violations=1
2025-07-22 19:57:50,073 - INFO - [intelligent_guardrails.py:675] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ GUARDRAILS APPLIED: Valid=False, Violations=1, Teaching Complete=False
2025-07-22 19:57:50,073 - INFO - [main.py:9485] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ GUARDRAIL VIOLATIONS: 1 found
2025-07-22 19:57:50,075 - INFO - [main.py:9490] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️   - BLOCKING: teaching_incomplete_quiz_block - Quiz transition blocked - teaching not complete
2025-07-22 19:57:50,075 - WARNING - [main.py:9496] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ BLOCKING VIOLATION: Preventing inappropriate progression
2025-07-22 19:57:50,075 - INFO - [main.py:9515] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ GUARDRAILS APPLIED: Valid=False, Enhanced=True
2025-07-22 19:57:50,075 - WARNING - [main.py:9563] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🤖 AI RESPONSE RECEIVED:
2025-07-22 19:57:50,076 - WARNING - [main.py:9564] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🤖   - Content length: 1782 chars
2025-07-22 19:57:50,076 - WARNING - [main.py:9565] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🤖   - State updates: {'new_phase': 'teaching_start', 'diagnostic_completed_this_session': True, 'assigned_level_for_teaching': 7, 'current_session_working_level': 7, 'latest_assessed_level_for_module': 7, 'diagnostic_complete': True, 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:57:50,076 - WARNING - [main.py:9566] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🤖   - Raw state block: None...
2025-07-22 19:57:50,077 - DEBUG - [main.py:10099] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 19:57:50,077 - DEBUG - [main.py:10100] - 🤖   Content: Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉

**Diagnostic Resu...
2025-07-22 19:57:50,077 - DEBUG - [main.py:10101] - 🤖   State: {'new_phase': 'teaching_start', 'diagnostic_completed_this_session': True, 'assigned_level_for_teaching': 7, 'current_session_working_level': 7, 'latest_assessed_level_for_module': 7, 'diagnostic_complete': True, 'teaching_interactions': 1, 'current_phase': 'teaching_start', 'unified_pathway_enforced': True}
2025-07-22 19:57:50,078 - INFO - [main.py:10127] - [1a73b49e-d48f-441e-a013-3445cb3c1550] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 19:57:50,078 - INFO - [main.py:10128] - [1a73b49e-d48f-441e-a013-3445cb3c1550] CURRENT PHASE DETERMINATION: AI=teaching_start, Session=smart_diagnostic_q5, Final=teaching_start
2025-07-22 19:57:50,391 - WARNING - [main.py:10230] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q5', new_phase='teaching_start'
2025-07-22 19:57:50,391 - INFO - [main.py:6365] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI state update validation passed: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:50,392 - WARNING - [main.py:10239] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 19:57:50,392 - WARNING - [main.py:10258] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔄 PHASE TRANSITION: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:50,393 - INFO - [main.py:10289] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 19:57:50,393 - INFO - [main.py:10294] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 19:57:50,393 - INFO - [phase_transition_integrity.py:330] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:50,393 - INFO - [phase_transition_integrity.py:293] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📸 DATA SNAPSHOT CREATED: Session fallback-********-a9d2-44b1-a778-78455bae1f78, Phase smart_diagnostic_q5
2025-07-22 19:57:50,394 - WARNING - [phase_transition_integrity.py:342] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 19:57:50,394 - WARNING - [phase_transition_integrity.py:343] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 19:57:50,394 - WARNING - [phase_transition_integrity.py:344] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   - assigned_level_for_teaching in state_updates: True
2025-07-22 19:57:50,396 - WARNING - [phase_transition_integrity.py:345] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 19:57:50,397 - WARNING - [phase_transition_integrity.py:347] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   - assigned_level_for_teaching value: 7
2025-07-22 19:57:50,397 - WARNING - [phase_transition_integrity.py:348] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   - state_updates keys: ['new_phase', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'current_session_working_level', 'latest_assessed_level_for_module', 'diagnostic_complete', 'teaching_interactions', 'current_phase', 'unified_pathway_enforced']
2025-07-22 19:57:50,398 - WARNING - [phase_transition_integrity.py:349] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 19:57:50,398 - INFO - [phase_transition_integrity.py:153] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:50,399 - INFO - [phase_transition_integrity.py:235] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ TRANSITION VALIDATED: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:50,399 - INFO - [phase_transition_integrity.py:360] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ TRANSITION APPLIED: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:50,400 - DEBUG - [phase_transition_integrity.py:847] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 19:57:50,400 - DEBUG - [phase_transition_integrity.py:880] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📝 TRANSITION RECORDED: smart_diagnostic_q5 → teaching_start (valid)
2025-07-22 19:57:50,400 - INFO - [phase_transition_integrity.py:406] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching_start
2025-07-22 19:57:50,401 - WARNING - [main.py:10336] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔧 STATE MACHINE: Invalid transition corrected to teaching
2025-07-22 19:57:50,402 - INFO - [main.py:10367] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ TRANSITION VALIDATED: smart_diagnostic_q5 → teaching
2025-07-22 19:57:50,402 - WARNING - [main.py:10372] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 19:57:50,403 - WARNING - [main.py:10373] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   1. Input phase: 'smart_diagnostic_q5'
2025-07-22 19:57:50,403 - WARNING - [main.py:10374] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 19:57:50,404 - WARNING - [main.py:10375] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 19:57:50,405 - WARNING - [main.py:10376] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   4. Integrity validation result: 'valid'
2025-07-22 19:57:50,406 - WARNING - [main.py:10377] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍   5. Final phase to save: 'teaching'
2025-07-22 19:57:50,407 - WARNING - [main.py:10380] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 💾 FINAL STATE APPLICATION:
2025-07-22 19:57:50,407 - WARNING - [main.py:10381] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 💾   - Current phase input: 'smart_diagnostic_q5'
2025-07-22 19:57:50,407 - WARNING - [main.py:10382] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 💾   - Validated state updates: 28 fields
2025-07-22 19:57:50,408 - WARNING - [main.py:10383] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 💾   - Final phase to save: 'teaching'
2025-07-22 19:57:50,408 - WARNING - [main.py:10384] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 💾   - Phase change: True
2025-07-22 19:57:50,409 - WARNING - [main.py:10385] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 💾   - Integrity applied: True
2025-07-22 19:57:50,409 - INFO - [main.py:6397] - [1a73b49e-d48f-441e-a013-3445cb3c1550] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 19:57:50,409 - INFO - [main.py:6398] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Phase transition: smart_diagnostic_q5 -> teaching_start
2025-07-22 19:57:50,410 - INFO - [main.py:6399] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Current level: 2
2025-07-22 19:57:50,410 - INFO - [main.py:6400] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Question index: 0
2025-07-22 19:57:50,410 - INFO - [main.py:6401] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   First encounter: True
2025-07-22 19:57:50,410 - INFO - [main.py:6406] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Answers collected: 0
2025-07-22 19:57:50,411 - INFO - [main.py:6407] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Levels failed: 0
2025-07-22 19:57:50,411 - INFO - [main.py:6365] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI state update validation passed: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:50,412 - INFO - [main.py:6411] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   State update valid: True
2025-07-22 19:57:50,412 - INFO - [main.py:6418] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Diagnostic complete: True
2025-07-22 19:57:50,413 - INFO - [main.py:6420] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   Assigned level: 7
2025-07-22 19:57:50,413 - WARNING - [main.py:10398] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔧 PROBING LEVEL SYNC: Set probing level to 7 to match assigned teaching level
2025-07-22 19:57:50,413 - WARNING - [main.py:10403] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 19:57:50,414 - INFO - [main.py:10412] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 DEBUG state_updates_from_ai teaching_interactions: 1
2025-07-22 19:57:50,414 - INFO - [main.py:10413] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 19:57:50,414 - WARNING - [main.py:10416] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ INTERACTION COUNT PROTECTION: AI tried to set 1, backend preserving 0
2025-07-22 19:57:50,415 - WARNING - [main.py:10417] - [1a73b49e-d48f-441e-a013-3445cb3c1550]   This prevents teaching interaction count resets and infinite loops
2025-07-22 19:57:50,908 - WARNING - [main.py:10463] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 19:57:50,909 - WARNING - [main.py:10464] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅   - Phase: teaching
2025-07-22 19:57:50,909 - WARNING - [main.py:10465] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅   - Probing Level: 7
2025-07-22 19:57:50,909 - WARNING - [main.py:10466] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅   - Question Index: 0
2025-07-22 19:57:50,909 - WARNING - [main.py:10467] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅   - Diagnostic Complete: True
2025-07-22 19:57:50,910 - WARNING - [main.py:10474] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅   - Quiz Questions Saved: 0
2025-07-22 19:57:50,910 - WARNING - [main.py:10475] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅   - Quiz Answers Saved: 0
2025-07-22 19:57:50,910 - WARNING - [main.py:10476] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅   - Quiz Started: False
2025-07-22 19:57:50,910 - DEBUG - [main.py:10525] - 🔥 STATE SAVED - Session: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: teaching
2025-07-22 19:57:50,911 - DEBUG - [main.py:10526] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 19:57:51,736 - DEBUG - [main.py:10584] - ✅ SESSION UPDATED - ID: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: teaching
2025-07-22 19:57:51,738 - DEBUG - [main.py:10585] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q5 → teaching
2025-07-22 19:57:51,739 - INFO - [main.py:10591] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ Updated existing session document: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:51,739 - INFO - [main.py:18505] - [1a73b49e-d48f-441e-a013-3445cb3c1550] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 19:57:51,739 - DEBUG - [main.py:4943] - [1a73b49e-d48f-441e-a013-3445cb3c1550] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 19:57:51,740 - DEBUG - [main.py:10678] - [1a73b49e-d48f-441e-a013-3445cb3c1550] No final assessment data found in AI response
2025-07-22 19:57:51,740 - DEBUG - [main.py:10701] - [1a73b49e-d48f-441e-a013-3445cb3c1550] No lesson completion detected (Phase: teaching_start, Complete: False)
2025-07-22 19:57:51,741 - DEBUG - [main.py:10725] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 19:57:51,741 - DEBUG - [main.py:10726] - 🔒   Current Phase: smart_diagnostic_q5
2025-07-22 19:57:51,742 - DEBUG - [main.py:10727] - 🔒   Final Phase: teaching_start
2025-07-22 19:57:51,743 - DEBUG - [main.py:10728] - 🔒   Diagnostic Complete: True
2025-07-22 19:57:51,743 - DEBUG - [main.py:10729] - 🔒   Assigned Level: 7
2025-07-22 19:57:51,745 - INFO - [main.py:10802] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 19:57:51,749 - INFO - [main.py:10836] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 19:57:51,750 - INFO - [main.py:10844] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 19:57:51,753 - INFO - [main.py:10849] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 19:57:51,755 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:57:51,755 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:57:51,756 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:57:51,756 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:57:51,757 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:57:51,757 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:57:51,758 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:57:51,758 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:57:51,759 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:57:51,759 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:57:51,759 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:57:51,760 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:57:51,760 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:57:51,760 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:57:51,761 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:57:51,761 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:57:51,762 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:57:51,762 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:57:51,762 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:57:51,763 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:57:51,763 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:57:51,764 - INFO - [main.py:10858] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 19:57:51,767 - INFO - [intelligent_guardrails.py:130] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:57:51,769 - WARNING - [intelligent_guardrails.py:146] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🚨 QUIZ REQUEST BLOCKED: Teaching incomplete
2025-07-22 19:57:51,774 - INFO - [intelligent_guardrails.py:451] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ BLOCKING VIOLATIONS: Preventing quiz transition
2025-07-22 19:57:51,777 - INFO - [intelligent_guardrails.py:464] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ BACKEND INSTRUCTION (INTERNAL ONLY): Continue teaching until all completion criteria are met
2025-07-22 19:57:51,777 - INFO - [intelligent_guardrails.py:512] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 📝 STUDENT-FRIENDLY CONTINUATION: Generated encouraging message instead of backend instruction
2025-07-22 19:57:51,778 - INFO - [intelligent_guardrails.py:183] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ GUARDRAILS RESULT: Valid=False, Violations=1
2025-07-22 19:57:51,778 - INFO - [main.py:10920] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🛡️ GUARDRAILS VALIDATION: Valid=False, Violations=1
2025-07-22 19:57:51,779 - WARNING - [main.py:10924] - [1a73b49e-d48f-441e-a013-3445cb3c1550] 🚨 GUARDRAIL VIOLATION: teaching_incomplete_quiz_block - Quiz transition blocked - teaching not complete (Severity: GuardrailSeverity.BLOCKING)
2025-07-22 19:57:51,780 - INFO - [main.py:10945] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✨ GUARDRAILS ENHANCED RESPONSE: Applied pedagogical improvements
2025-07-22 19:57:51,781 - INFO - [main.py:10948] - [1a73b49e-d48f-441e-a013-3445cb3c1550] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 19:57:51,782 - DEBUG - [main.py:11005] - 🎯 RESPONSE READY:
2025-07-22 19:57:51,782 - DEBUG - [main.py:11006] - 🎯   Session: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:57:51,783 - DEBUG - [main.py:11007] - 🎯   Phase: smart_diagnostic_q5 → teaching_start
2025-07-22 19:57:51,784 - DEBUG - [main.py:11008] - 🎯   Content: Excellent! You've completed all 5 questions of the...
2025-07-22 19:57:51,784 - DEBUG - [main.py:11009] - 🎯   Request ID: 1a73b49e-d48f-441e-a013-3445cb3c1550
2025-07-22 19:57:51,788 - INFO - [main.py:11015] - [1a73b49e-d48f-441e-a013-3445cb3c1550] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 19:57:51,794 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,797 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,799 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,804 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:51,805 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:51,806 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:51,807 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:51,807 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,808 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:57:51,808 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,809 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,809 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,811 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,814 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,816 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:57:51,829 - WARNING - [main.py:888] - High response time detected: 7.58s for enhance_content_api
2025-07-22 19:59:15,013 - INFO - [main.py:7143] - Incoming request: {"request_id": "5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229", "timestamp": "2025-07-22T18:59:15.011862+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "That's so cool! I'm excited to learn more!  I don't have any questions right now, but I'm ready for the next part of the lesson! It sounds like AI is going to be a really big help in making people healthy in the future.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-********-a9d2-44b1-a778-78455bae1f78", "chat_history": [{"role": "user", "content": "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", "timestamp": "2025-07-22T18:55:02.823Z"}, {"role": "assistant", "content": "That's a brilliant way to put it, Andrea! \"Super detective\" and \"super-vision\" are perfect descriptions. You've nailed the idea of AI helping to spot things in medical images and by looking at lots of past patient information. It's exactly like a detective gathering clues to solve a case!\n\nLet's keep that detective spirit going for our next question.\n\n**Question 3 of 5 (Level 4 Application Scenario):** Imagine a doctor has a new patient who has a cough and a fever. How might AI help the doctor decide what medicine to give them?", "timestamp": "2025-07-22T18:55:09.815Z"}, {"role": "user", "content": "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", "timestamp": "2025-07-22T18:56:26.985Z"}, {"role": "assistant", "content": "Wow, Andrea, you're on fire with these analogies! \"Super-smart assistant\" and comparing to \"lots of other people\" is exactly right. And the idea of a \"giant library of medicines\" is a fantastic way to think about how AI can find the best treatment. You're really understanding how AI can help doctors make informed decisions!\n\nWe're almost there! Let's try this next one.\n\n**Question 4 of 5 (Level 5 Deeper Understanding):** Besides helping doctors find out what's wrong and what medicine to use, what else do you think AI could do in hospitals or for people who are sick?", "timestamp": "2025-07-22T18:56:34.331Z"}, {"role": "user", "content": "Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to give, AI can do lots of other cool things! It can help make sure patients get the right medicine at the right time, like a helpful reminder system. AI can also help doctors keep track of lots of patient information really well, almost like a super-organized filing cabinet. It can even help invent new medicines by looking at tiny details about how sicknesses work. And sometimes, AI can help robots do very careful jobs, like helping a surgeon during an operation, making it even steadier. It can also help people who need to stay home by chatting with them and checking how they are feeling. It's like having many helpers all working together to make people better!", "timestamp": "2025-07-22T18:57:06.264Z"}, {"role": "assistant", "content": "That's an absolutely brilliant summary, Andrea! You've hit on so many important areas: keeping track of information, helping invent new medicines, assisting in surgeries, and even checking in on people at home. Your description of AI as \"many helpers all working together\" is spot on! You're showing a really deep understanding of how AI can impact many parts of healthcare.\n\nJust one more question to go!\n\n**Question 5 of 5 (Level 6 Synthesis):** Thinking about everything we've discussed, how do you think AI in Healthcare might change things for patients in the future?", "timestamp": "2025-07-22T18:57:12.926Z"}, {"role": "user", "content": "AI in healthcare is going to make things super different for patients in a really good way! Imagine going to the doctor and they already know a lot about you because AI helps them keep all your information super organised. This means they can spend more time actually talking to you and figuring out the best way to help you feel better, instead of spending ages writing notes.[1][2] AI can also help doctors find sicknesses really early, even before you feel sick![3][4] It's like having a secret early warning system. And for people who have long-term illnesses, AI might help them manage their health better from home, maybe with special apps or devices that check on them.[5] Plus, AI can help doctors find new medicines faster, so people can get better treatments sooner.[2] It's like having lots of super-smart helpers all working together to make everyone healthier and happier!", "timestamp": "2025-07-22T18:57:43.618Z"}, {"role": "assistant", "content": "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! \ud83c\udf89\n\n**Diagnostic Results:** \u2705 Questions Completed: 5/5\n\n\ud83d\udcca **Recommended Teaching Level: 7**\n\nBased on your insightful answers and fantastic analogies, I've determined that we should start teaching at Level 7. You've already shown a great grasp of the core concepts!\n\nNow let's begin our exciting lesson on AI in Healthcare! \ud83d\ude80\n\nAt its heart, **AI in Healthcare** means using smart computer programs to help doctors, nurses, and scientists do their jobs better and make people healthier. Think of it as giving our healthcare heroes super-powers!\n\nWe've already touched on how AI is **used** in **Healthcare** for **disease diagnosis**. This is when AI helps doctors figure out what illness a person has. For example, AI can look at X-rays or scans and spot tiny signs of **disease** that a human eye might miss. It's like having a super-powered magnifying glass! It can also help predict if someone might get a certain **disease** in the future by looking at their health history.\n\nNow, let's dive a little deeper into how AI helps **discover** new **drug**s. This is a really exciting area! Imagine trying to find the perfect key to unlock a door, but there are millions of keys! That's a bit like finding new medicines. AI can help by looking at tons of information about diseases and how different chemicals might affect them. It can quickly test many different ideas to find promising new **drug**s that could help people. This can make the process of finding new medicines much faster and more efficient.\n\nDo you have any initial questions about how AI helps with disease diagnosis or drug discovery?\n\nI can see you're learning, Andrea! Let's cover a few more important points about AI in Healthcare.\n\nI can see you're learning, Student! Let's cover a few more important points about AI in Healthcare.", "timestamp": "2025-07-22T18:57:51.924Z"}]}}
2025-07-22 19:59:15,015 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 19:59:15,016 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FojRArwf7TCv4y3JoZYASQTKKeemWG0Zxr2u8rLS_3HvNj0ZMYI-0G7Xbxx3Yhp6l5qhe14xG2DAhf0vXXcHgss9zySkSFdJqJhUCVL5I9WvN8l9G6LqHsOIOxXc3rKImNtmJ6U38HeqI3grXrFtqbS8AGZA8gVhKt97seDdqmUoIJxTvRa9B_CoDHpRgoPtma8eSC-2Z6xEepuhE0cmy8pqmRCRODvn7pgZ_TnOTK0AiA-OiH-PcV0sU9jLg2wdFacFrdCvYzgL_lTphao7Yrm-W1XQgBYZ4WRMLvIsy6AYZpEbXBcM2vfuQRBl3sqVAw2Ji5b4XU77_645ujHrDw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '7734', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 19:59:15,016 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229
2025-07-22 19:59:15,016 - INFO - [auth_decorator.py:74] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 19:59:15,016 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 19:59:15,016 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 19:59:15,017 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 19:59:15,017 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 19:59:15,017 - INFO - [auth_decorator.py:95] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229][require_auth] Development mode detected - bypassing authentication
2025-07-22 19:59:15,018 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 19:59:15,018 - INFO - [auth_decorator.py:121] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 19:59:15,306 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:59:15,306 - INFO - [auth_decorator.py:164] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 19:59:15,307 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 19:59:15,308 - INFO - [main.py:7327] -
================================================================================
2025-07-22 19:59:15,308 - WARNING - [main.py:7328] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 19:59:15,309 - WARNING - [main.py:7329] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 19:59:15,309 - DEBUG - [main.py:7337] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Console output error: I/O operation on closed file.
2025-07-22 19:59:15,309 - INFO - [main.py:8179] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"That's so cool! I'm excited to learn more!  I don't have any questions right now, but I'm ready for the next part of the lesson! It sounds like AI is going to be a really big help in making people healthy in the future.","country":"Nigeria","curriculum":"National Curriculum","grade":"primary-5","subject":"Artificial Intelligence","session_id":"fallback-********-a9d2-44b1-a778-78455bae1f78","chat_history":[{"role":"...
2025-07-22 19:59:15,311 - INFO - [main.py:8181] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "That's so cool! I'm excited to learn more!  I don't have any questions right now, but I'm ready for the next part of the lesson! It sounds like AI is going to be a really big help in making people healthy in the future.", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-********-a9d2-44b1-a778-78455bae1f78', 'chat_history': [{'role': 'user', 'content': "Absolutely! AI can be like a super detective for doctors when someone is sick. It can look at special pictures of the body, like X-rays or scans, and spot things that might be too small for a person to see easily.[1][2] It can also read lots and lots of information from when people have visited the doctor before, like a giant storybook, to find clues about what might be making someone feel unwell.[3][4] Sometimes, it's really good at finding very rare sicknesses that doctors might not see very often, helping to figure out what's wrong much faster.[5][6] It's like AI has super-vision and super-reading skills to help doctors solve the mystery of why someone is sick!", 'timestamp': '2025-07-22T18:55:02.823Z'}, {'role': 'assistant', 'content': 'That\'s a brilliant way to put it, Andrea! "Super detective" and "super-vision" are perfect descriptions. You\'ve nailed the idea of AI helping to spot things in medical images and by looking at lots of past patient information. It\'s exactly like a detective gathering clues to solve a case!\n\nLet\'s keep that detective spirit going for our next question.\n\n**Question 3 of 5 (Level 4 Application Scenario):** Imagine a doctor has a new patient who has a cough and a fever. How might AI help the doctor decide what medicine to give them?', 'timestamp': '2025-07-22T18:55:09.815Z'}, {'role': 'user', 'content': "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", 'timestamp': '2025-07-22T18:56:26.985Z'}, {'role': 'assistant', 'content': 'Wow, Andrea, you\'re on fire with these analogies! "Super-smart assistant" and comparing to "lots of other people" is exactly right. And the idea of a "giant library of medicines" is a fantastic way to think about how AI can find the best treatment. You\'re really understanding how AI can help doctors make informed decisions!\n\nWe\'re almost there! Let\'s try this next one.\n\n**Question 4 of 5 (Level 5 Deeper Understanding):** Besides helping doctors find out what\'s wrong and what medicine to use, what else do you think AI could do in hospitals or for people who are sick?', 'timestamp': '2025-07-22T18:56:34.331Z'}, {'role': 'user', 'content': "Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to give, AI can do lots of other cool things! It can help make sure patients get the right medicine at the right time, like a helpful reminder system. AI can also help doctors keep track of lots of patient information really well, almost like a super-organized filing cabinet. It can even help invent new medicines by looking at tiny details about how sicknesses work. And sometimes, AI can help robots do very careful jobs, like helping a surgeon during an operation, making it even steadier. It can also help people who need to stay home by chatting with them and checking how they are feeling. It's like having many helpers all working together to make people better!", 'timestamp': '2025-07-22T18:57:06.264Z'}, {'role': 'assistant', 'content': 'That\'s an absolutely brilliant summary, Andrea! You\'ve hit on so many important areas: keeping track of information, helping invent new medicines, assisting in surgeries, and even checking in on people at home. Your description of AI as "many helpers all working together" is spot on! You\'re showing a really deep understanding of how AI can impact many parts of healthcare.\n\nJust one more question to go!\n\n**Question 5 of 5 (Level 6 Synthesis):** Thinking about everything we\'ve discussed, how do you think AI in Healthcare might change things for patients in the future?', 'timestamp': '2025-07-22T18:57:12.926Z'}, {'role': 'user', 'content': "AI in healthcare is going to make things super different for patients in a really good way! Imagine going to the doctor and they already know a lot about you because AI helps them keep all your information super organised. This means they can spend more time actually talking to you and figuring out the best way to help you feel better, instead of spending ages writing notes.[1][2] AI can also help doctors find sicknesses really early, even before you feel sick![3][4] It's like having a secret early warning system. And for people who have long-term illnesses, AI might help them manage their health better from home, maybe with special apps or devices that check on them.[5] Plus, AI can help doctors find new medicines faster, so people can get better treatments sooner.[2] It's like having lots of super-smart helpers all working together to make everyone healthier and happier!", 'timestamp': '2025-07-22T18:57:43.618Z'}, {'role': 'assistant', 'content': "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉\n\n**Diagnostic Results:** ✅ Questions Completed: 5/5\n\n📊 **Recommended Teaching Level: 7**\n\nBased on your insightful answers and fantastic analogies, I've determined that we should start teaching at Level 7. You've already shown a great grasp of the core concepts!\n\nNow let's begin our exciting lesson on AI in Healthcare! 🚀\n\nAt its heart, **AI in Healthcare** means using smart computer programs to help doctors, nurses, and scientists do their jobs better and make people healthier. Think of it as giving our healthcare heroes super-powers!\n\nWe've already touched on how AI is **used** in **Healthcare** for **disease diagnosis**. This is when AI helps doctors figure out what illness a person has. For example, AI can look at X-rays or scans and spot tiny signs of **disease** that a human eye might miss. It's like having a super-powered magnifying glass! It can also help predict if someone might get a certain **disease** in the future by looking at their health history.\n\nNow, let's dive a little deeper into how AI helps **discover** new **drug**s. This is a really exciting area! Imagine trying to find the perfect key to unlock a door, but there are millions of keys! That's a bit like finding new medicines. AI can help by looking at tons of information about diseases and how different chemicals might affect them. It can quickly test many different ideas to find promising new **drug**s that could help people. This can make the process of finding new medicines much faster and more efficient.\n\nDo you have any initial questions about how AI helps with disease diagnosis or drug discovery?\n\nI can see you're learning, Andrea! Let's cover a few more important points about AI in Healthcare.\n\nI can see you're learning, Student! Let's cover a few more important points about AI in Healthcare.", 'timestamp': '2025-07-22T18:57:51.924Z'}]}
2025-07-22 19:59:15,311 - INFO - [main.py:8183] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍  - Session ID from payload: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:59:15,312 - INFO - [main.py:8184] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 19:59:15,312 - INFO - [main.py:8185] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 19:59:15,312 - DEBUG - [main.py:8223] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:59:15,312 - INFO - [main.py:8224] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 19:59:15,313 - INFO - [main.py:8264] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Level not provided, determined from grade 'primary-5': 5
2025-07-22 19:59:16,073 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 19:59:16,074 - INFO - [main.py:8281] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 19:59:16,074 - INFO - [main.py:8282] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 19:59:16,074 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 19:59:16,610 - INFO - [main.py:8312] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 19:59:16,610 - INFO - [main.py:8375] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 19:59:16,610 - INFO - [main.py:8500] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ Successfully retrieved lesson from primary path
2025-07-22 19:59:16,611 - INFO - [main.py:8511] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 19:59:16,611 - INFO - [main.py:8550] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 19:59:16,612 - INFO - [main.py:4456] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 19:59:16,952 - INFO - [main.py:4522] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 19:59:16,952 - INFO - [main.py:4522] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 19:59:16,952 - INFO - [main.py:4522] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 19:59:16,953 - INFO - [main.py:4522] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 19:59:16,953 - INFO - [main.py:4522] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 19:59:16,953 - INFO - [main.py:4591] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 19:59:16,953 - DEBUG - [main.py:4605] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 19:59:16,954 - DEBUG - [main.py:4608] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 19:59:16,954 - DEBUG - [main.py:4609] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 19:59:16,954 - DEBUG - [main.py:4610] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 19:59:16,954 - INFO - [main.py:4614] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Calling Gemini API for module inference...
2025-07-22 19:59:17,403 - INFO - [main.py:4624] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Gemini API call completed in 0.45s. Raw response: 'ai_tools_and_applications'
2025-07-22 19:59:17,404 - DEBUG - [main.py:4646] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 19:59:17,405 - INFO - [main.py:4651] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 19:59:17,406 - INFO - [main.py:8584] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 19:59:17,408 - INFO - [main.py:8621] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 19:59:17,701 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 19:59:18,190 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 19:59:18,190 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:59:18,191 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 19:59:18,191 - DEBUG - [main.py:8677] - 🔍   - Current Phase: teaching
2025-07-22 19:59:18,192 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 7
2025-07-22 19:59:18,192 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 19:59:18,192 - WARNING - [main.py:8685] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 SESSION STATE DEBUG:
2025-07-22 19:59:18,192 - WARNING - [main.py:8686] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍   - Session exists: True
2025-07-22 19:59:18,193 - WARNING - [main.py:8687] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍   - Current phase: teaching
2025-07-22 19:59:18,193 - WARNING - [main.py:8688] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'new_phase', 'quiz_performance', 'current_probing_level_number', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'diagnostic_complete', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 19:59:18,194 - DEBUG - [main.py:8706] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 19:59:18,195 - DEBUG - [main.py:8707] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Retrieved Phase: 'teaching'
2025-07-22 19:59:18,195 - DEBUG - [main.py:8708] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Diagnostic Completed: True
2025-07-22 19:59:18,196 - DEBUG - [main.py:8709] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Assigned Level: 7
2025-07-22 19:59:18,196 - WARNING - [main.py:8710] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔒 STATE PROTECTION: phase='teaching', diagnostic_done=True, level=7
2025-07-22 19:59:18,197 - INFO - [main.py:8739] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ State is valid - no correction needed
2025-07-22 19:59:18,198 - INFO - [main.py:8740] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ State validation passed: teaching
2025-07-22 19:59:18,199 - WARNING - [main.py:8789] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ⚠️ No latest assessed level in profile, using session state: 7
2025-07-22 19:59:18,199 - INFO - [main.py:8791] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 19:59:18,200 - INFO - [main.py:8792] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   assigned_level_for_teaching (session): 7
2025-07-22 19:59:18,200 - INFO - [main.py:8793] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   latest_assessed_level (profile): None
2025-07-22 19:59:18,200 - INFO - [main.py:8794] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   teaching_level_for_returning_student: 7
2025-07-22 19:59:18,201 - INFO - [main.py:8795] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   has_completed_diagnostic_before: True
2025-07-22 19:59:18,201 - INFO - [main.py:8796] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   is_first_encounter_for_module: False
2025-07-22 19:59:18,202 - INFO - [main.py:8806] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ RETURNING STUDENT: Using existing diagnostic state - diagnostic_completed=True
2025-07-22 19:59:18,203 - INFO - [main.py:8809] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 PHASE INVESTIGATION:
2025-07-22 19:59:18,204 - INFO - [main.py:8810] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Retrieved from Firestore: 'teaching'
2025-07-22 19:59:18,204 - INFO - [main.py:8811] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 19:59:18,204 - INFO - [main.py:8812] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Is first encounter: False
2025-07-22 19:59:18,205 - INFO - [main.py:8813] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Diagnostic completed: True
2025-07-22 19:59:18,205 - WARNING - [main.py:8821] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ⚠️ Invalid phase format: 'teaching', using default
2025-07-22 19:59:18,205 - INFO - [main.py:8833] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 19:59:18,206 - INFO - [main.py:8835] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Final phase for AI logic: smart_diagnostic_start
2025-07-22 19:59:18,206 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 19:59:18,207 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 19:59:18,208 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 19:59:18,208 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 19:59:18,209 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 19:59:18,209 - INFO - [main.py:6144] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Diagnostic context validation passed
2025-07-22 19:59:18,209 - INFO - [main.py:9020] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Phase determination: smart_diagnostic_start -> teaching
2025-07-22 19:59:18,210 - INFO - [main.py:9030] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Skipping diagnostic context enhancement for non-diagnostic phase: teaching
2025-07-22 19:59:18,210 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'teaching'
2025-07-22 19:59:18,211 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'teaching'
2025-07-22 19:59:18,211 - INFO - [main.py:9042] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Robust context prepared successfully. Phase: teaching
2025-07-22 19:59:18,211 - DEBUG - [main.py:9043] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 19:59:18,212 - INFO - [main.py:9285] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 UNIFIED STATE MACHINE: Current phase = teaching
2025-07-22 19:59:18,212 - INFO - [main.py:9286] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 UNIFIED STATE MACHINE: Processing user query: That's so cool! I'm excited to learn more!  I don't have any questions right now, but I'm ready for ...
2025-07-22 19:59:18,213 - INFO - [main.py:9297] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 ROUTING: Teaching phase handler
2025-07-22 19:59:18,213 - INFO - [main.py:7513] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📚 TEACHING PHASE HANDLER: Processing teaching content
2025-07-22 19:59:18,213 - INFO - [main.py:7545] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🤖 AI INSTRUCTOR: Continuing teaching phase
2025-07-22 19:59:18,214 - INFO - [main.py:11163] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:59:18,214 - DEBUG - [main.py:11172] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Console output error: I/O operation on closed file.
2025-07-22 19:59:18,214 - INFO - [main.py:11205] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📤 Sending message to existing session: That's so cool! I'm excited to learn more!  I don'...
2025-07-22 19:59:18,215 - INFO - [main.py:5965] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 💰 Sending message to existing session (NO API CALL)
2025-07-22 19:59:19,787 - INFO - [main.py:5970] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ Response received from session (NO API CALL COST)
2025-07-22 19:59:19,788 - INFO - [main.py:11212] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📥 Received response from session: That's wonderful to hear, Andrea! You're absolutely right, AI is set to be a huge help in making peo...
2025-07-22 19:59:19,790 - INFO - [main.py:11407] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📋 LOGIC-BASED STATE EXTRACTION: No text patterns, relying on structured data only
2025-07-22 19:59:19,792 - INFO - [main.py:7573] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📊 TEACHING PROGRESS: 1 interactions
2025-07-22 19:59:19,792 - INFO - [main.py:7574] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📊 OBJECTIVES COVERAGE: 0.0%
2025-07-22 19:59:19,793 - INFO - [main.py:9342] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 19:59:19,794 - INFO - [main.py:5710] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔄 PHASE TRANSITION: teaching → teaching
2025-07-22 19:59:19,796 - INFO - [phase_transition_integrity.py:153] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 PHASE TRANSITION VALIDATION: teaching → teaching
2025-07-22 19:59:19,799 - WARNING - [phase_transition_integrity.py:191] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔧 DATA RECOVERY APPLIED: Recovered assigned_level_for_teaching as 5
2025-07-22 19:59:19,800 - ERROR - [phase_transition_integrity.py:719] - 🔍 STATE CONSISTENCY ERRORS DETECTED:
2025-07-22 19:59:19,801 - ERROR - [phase_transition_integrity.py:721] - 🔍   1. Cannot enter teaching phase without completing diagnostic
2025-07-22 19:59:19,801 - ERROR - [phase_transition_integrity.py:722] - 🔍 SESSION DATA DEBUG:
2025-07-22 19:59:19,802 - ERROR - [phase_transition_integrity.py:723] - 🔍   - diagnostic_complete: False
2025-07-22 19:59:19,803 - ERROR - [phase_transition_integrity.py:724] - 🔍   - assigned_level_for_teaching: None
2025-07-22 19:59:19,803 - ERROR - [phase_transition_integrity.py:725] - 🔍   - teaching_complete: False
2025-07-22 19:59:19,804 - ERROR - [phase_transition_integrity.py:726] - 🔍   - teaching_interactions: 1
2025-07-22 19:59:19,804 - ERROR - [phase_transition_integrity.py:727] - 🔍   - from_phase: teaching, to_phase: teaching
2025-07-22 19:59:19,806 - ERROR - [phase_transition_integrity.py:741] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔧 STATE CORRECTION ATTEMPT: teaching → teaching
2025-07-22 19:59:19,806 - ERROR - [phase_transition_integrity.py:748] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔧 CORRECTION CHECK: diagnostic_complete=False, assigned_level=None
2025-07-22 19:59:19,807 - ERROR - [phase_transition_integrity.py:781] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔧 CORRECTION RESULT: {'corrected': False, 'corrected_phase': 'teaching', 'corrected_data': {}, 'message': 'No corrections applied'}
2025-07-22 19:59:19,807 - WARNING - [phase_transition_integrity.py:224] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔧 AUTO-CORRECTION: diagnostic_complete=True, assigned_level=1 for transition to teaching
2025-07-22 19:59:19,808 - INFO - [phase_transition_integrity.py:235] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ TRANSITION VALIDATED: teaching → teaching
2025-07-22 19:59:19,808 - INFO - [main.py:5718] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ VALID TRANSITION: teaching → teaching
2025-07-22 19:59:19,809 - INFO - [main.py:9358] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔒 UNIFIED PATHWAY ENFORCED: teaching → teaching
2025-07-22 19:59:19,810 - INFO - [main.py:9359] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 19:59:20,632 - INFO - [main.py:9373] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ UNIFIED STATE MACHINE: Session state updated with phase teaching
2025-07-22 19:59:20,632 - INFO - [main.py:9383] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 19:59:20,633 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:59:20,633 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:59:20,633 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:59:20,634 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:59:20,634 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:59:20,634 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:59:20,634 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:59:20,635 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:59:20,635 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:59:20,635 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:59:20,636 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:59:20,636 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:59:20,637 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:59:20,637 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:59:20,638 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:59:20,638 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:59:20,638 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:59:20,638 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:59:20,639 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:59:20,639 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:59:20,639 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:59:20,639 - INFO - [intelligent_guardrails.py:130] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:59:20,640 - WARNING - [intelligent_guardrails.py:146] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🚨 QUIZ REQUEST BLOCKED: Teaching incomplete
2025-07-22 19:59:20,640 - INFO - [intelligent_guardrails.py:451] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ BLOCKING VIOLATIONS: Preventing quiz transition
2025-07-22 19:59:20,640 - INFO - [intelligent_guardrails.py:464] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ BACKEND INSTRUCTION (INTERNAL ONLY): Continue teaching until all completion criteria are met
2025-07-22 19:59:20,641 - INFO - [intelligent_guardrails.py:512] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📝 STUDENT-FRIENDLY CONTINUATION: Generated encouraging message instead of backend instruction
2025-07-22 19:59:20,641 - INFO - [intelligent_guardrails.py:183] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ GUARDRAILS RESULT: Valid=False, Violations=1
2025-07-22 19:59:20,641 - INFO - [intelligent_guardrails.py:675] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ GUARDRAILS APPLIED: Valid=False, Violations=1, Teaching Complete=False
2025-07-22 19:59:20,642 - INFO - [main.py:9485] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ GUARDRAIL VIOLATIONS: 1 found
2025-07-22 19:59:20,642 - INFO - [main.py:9490] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️   - BLOCKING: teaching_incomplete_quiz_block - Quiz transition blocked - teaching not complete
2025-07-22 19:59:20,642 - WARNING - [main.py:9496] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ BLOCKING VIOLATION: Preventing inappropriate progression
2025-07-22 19:59:20,643 - INFO - [main.py:9515] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ GUARDRAILS APPLIED: Valid=False, Enhanced=True
2025-07-22 19:59:20,643 - WARNING - [main.py:9563] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🤖 AI RESPONSE RECEIVED:
2025-07-22 19:59:20,643 - WARNING - [main.py:9564] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🤖   - Content length: 1300 chars
2025-07-22 19:59:20,644 - WARNING - [main.py:9565] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🤖   - State updates: {'new_phase': 'teaching', 'teaching_interactions': 1}
2025-07-22 19:59:20,644 - WARNING - [main.py:9566] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🤖   - Raw state block: None...
2025-07-22 19:59:20,644 - INFO - [main.py:9574] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎓 APPLYING TEACHING PHASE ENHANCEMENT
2025-07-22 19:59:20,650 - WARNING - [teaching_phase_enhancement.py:445] - No active teaching session found for fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:59:20,653 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:59:20,654 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:59:20,654 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:59:20,655 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:59:20,655 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:59:20,655 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:59:20,655 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:59:20,656 - INFO - [main.py:9665] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 ADAPTIVE REQUIREMENTS: 10 interactions required
2025-07-22 19:59:20,656 - INFO - [main.py:9666] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯   Based on: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:59:20,656 - ERROR - [main.py:10009] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ❌ Teaching phase analysis error: No active teaching session
2025-07-22 19:59:20,657 - INFO - [main.py:10041] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 QUIZ VALIDATION: 0 interactions, 0.0% coverage (0/2 objectives)
2025-07-22 19:59:20,658 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:59:20,658 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:59:20,658 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:59:20,658 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:59:20,659 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:59:20,659 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:59:20,659 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:59:20,659 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:59:20,660 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:59:20,660 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:59:20,660 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:59:20,660 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:59:20,660 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:59:20,661 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:59:20,661 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:59:20,661 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:59:20,661 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:59:20,662 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:59:20,662 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:59:20,662 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:59:20,662 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:59:20,662 - INFO - [main.py:10084] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🚫 AI INSTRUCTOR QUIZ BLOCKED: teaching_incomplete_need_more_coverage
2025-07-22 19:59:20,663 - INFO - [main.py:10085] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Interactions: 0, Coverage: 0.0%
2025-07-22 19:59:20,663 - INFO - [main.py:10096] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ AI response filtered to continue teaching
2025-07-22 19:59:20,663 - DEBUG - [main.py:10099] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 19:59:20,663 - DEBUG - [main.py:10100] - 🤖   Content: That's wonderful to hear, Andrea! You're absolutely right, AI is set to be a huge help in making peo...
2025-07-22 19:59:20,664 - DEBUG - [main.py:10101] - 🤖   State: {'new_phase': 'teaching', 'teaching_interactions': 1}
2025-07-22 19:59:20,664 - INFO - [main.py:10127] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 19:59:20,664 - INFO - [main.py:10128] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] CURRENT PHASE DETERMINATION: AI=teaching, Session=teaching, Final=teaching
2025-07-22 19:59:20,962 - WARNING - [main.py:10230] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 STATE UPDATE VALIDATION: current_phase='teaching', new_phase='teaching'
2025-07-22 19:59:20,962 - INFO - [main.py:6365] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI state update validation passed: teaching → teaching
2025-07-22 19:59:20,963 - WARNING - [main.py:10239] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 19:59:20,963 - WARNING - [main.py:10260] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   PHASE MAINTAINED: teaching
2025-07-22 19:59:20,963 - INFO - [main.py:10289] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching
2025-07-22 19:59:20,964 - INFO - [main.py:10294] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 19:59:20,964 - INFO - [phase_transition_integrity.py:330] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔄 APPLYING TRANSITION WITH INTEGRITY: teaching → teaching
2025-07-22 19:59:20,965 - INFO - [phase_transition_integrity.py:293] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📸 DATA SNAPSHOT CREATED: Session fallback-********-a9d2-44b1-a778-78455bae1f78, Phase teaching
2025-07-22 19:59:20,965 - INFO - [phase_transition_integrity.py:153] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 PHASE TRANSITION VALIDATION: teaching → teaching
2025-07-22 19:59:20,966 - INFO - [phase_transition_integrity.py:235] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ TRANSITION VALIDATED: teaching → teaching
2025-07-22 19:59:20,967 - INFO - [phase_transition_integrity.py:360] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ TRANSITION APPLIED: teaching → teaching
2025-07-22 19:59:20,968 - DEBUG - [phase_transition_integrity.py:847] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 19:59:20,970 - DEBUG - [phase_transition_integrity.py:880] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📝 TRANSITION RECORDED: teaching → teaching (valid)
2025-07-22 19:59:20,971 - INFO - [phase_transition_integrity.py:406] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching
2025-07-22 19:59:20,971 - WARNING - [main.py:10336] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔧 STATE MACHINE: Invalid transition corrected to quiz_initiate
2025-07-22 19:59:20,972 - INFO - [main.py:10367] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ TRANSITION VALIDATED: teaching → quiz_initiate
2025-07-22 19:59:20,972 - WARNING - [main.py:10372] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 19:59:20,973 - WARNING - [main.py:10373] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍   1. Input phase: 'teaching'
2025-07-22 19:59:20,973 - WARNING - [main.py:10374] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 19:59:20,973 - WARNING - [main.py:10375] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍   3. Proposed phase: 'teaching'
2025-07-22 19:59:20,974 - WARNING - [main.py:10376] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍   4. Integrity validation result: 'valid'
2025-07-22 19:59:20,974 - WARNING - [main.py:10377] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍   5. Final phase to save: 'quiz_initiate'
2025-07-22 19:59:20,974 - WARNING - [main.py:10380] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 💾 FINAL STATE APPLICATION:
2025-07-22 19:59:20,975 - WARNING - [main.py:10381] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 💾   - Current phase input: 'teaching'
2025-07-22 19:59:20,975 - WARNING - [main.py:10382] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 💾   - Validated state updates: 24 fields
2025-07-22 19:59:20,975 - WARNING - [main.py:10383] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 💾   - Final phase to save: 'quiz_initiate'
2025-07-22 19:59:20,975 - WARNING - [main.py:10384] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 💾   - Phase change: True
2025-07-22 19:59:20,976 - WARNING - [main.py:10385] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 💾   - Integrity applied: True
2025-07-22 19:59:20,976 - INFO - [main.py:6397] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 19:59:20,976 - INFO - [main.py:6398] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Phase transition: teaching -> teaching
2025-07-22 19:59:20,976 - INFO - [main.py:6399] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Current level: 7
2025-07-22 19:59:20,977 - INFO - [main.py:6400] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Question index: 0
2025-07-22 19:59:20,977 - INFO - [main.py:6401] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   First encounter: False
2025-07-22 19:59:20,977 - INFO - [main.py:6406] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Answers collected: 0
2025-07-22 19:59:20,977 - INFO - [main.py:6407] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Levels failed: 0
2025-07-22 19:59:20,978 - INFO - [main.py:6365] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI state update validation passed: teaching → teaching
2025-07-22 19:59:20,978 - INFO - [main.py:6411] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   State update valid: True
2025-07-22 19:59:20,978 - INFO - [main.py:6418] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Diagnostic complete: False
2025-07-22 19:59:20,978 - INFO - [main.py:6420] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   Assigned level: 7
2025-07-22 19:59:20,979 - WARNING - [main.py:10403] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 19:59:20,979 - INFO - [main.py:10412] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 DEBUG state_updates_from_ai teaching_interactions: 1
2025-07-22 19:59:20,979 - INFO - [main.py:10413] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 19:59:20,979 - WARNING - [main.py:10416] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ INTERACTION COUNT PROTECTION: AI tried to set 1, backend preserving 0
2025-07-22 19:59:20,980 - WARNING - [main.py:10417] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229]   This prevents teaching interaction count resets and infinite loops
2025-07-22 19:59:21,479 - WARNING - [main.py:10463] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 19:59:21,480 - WARNING - [main.py:10464] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅   - Phase: quiz_initiate
2025-07-22 19:59:21,480 - WARNING - [main.py:10465] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅   - Probing Level: 7
2025-07-22 19:59:21,480 - WARNING - [main.py:10466] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅   - Question Index: 0
2025-07-22 19:59:21,480 - WARNING - [main.py:10467] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅   - Diagnostic Complete: True
2025-07-22 19:59:21,481 - WARNING - [main.py:10474] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅   - Quiz Questions Saved: 0
2025-07-22 19:59:21,481 - WARNING - [main.py:10475] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅   - Quiz Answers Saved: 0
2025-07-22 19:59:21,481 - WARNING - [main.py:10476] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅   - Quiz Started: False
2025-07-22 19:59:21,482 - DEBUG - [main.py:10525] - 🔥 STATE SAVED - Session: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: quiz_initiate
2025-07-22 19:59:21,482 - DEBUG - [main.py:10526] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 19:59:22,326 - DEBUG - [main.py:10584] - ✅ SESSION UPDATED - ID: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: quiz_initiate
2025-07-22 19:59:22,327 - DEBUG - [main.py:10585] - ✅ INTERACTION LOGGED - Phase: teaching → quiz_initiate
2025-07-22 19:59:22,328 - INFO - [main.py:10591] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ Updated existing session document: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:59:22,329 - INFO - [main.py:18505] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 19:59:22,329 - DEBUG - [main.py:4943] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 19:59:22,330 - DEBUG - [main.py:10678] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] No final assessment data found in AI response
2025-07-22 19:59:22,330 - DEBUG - [main.py:10701] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] No lesson completion detected (Phase: teaching, Complete: False)
2025-07-22 19:59:22,332 - WARNING - [main.py:10720] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🔧 STATE CORRECTION: Forcing 'diagnostic_completed_this_session' to True because phase is 'teaching'.
2025-07-22 19:59:22,333 - DEBUG - [main.py:10725] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 19:59:22,334 - DEBUG - [main.py:10726] - 🔒   Current Phase: teaching
2025-07-22 19:59:22,334 - DEBUG - [main.py:10727] - 🔒   Final Phase: teaching
2025-07-22 19:59:22,335 - DEBUG - [main.py:10728] - 🔒   Diagnostic Complete: True
2025-07-22 19:59:22,336 - DEBUG - [main.py:10729] - 🔒   Assigned Level: 7
2025-07-22 19:59:22,337 - INFO - [main.py:10802] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 19:59:22,338 - INFO - [main.py:10836] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 19:59:22,339 - INFO - [main.py:10844] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 19:59:22,339 - INFO - [main.py:10849] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 19:59:22,340 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 19:59:22,340 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 19:59:22,340 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 19:59:22,341 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 19:59:22,342 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 19:59:22,342 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 19:59:22,343 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 19:59:22,343 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 19:59:22,343 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 19:59:22,344 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 19:59:22,344 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 19:59:22,344 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 19:59:22,345 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 19:59:22,345 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 19:59:22,345 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 19:59:22,346 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 19:59:22,346 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 19:59:22,346 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 19:59:22,347 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 19:59:22,347 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 19:59:22,348 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 19:59:22,348 - INFO - [main.py:10858] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 19:59:22,348 - INFO - [intelligent_guardrails.py:130] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 19:59:22,349 - WARNING - [intelligent_guardrails.py:146] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🚨 QUIZ REQUEST BLOCKED: Teaching incomplete
2025-07-22 19:59:22,350 - INFO - [intelligent_guardrails.py:167] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ GUARDRAIL VIOLATION: vocabulary_appropriateness - warning
2025-07-22 19:59:22,350 - INFO - [intelligent_guardrails.py:451] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ BLOCKING VIOLATIONS: Preventing quiz transition
2025-07-22 19:59:22,351 - INFO - [intelligent_guardrails.py:464] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ BACKEND INSTRUCTION (INTERNAL ONLY): Continue teaching until all completion criteria are met
2025-07-22 19:59:22,351 - INFO - [intelligent_guardrails.py:512] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 📝 STUDENT-FRIENDLY CONTINUATION: Generated encouraging message instead of backend instruction
2025-07-22 19:59:22,351 - INFO - [intelligent_guardrails.py:183] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ GUARDRAILS RESULT: Valid=False, Violations=2
2025-07-22 19:59:22,352 - INFO - [main.py:10920] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🛡️ GUARDRAILS VALIDATION: Valid=False, Violations=2
2025-07-22 19:59:22,352 - WARNING - [main.py:10924] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🚨 GUARDRAIL VIOLATION: teaching_incomplete_quiz_block - Quiz transition blocked - teaching not complete (Severity: GuardrailSeverity.BLOCKING)
2025-07-22 19:59:22,353 - WARNING - [main.py:10924] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] 🚨 GUARDRAIL VIOLATION: vocabulary_appropriateness - Vocabulary may be too advanced for primary-5 (Severity: GuardrailSeverity.WARNING)
2025-07-22 19:59:22,353 - INFO - [main.py:10945] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✨ GUARDRAILS ENHANCED RESPONSE: Applied pedagogical improvements
2025-07-22 19:59:22,354 - INFO - [main.py:10948] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 19:59:22,354 - DEBUG - [main.py:11005] - 🎯 RESPONSE READY:
2025-07-22 19:59:22,355 - DEBUG - [main.py:11006] - 🎯   Session: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 19:59:22,355 - DEBUG - [main.py:11007] - 🎯   Phase: teaching → teaching
2025-07-22 19:59:22,355 - DEBUG - [main.py:11008] - 🎯   Content: That's wonderful to hear, Andrea! You're absolutel...
2025-07-22 19:59:22,355 - DEBUG - [main.py:11009] - 🎯   Request ID: 5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229
2025-07-22 19:59:22,356 - INFO - [main.py:11015] - [5b9fc73b-a1cc-4f2a-a834-90ddf3b5d229] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 19:59:22,356 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,356 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,357 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,357 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:59:22,357 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:59:22,357 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:59:22,358 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:59:22,358 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,358 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 19:59:22,358 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,358 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,359 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,359 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,359 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,359 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 19:59:22,360 - WARNING - [main.py:888] - High response time detected: 7.05s for enhance_content_api
2025-07-22 20:00:13,018 - INFO - [main.py:7143] - Incoming request: {"request_id": "47c49979-893b-41cf-8d0d-a9cd508617ab", "timestamp": "2025-07-22T19:00:13.018018+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "show you a few more examples to strengthen your understanding.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-********-a9d2-44b1-a778-78455bae1f78", "chat_history": [{"role": "user", "content": "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", "timestamp": "2025-07-22T18:56:26.985Z"}, {"role": "assistant", "content": "Wow, Andrea, you're on fire with these analogies! \"Super-smart assistant\" and comparing to \"lots of other people\" is exactly right. And the idea of a \"giant library of medicines\" is a fantastic way to think about how AI can find the best treatment. You're really understanding how AI can help doctors make informed decisions!\n\nWe're almost there! Let's try this next one.\n\n**Question 4 of 5 (Level 5 Deeper Understanding):** Besides helping doctors find out what's wrong and what medicine to use, what else do you think AI could do in hospitals or for people who are sick?", "timestamp": "2025-07-22T18:56:34.331Z"}, {"role": "user", "content": "Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to give, AI can do lots of other cool things! It can help make sure patients get the right medicine at the right time, like a helpful reminder system. AI can also help doctors keep track of lots of patient information really well, almost like a super-organized filing cabinet. It can even help invent new medicines by looking at tiny details about how sicknesses work. And sometimes, AI can help robots do very careful jobs, like helping a surgeon during an operation, making it even steadier. It can also help people who need to stay home by chatting with them and checking how they are feeling. It's like having many helpers all working together to make people better!", "timestamp": "2025-07-22T18:57:06.264Z"}, {"role": "assistant", "content": "That's an absolutely brilliant summary, Andrea! You've hit on so many important areas: keeping track of information, helping invent new medicines, assisting in surgeries, and even checking in on people at home. Your description of AI as \"many helpers all working together\" is spot on! You're showing a really deep understanding of how AI can impact many parts of healthcare.\n\nJust one more question to go!\n\n**Question 5 of 5 (Level 6 Synthesis):** Thinking about everything we've discussed, how do you think AI in Healthcare might change things for patients in the future?", "timestamp": "2025-07-22T18:57:12.926Z"}, {"role": "user", "content": "AI in healthcare is going to make things super different for patients in a really good way! Imagine going to the doctor and they already know a lot about you because AI helps them keep all your information super organised. This means they can spend more time actually talking to you and figuring out the best way to help you feel better, instead of spending ages writing notes.[1][2] AI can also help doctors find sicknesses really early, even before you feel sick![3][4] It's like having a secret early warning system. And for people who have long-term illnesses, AI might help them manage their health better from home, maybe with special apps or devices that check on them.[5] Plus, AI can help doctors find new medicines faster, so people can get better treatments sooner.[2] It's like having lots of super-smart helpers all working together to make everyone healthier and happier!", "timestamp": "2025-07-22T18:57:43.618Z"}, {"role": "assistant", "content": "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! \ud83c\udf89\n\n**Diagnostic Results:** \u2705 Questions Completed: 5/5\n\n\ud83d\udcca **Recommended Teaching Level: 7**\n\nBased on your insightful answers and fantastic analogies, I've determined that we should start teaching at Level 7. You've already shown a great grasp of the core concepts!\n\nNow let's begin our exciting lesson on AI in Healthcare! \ud83d\ude80\n\nAt its heart, **AI in Healthcare** means using smart computer programs to help doctors, nurses, and scientists do their jobs better and make people healthier. Think of it as giving our healthcare heroes super-powers!\n\nWe've already touched on how AI is **used** in **Healthcare** for **disease diagnosis**. This is when AI helps doctors figure out what illness a person has. For example, AI can look at X-rays or scans and spot tiny signs of **disease** that a human eye might miss. It's like having a super-powered magnifying glass! It can also help predict if someone might get a certain **disease** in the future by looking at their health history.\n\nNow, let's dive a little deeper into how AI helps **discover** new **drug**s. This is a really exciting area! Imagine trying to find the perfect key to unlock a door, but there are millions of keys! That's a bit like finding new medicines. AI can help by looking at tons of information about diseases and how different chemicals might affect them. It can quickly test many different ideas to find promising new **drug**s that could help people. This can make the process of finding new medicines much faster and more efficient.\n\nDo you have any initial questions about how AI helps with disease diagnosis or drug discovery?\n\nI can see you're learning, Andrea! Let's cover a few more important points about AI in Healthcare.\n\nI can see you're learning, Student! Let's cover a few more important points about AI in Healthcare.", "timestamp": "2025-07-22T18:57:51.924Z"}, {"role": "user", "content": "That's so cool! I'm excited to learn more!  I don't have any questions right now, but I'm ready for the next part of the lesson! It sounds like AI is going to be a really big help in making people healthy in the future.", "timestamp": "2025-07-22T18:59:14.904Z"}, {"role": "assistant", "content": "That's wonderful to hear, Andrea! You're absolutely right, AI is set to be a huge help in making people healthier in the future.\n\nSo, we've learned that **AI in Healthcare** is about using smart technology to assist medical professionals. One of the key ways it's **used** is in **disease diagnosis**. This involves AI analyzing medical data, like X-rays or patient records, to help doctors identify illnesses. For instance, an AI might be trained to spot patterns in scans that indicate a specific **disease**, helping doctors make a quicker and more accurate **diagnosis**.\n\nAnother really important **role** of AI is in **drug discovery**. Creating new medicines can take a very long time and be very expensive. AI can speed this up by sifting through vast amounts of data to identify potential new **drug** candidates. It can predict which molecules might be effective against a particular **disease**, saving researchers a lot of time and effort. It's like having a super-fast researcher who can look at millions of possibilities!\n\nTo recap, AI is a powerful tool that helps in understanding and treating diseases, and in creating new ways to fight them.\n\nReady to test your knowledge with a quiz?\n\nYou're doing well, Andrea! Let me show you a few more examples to strengthen your understanding.\n\nYou're doing well, Student! Let me show you a few more examples to strengthen your understanding.", "timestamp": "2025-07-22T18:59:22.375Z"}]}}
2025-07-22 20:00:13,019 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 20:00:13,019 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FojRArwf7TCv4y3JoZYASQTKKeemWG0Zxr2u8rLS_3HvNj0ZMYI-0G7Xbxx3Yhp6l5qhe14xG2DAhf0vXXcHgss9zySkSFdJqJhUCVL5I9WvN8l9G6LqHsOIOxXc3rKImNtmJ6U38HeqI3grXrFtqbS8AGZA8gVhKt97seDdqmUoIJxTvRa9B_CoDHpRgoPtma8eSC-2Z6xEepuhE0cmy8pqmRCRODvn7pgZ_TnOTK0AiA-OiH-PcV0sU9jLg2wdFacFrdCvYzgL_lTphao7Yrm-W1XQgBYZ4WRMLvIsy6AYZpEbXBcM2vfuQRBl3sqVAw2Ji5b4XU77_645ujHrDw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '7994', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 20:00:13,019 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 47c49979-893b-41cf-8d0d-a9cd508617ab
2025-07-22 20:00:13,019 - INFO - [auth_decorator.py:74] - [47c49979-893b-41cf-8d0d-a9cd508617ab][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 20:00:13,020 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 20:00:13,021 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 20:00:13,021 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 20:00:13,022 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 20:00:13,022 - INFO - [auth_decorator.py:95] - [47c49979-893b-41cf-8d0d-a9cd508617ab][require_auth] Development mode detected - bypassing authentication
2025-07-22 20:00:13,023 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 20:00:13,024 - INFO - [auth_decorator.py:121] - [47c49979-893b-41cf-8d0d-a9cd508617ab][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 20:00:13,415 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:00:13,415 - INFO - [auth_decorator.py:164] - [47c49979-893b-41cf-8d0d-a9cd508617ab][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:00:13,416 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 20:00:13,418 - INFO - [main.py:7327] -
================================================================================
2025-07-22 20:00:13,419 - WARNING - [main.py:7328] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 20:00:13,419 - WARNING - [main.py:7329] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 20:00:13,420 - DEBUG - [main.py:7337] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Console output error: I/O operation on closed file.
2025-07-22 20:00:13,422 - INFO - [main.py:8179] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"show you a few more examples to strengthen your understanding.","country":"Nigeria","curriculum":"National Curriculum","grade":"primary-5","subject":"Artificial Intelligence","session_id":"fallback-********-a9d2-44b1-a778-78455bae1f78","chat_history":[{"role":"user","content":"The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long th...
2025-07-22 20:00:13,423 - INFO - [main.py:8181] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': 'show you a few more examples to strengthen your understanding.', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-********-a9d2-44b1-a778-78455bae1f78', 'chat_history': [{'role': 'user', 'content': "The AI can be like a super-smart assistant for the doctor! First, it can look at all the information the patient has given, like how long they've had the cough and if they have a fever. Then, it can compare this to lots of other people who had similar coughs and fevers to see what made them better. The AI can also search through a giant library of medicines and tell the doctor which ones are most likely to work for this specific cough and fever, almost like picking the best tool for a job. It can even suggest if a medicine might cause any problems for this particular person by looking at their past health history. It's like the AI has read every book about medicines and knows which one is the perfect fit! Google Search Suggestions", 'timestamp': '2025-07-22T18:56:26.985Z'}, {'role': 'assistant', 'content': 'Wow, Andrea, you\'re on fire with these analogies! "Super-smart assistant" and comparing to "lots of other people" is exactly right. And the idea of a "giant library of medicines" is a fantastic way to think about how AI can find the best treatment. You\'re really understanding how AI can help doctors make informed decisions!\n\nWe\'re almost there! Let\'s try this next one.\n\n**Question 4 of 5 (Level 5 Deeper Understanding):** Besides helping doctors find out what\'s wrong and what medicine to use, what else do you think AI could do in hospitals or for people who are sick?', 'timestamp': '2025-07-22T18:56:34.331Z'}, {'role': 'user', 'content': "Wow, that's a great question! Besides helping doctors figure out what's wrong and what medicine to give, AI can do lots of other cool things! It can help make sure patients get the right medicine at the right time, like a helpful reminder system. AI can also help doctors keep track of lots of patient information really well, almost like a super-organized filing cabinet. It can even help invent new medicines by looking at tiny details about how sicknesses work. And sometimes, AI can help robots do very careful jobs, like helping a surgeon during an operation, making it even steadier. It can also help people who need to stay home by chatting with them and checking how they are feeling. It's like having many helpers all working together to make people better!", 'timestamp': '2025-07-22T18:57:06.264Z'}, {'role': 'assistant', 'content': 'That\'s an absolutely brilliant summary, Andrea! You\'ve hit on so many important areas: keeping track of information, helping invent new medicines, assisting in surgeries, and even checking in on people at home. Your description of AI as "many helpers all working together" is spot on! You\'re showing a really deep understanding of how AI can impact many parts of healthcare.\n\nJust one more question to go!\n\n**Question 5 of 5 (Level 6 Synthesis):** Thinking about everything we\'ve discussed, how do you think AI in Healthcare might change things for patients in the future?', 'timestamp': '2025-07-22T18:57:12.926Z'}, {'role': 'user', 'content': "AI in healthcare is going to make things super different for patients in a really good way! Imagine going to the doctor and they already know a lot about you because AI helps them keep all your information super organised. This means they can spend more time actually talking to you and figuring out the best way to help you feel better, instead of spending ages writing notes.[1][2] AI can also help doctors find sicknesses really early, even before you feel sick![3][4] It's like having a secret early warning system. And for people who have long-term illnesses, AI might help them manage their health better from home, maybe with special apps or devices that check on them.[5] Plus, AI can help doctors find new medicines faster, so people can get better treatments sooner.[2] It's like having lots of super-smart helpers all working together to make everyone healthier and happier!", 'timestamp': '2025-07-22T18:57:43.618Z'}, {'role': 'assistant', 'content': "Excellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉\n\n**Diagnostic Results:** ✅ Questions Completed: 5/5\n\n📊 **Recommended Teaching Level: 7**\n\nBased on your insightful answers and fantastic analogies, I've determined that we should start teaching at Level 7. You've already shown a great grasp of the core concepts!\n\nNow let's begin our exciting lesson on AI in Healthcare! 🚀\n\nAt its heart, **AI in Healthcare** means using smart computer programs to help doctors, nurses, and scientists do their jobs better and make people healthier. Think of it as giving our healthcare heroes super-powers!\n\nWe've already touched on how AI is **used** in **Healthcare** for **disease diagnosis**. This is when AI helps doctors figure out what illness a person has. For example, AI can look at X-rays or scans and spot tiny signs of **disease** that a human eye might miss. It's like having a super-powered magnifying glass! It can also help predict if someone might get a certain **disease** in the future by looking at their health history.\n\nNow, let's dive a little deeper into how AI helps **discover** new **drug**s. This is a really exciting area! Imagine trying to find the perfect key to unlock a door, but there are millions of keys! That's a bit like finding new medicines. AI can help by looking at tons of information about diseases and how different chemicals might affect them. It can quickly test many different ideas to find promising new **drug**s that could help people. This can make the process of finding new medicines much faster and more efficient.\n\nDo you have any initial questions about how AI helps with disease diagnosis or drug discovery?\n\nI can see you're learning, Andrea! Let's cover a few more important points about AI in Healthcare.\n\nI can see you're learning, Student! Let's cover a few more important points about AI in Healthcare.", 'timestamp': '2025-07-22T18:57:51.924Z'}, {'role': 'user', 'content': "That's so cool! I'm excited to learn more!  I don't have any questions right now, but I'm ready for the next part of the lesson! It sounds like AI is going to be a really big help in making people healthy in the future.", 'timestamp': '2025-07-22T18:59:14.904Z'}, {'role': 'assistant', 'content': "That's wonderful to hear, Andrea! You're absolutely right, AI is set to be a huge help in making people healthier in the future.\n\nSo, we've learned that **AI in Healthcare** is about using smart technology to assist medical professionals. One of the key ways it's **used** is in **disease diagnosis**. This involves AI analyzing medical data, like X-rays or patient records, to help doctors identify illnesses. For instance, an AI might be trained to spot patterns in scans that indicate a specific **disease**, helping doctors make a quicker and more accurate **diagnosis**.\n\nAnother really important **role** of AI is in **drug discovery**. Creating new medicines can take a very long time and be very expensive. AI can speed this up by sifting through vast amounts of data to identify potential new **drug** candidates. It can predict which molecules might be effective against a particular **disease**, saving researchers a lot of time and effort. It's like having a super-fast researcher who can look at millions of possibilities!\n\nTo recap, AI is a powerful tool that helps in understanding and treating diseases, and in creating new ways to fight them.\n\nReady to test your knowledge with a quiz?\n\nYou're doing well, Andrea! Let me show you a few more examples to strengthen your understanding.\n\nYou're doing well, Student! Let me show you a few more examples to strengthen your understanding.", 'timestamp': '2025-07-22T18:59:22.375Z'}]}
2025-07-22 20:00:13,424 - INFO - [main.py:8183] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍  - Session ID from payload: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 20:00:13,424 - INFO - [main.py:8184] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 20:00:13,424 - INFO - [main.py:8185] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 20:00:13,425 - DEBUG - [main.py:8223] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 20:00:13,425 - INFO - [main.py:8224] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-********-a9d2-44b1-a778-78455bae1f78', lesson_ref='P5-AI-089'
2025-07-22 20:00:13,425 - INFO - [main.py:8264] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Level not provided, determined from grade 'primary-5': 5
2025-07-22 20:00:13,823 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 20:00:13,824 - INFO - [main.py:8281] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 20:00:13,824 - INFO - [main.py:8282] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 20:00:13,824 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 20:00:14,232 - INFO - [main.py:8312] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 20:00:14,232 - INFO - [main.py:8375] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 20:00:14,232 - INFO - [main.py:8500] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ Successfully retrieved lesson from primary path
2025-07-22 20:00:14,233 - INFO - [main.py:8511] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 20:00:14,233 - INFO - [main.py:8550] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 20:00:14,233 - INFO - [main.py:4456] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 20:00:14,639 - INFO - [main.py:4522] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 20:00:14,639 - INFO - [main.py:4522] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 20:00:14,639 - INFO - [main.py:4522] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 20:00:14,639 - INFO - [main.py:4522] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 20:00:14,640 - INFO - [main.py:4522] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 20:00:14,640 - INFO - [main.py:4591] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 20:00:14,640 - DEBUG - [main.py:4605] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 20:00:14,640 - DEBUG - [main.py:4608] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 20:00:14,640 - DEBUG - [main.py:4609] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 20:00:14,641 - DEBUG - [main.py:4610] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 20:00:14,641 - INFO - [main.py:4614] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Calling Gemini API for module inference...
2025-07-22 20:00:15,095 - INFO - [main.py:4624] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Gemini API call completed in 0.45s. Raw response: 'ai_tools_and_applications'
2025-07-22 20:00:15,095 - DEBUG - [main.py:4646] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 20:00:15,095 - INFO - [main.py:4651] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 20:00:15,096 - INFO - [main.py:8584] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 20:00:15,096 - INFO - [main.py:8621] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 20:00:15,387 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 20:00:15,884 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 20:00:15,884 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 20:00:15,884 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 20:00:15,885 - DEBUG - [main.py:8677] - 🔍   - Current Phase: quiz_initiate
2025-07-22 20:00:15,885 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 7
2025-07-22 20:00:15,885 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 20:00:15,886 - WARNING - [main.py:8685] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 SESSION STATE DEBUG:
2025-07-22 20:00:15,886 - WARNING - [main.py:8686] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   - Session exists: True
2025-07-22 20:00:15,886 - WARNING - [main.py:8687] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   - Current phase: quiz_initiate
2025-07-22 20:00:15,887 - WARNING - [main.py:8688] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'new_phase', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'diagnostic_complete', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 20:00:15,888 - DEBUG - [main.py:8706] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 20:00:15,888 - DEBUG - [main.py:8707] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Retrieved Phase: 'quiz_initiate'
2025-07-22 20:00:15,889 - DEBUG - [main.py:8708] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Diagnostic Completed: True
2025-07-22 20:00:15,889 - DEBUG - [main.py:8709] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Assigned Level: 7
2025-07-22 20:00:15,890 - WARNING - [main.py:8710] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔒 STATE PROTECTION: phase='quiz_initiate', diagnostic_done=True, level=7
2025-07-22 20:00:15,891 - INFO - [main.py:8739] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ State is valid - no correction needed
2025-07-22 20:00:15,891 - INFO - [main.py:8740] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ State validation passed: quiz_initiate
2025-07-22 20:00:15,892 - INFO - [main.py:8772] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 ADVANCED PHASE DETECTED: 'quiz_initiate' - treating as returning student
2025-07-22 20:00:15,892 - WARNING - [main.py:8789] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ⚠️ No latest assessed level in profile, using session state: 7
2025-07-22 20:00:15,893 - INFO - [main.py:8791] - [47c49979-893b-41cf-8d0d-a9cd508617ab]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 20:00:15,893 - INFO - [main.py:8792] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   assigned_level_for_teaching (session): 7
2025-07-22 20:00:15,894 - INFO - [main.py:8793] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   latest_assessed_level (profile): None
2025-07-22 20:00:15,894 - INFO - [main.py:8794] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   teaching_level_for_returning_student: 7
2025-07-22 20:00:15,894 - INFO - [main.py:8795] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   has_completed_diagnostic_before: True
2025-07-22 20:00:15,895 - INFO - [main.py:8796] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   is_first_encounter_for_module: False
2025-07-22 20:00:15,895 - INFO - [main.py:8806] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ RETURNING STUDENT: Using existing diagnostic state - diagnostic_completed=True
2025-07-22 20:00:15,895 - INFO - [main.py:8809] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 PHASE INVESTIGATION:
2025-07-22 20:00:15,895 - INFO - [main.py:8810] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Retrieved from Firestore: 'quiz_initiate'
2025-07-22 20:00:15,895 - INFO - [main.py:8811] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 20:00:15,896 - INFO - [main.py:8812] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Is first encounter: False
2025-07-22 20:00:15,896 - INFO - [main.py:8813] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Diagnostic completed: True
2025-07-22 20:00:15,896 - INFO - [main.py:8819] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ Using stored phase from Firestore: 'quiz_initiate'
2025-07-22 20:00:15,896 - INFO - [main.py:8833] - [47c49979-893b-41cf-8d0d-a9cd508617ab] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 20:00:15,896 - INFO - [main.py:8835] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Final phase for AI logic: quiz_initiate
2025-07-22 20:00:15,897 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 20:00:15,897 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 20:00:15,897 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 20:00:15,897 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 20:00:15,898 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 20:00:15,898 - INFO - [main.py:6144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Diagnostic context validation passed
2025-07-22 20:00:15,898 - INFO - [main.py:9013] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 PRESERVING ADVANCED PHASE: 'quiz_initiate' - no override for advanced phases
2025-07-22 20:00:15,899 - INFO - [main.py:9030] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Skipping diagnostic context enhancement for non-diagnostic phase: quiz_initiate
2025-07-22 20:00:15,899 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'quiz_initiate'
2025-07-22 20:00:15,899 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'quiz_initiate'
2025-07-22 20:00:15,900 - INFO - [main.py:9042] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Robust context prepared successfully. Phase: quiz_initiate
2025-07-22 20:00:15,900 - DEBUG - [main.py:9043] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 20:00:15,900 - INFO - [main.py:9056] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ENHANCED: Handling quiz_initiate phase...
2025-07-22 20:00:15,900 - INFO - [main.py:9060] - [47c49979-893b-41cf-8d0d-a9cd508617ab] QUIZ_INITIATE: Automatically proceeding to quiz questions (no confirmation required)
2025-07-22 20:00:15,901 - INFO - [main.py:9064] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ENHANCED: Student is ready for curriculum-aligned quiz. Generating questions...
2025-07-22 20:00:15,902 - INFO - [teaching_level_consistency.py:44] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 TEACHING_LEVEL: Determining teaching level from context
2025-07-22 20:00:15,904 - INFO - [teaching_level_consistency.py:60] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 TEACHING_LEVEL: Using Explicitly assigned teaching level = 7
2025-07-22 20:00:15,905 - INFO - [teaching_level_consistency.py:102] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔄 TEACHING_LEVEL: Propagating level 7 throughout context
2025-07-22 20:00:15,905 - DEBUG - [teaching_level_consistency.py:118] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔄 TEACHING_LEVEL: Set assigned_level_for_teaching = 7
2025-07-22 20:00:15,905 - DEBUG - [teaching_level_consistency.py:118] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔄 TEACHING_LEVEL: Set current_session_working_level = 7
2025-07-22 20:00:15,906 - DEBUG - [teaching_level_consistency.py:118] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔄 TEACHING_LEVEL: Set teaching_level = 7
2025-07-22 20:00:15,906 - DEBUG - [teaching_level_consistency.py:118] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔄 TEACHING_LEVEL: Set quiz_generation_level = 7
2025-07-22 20:00:15,906 - DEBUG - [teaching_level_consistency.py:118] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔄 TEACHING_LEVEL: Set content_generation_level = 7
2025-07-22 20:00:15,907 - INFO - [main.py:9072] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 CONSISTENT TEACHING LEVEL: Using level 7 from Explicitly assigned teaching level
2025-07-22 20:00:15,907 - INFO - [main.py:9073] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 Teaching level propagated throughout lesson context
2025-07-22 20:00:15,909 - INFO - [main.py:9096] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ENHANCED: Generating curriculum-aligned quiz questions using lesson content: 136 chars
2025-07-22 20:00:15,913 - INFO - [main.py:22864] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 CURRICULUM-ALIGNED: Generating quiz questions from lesson content (136 chars)
2025-07-22 20:00:15,915 - INFO - [main.py:22865] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 DYNAMIC DIFFICULTY: Adapting quiz complexity to teaching level 7/10
2025-07-22 20:00:19,029 - INFO - [main.py:22962] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ AI RESPONSE RECEIVED: 3524 characters
2025-07-22 20:00:19,030 - INFO - [main.py:22977] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 RAW AI RESPONSE: ```json
[
  {
    "question_number": 1,
    "question_type": "multiple_choice",
    "question": "What is the primary goal of using AI in Healthcare?",
    "options": [
      "A) To replace all human d...
2025-07-22 20:00:19,030 - INFO - [main.py:22994] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ JSON PARSED: Found 10 questions
2025-07-22 20:00:19,032 - INFO - [main.py:23031] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ QUIZ GENERATED: 10 questions at level 7
2025-07-22 20:00:19,035 - INFO - [main.py:9118] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ENHANCED: Quiz validation score: 0.00
2025-07-22 20:00:19,036 - INFO - [teaching_level_consistency.py:44] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 TEACHING_LEVEL: Determining teaching level from context
2025-07-22 20:00:19,036 - INFO - [teaching_level_consistency.py:60] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 TEACHING_LEVEL: Using Explicitly assigned teaching level = 7
2025-07-22 20:00:19,037 - INFO - [teaching_level_consistency.py:215] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 QUIZ_VALIDATION: Validating 10 questions for level 7
2025-07-22 20:00:19,037 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_1 content for level 7
2025-07-22 20:00:19,041 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_1 consistency = True, score = 1.00
2025-07-22 20:00:19,041 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_2 content for level 7
2025-07-22 20:00:19,041 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_2 consistency = True, score = 1.00
2025-07-22 20:00:19,041 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_3 content for level 7
2025-07-22 20:00:19,042 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_3 consistency = True, score = 1.00
2025-07-22 20:00:19,042 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_4 content for level 7
2025-07-22 20:00:19,042 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_4 consistency = True, score = 1.00
2025-07-22 20:00:19,042 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_5 content for level 7
2025-07-22 20:00:19,042 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_5 consistency = True, score = 1.00
2025-07-22 20:00:19,043 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_6 content for level 7
2025-07-22 20:00:19,043 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_6 consistency = True, score = 1.00
2025-07-22 20:00:19,043 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_7 content for level 7
2025-07-22 20:00:19,043 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_7 consistency = True, score = 1.00
2025-07-22 20:00:19,044 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_8 content for level 7
2025-07-22 20:00:19,044 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_8 consistency = True, score = 1.00
2025-07-22 20:00:19,044 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_9 content for level 7
2025-07-22 20:00:19,044 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_9 consistency = True, score = 1.00
2025-07-22 20:00:19,044 - INFO - [teaching_level_consistency.py:144] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: Validating quiz_question_10 content for level 7
2025-07-22 20:00:19,045 - INFO - [teaching_level_consistency.py:171] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 LEVEL_VALIDATION: quiz_question_10 consistency = True, score = 1.00
2025-07-22 20:00:19,045 - INFO - [teaching_level_consistency.py:251] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 QUIZ_VALIDATION: Alignment = True, aligned = 10/10
2025-07-22 20:00:19,045 - INFO - [main.py:9129] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ENHANCED: Quiz questions properly aligned with teaching level 7
2025-07-22 20:00:19,046 - WARNING - [main.py:9132] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ENHANCED: Quiz questions not sufficiently relevant (score: 0.00). Regenerating...
2025-07-22 20:00:19,047 - INFO - [main.py:9160] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ENHANCED: Transitioning to 'quiz_questions' phase.
2025-07-22 20:00:19,048 - DEBUG - [main.py:9161] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 ENHANCED QUIZ: Generated 10 questions, starting quiz
2025-07-22 20:00:19,048 - INFO - [main.py:9169] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ENHANCED: Quiz initiate handled, skipping normal AI processing
2025-07-22 20:00:19,048 - INFO - [main.py:9247] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ENHANCED: Quiz phase handled, skipping AI processing
2025-07-22 20:00:19,048 - DEBUG - [main.py:9248] - 🔥 QUIZ PHASE SKIPPED AI PROCESSING:
2025-07-22 20:00:19,049 - DEBUG - [main.py:9249] - 🔥   Phase: quiz_initiate
2025-07-22 20:00:19,049 - DEBUG - [main.py:9250] - 🔥   Enhanced content available: True
2025-07-22 20:00:19,049 - DEBUG - [main.py:9251] - 🔥   State updates: {'new_phase': 'quiz_questions', 'quiz_questions_generated': [{'question': 'What is the primary goal of using AI in Healthcare?', 'type': 'Multiple Choice', 'options': ['A) To replace all human doctors', 'B) To improve patient care and outcomes', 'C) To make healthcare more expensive', 'D) To only focus on administrative tasks'], 'correct_answer': 'B', 'concept': 'AI in Healthcare'}, {'question': 'Which of the following is a key area where AI is used in Healthcare for identifying illnesses?', 'type': 'Multiple Choice', 'options': ['A) Entertainment systems', 'B) Disease diagnosis', 'C) Personal finance management', 'D) Weather forecasting'], 'correct_answer': 'B', 'concept': 'disease'}, {'question': 'AI plays a significant role in accelerating the process of:', 'type': 'Multiple Choice', 'options': ['A) Patient scheduling', 'B) Drug discovery', 'C) Medical equipment repair', 'D) Hospital cleaning services'], 'correct_answer': 'B', 'concept': 'drug'}, {'question': "The 'Introduction' to AI in Healthcare highlights its potential to:", 'type': 'Multiple Choice', 'options': ['A) Decrease the availability of medical information', 'B) Make healthcare less accessible', 'C) Explore new ways to treat and prevent diseases', 'D) Reduce the importance of medical research'], 'correct_answer': 'C', 'concept': 'Introduction'}, {'question': 'AI can assist healthcare professionals in making more accurate ___________ of various conditions.', 'type': 'Fill in the Blank', 'options': [], 'correct_answer': 'diagnosis', 'concept': 'diagnosis'}, {'question': 'The __________ of AI in healthcare is to enhance efficiency and patient outcomes.', 'type': 'Fill in the Blank', 'options': [], 'correct_answer': 'role', 'concept': 'role'}, {'question': 'AI algorithms are being __________ to find new medicines and treatments.', 'type': 'Fill in the Blank', 'options': [], 'correct_answer': 'used', 'concept': 'used'}, {'question': 'Besides disease diagnosis, name one other specific application of AI in Healthcare.', 'type': 'Short Answer', 'options': [], 'correct_answer': 'Drug discovery, personalized treatment plans, medical imaging analysis, administrative task automation, patient monitoring, etc.', 'concept': 'AI in Healthcare'}, {'question': 'Explain briefly how AI can help in the discovery of new drugs.', 'type': 'Short Answer', 'options': [], 'correct_answer': 'AI can analyze vast amounts of biological data, identify potential drug candidates, predict their effectiveness, and speed up the research process.', 'concept': 'drug'}, {'question': "What does the term 'Healthcare' encompass in the context of AI applications?", 'type': 'Short Answer', 'options': [], 'correct_answer': 'Healthcare refers to the entire system of maintaining and restoring health, including prevention, diagnosis, treatment, and recovery from illness.', 'concept': 'Healthcare'}], 'quiz_started': True, 'current_quiz_question': 0, 'quiz_answers': []}
2025-07-22 20:00:19,049 - INFO - [main.py:9259] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 UNIFIED STATE MACHINE STATUS:
2025-07-22 20:00:19,049 - INFO - [main.py:9260] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   Current phase: quiz_initiate
2025-07-22 20:00:19,050 - INFO - [main.py:9261] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   Teaching interactions: 0
2025-07-22 20:00:19,050 - INFO - [main.py:9262] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   Quiz interactions: 0
2025-07-22 20:00:19,050 - INFO - [main.py:9263] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   Teaching complete: False
2025-07-22 20:00:19,050 - INFO - [main.py:9264] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   Quiz complete: False
2025-07-22 20:00:19,051 - INFO - [main.py:9265] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   Objectives coverage: 0.0%
2025-07-22 20:00:19,051 - WARNING - [main.py:10230] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 STATE UPDATE VALIDATION: current_phase='quiz_initiate', new_phase='quiz_questions'
2025-07-22 20:00:19,051 - INFO - [main.py:6365] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI state update validation passed: quiz_initiate → quiz_questions
2025-07-22 20:00:19,051 - WARNING - [main.py:10239] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 20:00:19,051 - WARNING - [main.py:10258] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔄 PHASE TRANSITION: quiz_initiate → quiz_questions
2025-07-22 20:00:19,052 - INFO - [main.py:10289] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: quiz_initiate
2025-07-22 20:00:19,052 - INFO - [main.py:10294] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 20:00:19,053 - INFO - [phase_transition_integrity.py:330] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔄 APPLYING TRANSITION WITH INTEGRITY: quiz_initiate → quiz_initiate
2025-07-22 20:00:19,053 - INFO - [phase_transition_integrity.py:293] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 📸 DATA SNAPSHOT CREATED: Session fallback-********-a9d2-44b1-a778-78455bae1f78, Phase quiz_initiate
2025-07-22 20:00:19,054 - INFO - [phase_transition_integrity.py:153] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 PHASE TRANSITION VALIDATION: quiz_initiate → quiz_initiate
2025-07-22 20:00:19,054 - ERROR - [phase_transition_integrity.py:719] - 🔍 STATE CONSISTENCY ERRORS DETECTED:
2025-07-22 20:00:19,054 - ERROR - [phase_transition_integrity.py:721] - 🔍   1. Cannot enter quiz phase without sufficient teaching
2025-07-22 20:00:19,055 - ERROR - [phase_transition_integrity.py:722] - 🔍 SESSION DATA DEBUG:
2025-07-22 20:00:19,055 - ERROR - [phase_transition_integrity.py:723] - 🔍   - diagnostic_complete: True
2025-07-22 20:00:19,055 - ERROR - [phase_transition_integrity.py:724] - 🔍   - assigned_level_for_teaching: 7
2025-07-22 20:00:19,055 - ERROR - [phase_transition_integrity.py:725] - 🔍   - teaching_complete: False
2025-07-22 20:00:19,055 - ERROR - [phase_transition_integrity.py:726] - 🔍   - teaching_interactions: 0
2025-07-22 20:00:19,056 - ERROR - [phase_transition_integrity.py:727] - 🔍   - from_phase: quiz_initiate, to_phase: quiz_initiate
2025-07-22 20:00:19,056 - ERROR - [phase_transition_integrity.py:741] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔧 STATE CORRECTION ATTEMPT: quiz_initiate → quiz_initiate
2025-07-22 20:00:19,056 - ERROR - [phase_transition_integrity.py:748] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔧 CORRECTION CHECK: diagnostic_complete=True, assigned_level=7
2025-07-22 20:00:19,056 - ERROR - [phase_transition_integrity.py:781] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔧 CORRECTION RESULT: {'corrected': False, 'corrected_phase': 'quiz_initiate', 'corrected_data': {}, 'message': 'No corrections applied'}
2025-07-22 20:00:19,056 - ERROR - [phase_transition_integrity.py:229] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ❌ STATE INCONSISTENCY: State consistency check: 1 errors found
2025-07-22 20:00:19,056 - ERROR - [phase_transition_integrity.py:389] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔧 TRANSITION RECOVERY: quiz_initiate → teaching_start
2025-07-22 20:00:19,057 - DEBUG - [phase_transition_integrity.py:847] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 20:00:19,057 - DEBUG - [phase_transition_integrity.py:880] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 📝 TRANSITION RECORDED: quiz_initiate → teaching_start (invalid)
2025-07-22 20:00:19,058 - INFO - [phase_transition_integrity.py:406] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching_start
2025-07-22 20:00:19,059 - WARNING - [main.py:10336] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔧 STATE MACHINE: Invalid transition corrected to quiz_questions
2025-07-22 20:00:19,059 - WARNING - [main.py:10363] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔧 TRANSITION RECOVERED: State inconsistency cannot be resolved: State consistency check: 1 errors found
2025-07-22 20:00:19,059 - WARNING - [main.py:10372] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 20:00:19,059 - WARNING - [main.py:10373] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   1. Input phase: 'quiz_initiate'
2025-07-22 20:00:19,060 - WARNING - [main.py:10374] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 20:00:19,060 - WARNING - [main.py:10375] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   3. Proposed phase: 'quiz_initiate'
2025-07-22 20:00:19,060 - WARNING - [main.py:10376] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 20:00:19,060 - WARNING - [main.py:10377] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍   5. Final phase to save: 'quiz_questions'
2025-07-22 20:00:19,061 - WARNING - [main.py:10380] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 💾 FINAL STATE APPLICATION:
2025-07-22 20:00:19,061 - WARNING - [main.py:10381] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 💾   - Current phase input: 'quiz_initiate'
2025-07-22 20:00:19,061 - WARNING - [main.py:10382] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 💾   - Validated state updates: 28 fields
2025-07-22 20:00:19,061 - WARNING - [main.py:10383] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 💾   - Final phase to save: 'quiz_questions'
2025-07-22 20:00:19,061 - WARNING - [main.py:10384] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 💾   - Phase change: True
2025-07-22 20:00:19,062 - WARNING - [main.py:10385] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 💾   - Integrity applied: True
2025-07-22 20:00:19,062 - INFO - [main.py:6397] - [47c49979-893b-41cf-8d0d-a9cd508617ab] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 20:00:19,062 - INFO - [main.py:6398] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Phase transition: quiz_initiate -> teaching_start
2025-07-22 20:00:19,062 - INFO - [main.py:6399] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Current level: 7
2025-07-22 20:00:19,062 - INFO - [main.py:6400] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Question index: 0
2025-07-22 20:00:19,062 - INFO - [main.py:6401] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   First encounter: False
2025-07-22 20:00:19,063 - INFO - [main.py:6406] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Answers collected: 0
2025-07-22 20:00:19,063 - INFO - [main.py:6407] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Levels failed: 0
2025-07-22 20:00:19,063 - INFO - [main.py:6365] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI state update validation passed: quiz_initiate → teaching_start
2025-07-22 20:00:19,063 - INFO - [main.py:6411] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   State update valid: True
2025-07-22 20:00:19,063 - INFO - [main.py:6418] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Diagnostic complete: False
2025-07-22 20:00:19,063 - INFO - [main.py:6420] - [47c49979-893b-41cf-8d0d-a9cd508617ab]   Assigned level: 7
2025-07-22 20:00:19,063 - WARNING - [main.py:10403] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 20:00:19,064 - INFO - [main.py:10412] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-22 20:00:19,064 - INFO - [main.py:10413] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 20:00:19,460 - WARNING - [main.py:10463] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 20:00:19,460 - WARNING - [main.py:10464] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅   - Phase: quiz_questions
2025-07-22 20:00:19,460 - WARNING - [main.py:10465] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅   - Probing Level: 7
2025-07-22 20:00:19,461 - WARNING - [main.py:10466] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅   - Question Index: 0
2025-07-22 20:00:19,461 - WARNING - [main.py:10467] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅   - Diagnostic Complete: True
2025-07-22 20:00:19,461 - WARNING - [main.py:10474] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅   - Quiz Questions Saved: 10
2025-07-22 20:00:19,461 - WARNING - [main.py:10475] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅   - Quiz Answers Saved: 0
2025-07-22 20:00:19,462 - WARNING - [main.py:10476] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅   - Quiz Started: True
2025-07-22 20:00:19,462 - DEBUG - [main.py:10525] - 🔥 STATE SAVED - Session: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: quiz_questions
2025-07-22 20:00:19,463 - DEBUG - [main.py:10526] - 🔥 QUIZ DATA - Questions: 10, Answers: 0
2025-07-22 20:00:20,243 - DEBUG - [main.py:10584] - ✅ SESSION UPDATED - ID: fallback-********-a9d2-44b1-a778-78455bae1f78, Phase: quiz_questions
2025-07-22 20:00:20,244 - DEBUG - [main.py:10585] - ✅ INTERACTION LOGGED - Phase: quiz_initiate → quiz_questions
2025-07-22 20:00:20,244 - INFO - [main.py:10591] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ Updated existing session document: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 20:00:20,244 - INFO - [main.py:18505] - [47c49979-893b-41cf-8d0d-a9cd508617ab] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 20:00:20,244 - DEBUG - [main.py:4943] - [47c49979-893b-41cf-8d0d-a9cd508617ab] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 20:00:20,245 - DEBUG - [main.py:10678] - [47c49979-893b-41cf-8d0d-a9cd508617ab] No final assessment data found in AI response
2025-07-22 20:00:20,245 - DEBUG - [main.py:10701] - [47c49979-893b-41cf-8d0d-a9cd508617ab] No lesson completion detected (Phase: teaching_start, Complete: False)
2025-07-22 20:00:20,245 - WARNING - [main.py:10720] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🔧 STATE CORRECTION: Forcing 'diagnostic_completed_this_session' to True because phase is 'teaching_start'.
2025-07-22 20:00:20,245 - DEBUG - [main.py:10725] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 20:00:20,246 - DEBUG - [main.py:10726] - 🔒   Current Phase: quiz_initiate
2025-07-22 20:00:20,246 - DEBUG - [main.py:10727] - 🔒   Final Phase: teaching_start
2025-07-22 20:00:20,246 - DEBUG - [main.py:10728] - 🔒   Diagnostic Complete: True
2025-07-22 20:00:20,247 - DEBUG - [main.py:10729] - 🔒   Assigned Level: 7
2025-07-22 20:00:20,247 - INFO - [main.py:10802] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 20:00:20,248 - INFO - [main.py:10836] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 20:00:20,248 - INFO - [main.py:10844] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 20:00:20,248 - INFO - [main.py:10849] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 20:00:20,249 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:00:20,249 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:00:20,250 - INFO - [teaching_rules.py:168] -    Teaching Level: 7 → Multiplier: 1.2
2025-07-22 20:00:20,250 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:00:20,250 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 12
2025-07-22 20:00:20,250 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.79
2025-07-22 20:00:20,251 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:00:20,251 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:00:20,251 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:00:20,251 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:00:20,251 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:00:20,251 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:00:20,252 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 7 (×1.2)
2025-07-22 20:00:20,252 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:00:20,252 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.2 × 1.0 = 12
2025-07-22 20:00:20,252 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:00:20,252 - INFO - [teaching_rules.py:405] -       Interactions: 0/12 (❌)
2025-07-22 20:00:20,253 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:00:20,253 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.79 (❌)
2025-07-22 20:00:20,253 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:00:20,254 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:00:20,254 - INFO - [main.py:10858] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 20:00:20,255 - INFO - [intelligent_guardrails.py:130] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:00:20,256 - INFO - [intelligent_guardrails.py:167] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🛡️ GUARDRAIL VIOLATION: cognitive_level_matching - critical
2025-07-22 20:00:20,256 - INFO - [intelligent_guardrails.py:473] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🛡️ CRITICAL VIOLATIONS: Adjusting content complexity
2025-07-22 20:00:20,257 - INFO - [intelligent_guardrails.py:183] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=1
2025-07-22 20:00:20,257 - INFO - [main.py:10920] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=1
2025-07-22 20:00:20,257 - WARNING - [main.py:10924] - [47c49979-893b-41cf-8d0d-a9cd508617ab] 🚨 GUARDRAIL VIOLATION: cognitive_level_matching - Content too complex for level 5 (Severity: GuardrailSeverity.CRITICAL)
2025-07-22 20:00:20,258 - INFO - [main.py:10945] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✨ GUARDRAILS ENHANCED RESPONSE: Applied pedagogical improvements
2025-07-22 20:00:20,258 - INFO - [main.py:10948] - [47c49979-893b-41cf-8d0d-a9cd508617ab] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 20:00:20,258 - DEBUG - [main.py:11005] - 🎯 RESPONSE READY:
2025-07-22 20:00:20,259 - DEBUG - [main.py:11006] - 🎯   Session: fallback-********-a9d2-44b1-a778-78455bae1f78
2025-07-22 20:00:20,259 - DEBUG - [main.py:11007] - 🎯   Phase: quiz_initiate → teaching_start
2025-07-22 20:00:20,259 - DEBUG - [main.py:11008] - 🎯   Content: Let me adjust that explanation. Simplify language ...
2025-07-22 20:00:20,259 - DEBUG - [main.py:11009] - 🎯   Request ID: 47c49979-893b-41cf-8d0d-a9cd508617ab
2025-07-22 20:00:20,260 - INFO - [main.py:11015] - [47c49979-893b-41cf-8d0d-a9cd508617ab] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 20:00:20,260 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,260 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,261 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,261 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,261 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,261 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,261 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,261 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,261 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,261 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,262 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,262 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,262 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,262 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,262 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,262 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,263 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,263 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,263 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,263 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,264 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,264 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,264 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,264 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,265 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,265 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,265 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,265 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,265 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:00:20,265 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,266 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,266 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,266 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,266 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,266 - DEBUG - [main.py:23655] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:00:20,267 - WARNING - [main.py:888] - High response time detected: 6.85s for enhance_content_api