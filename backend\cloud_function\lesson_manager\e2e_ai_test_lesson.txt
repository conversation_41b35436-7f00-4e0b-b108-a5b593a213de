2025-07-22 20:33:17,846 - INFO - [main.py:7143] - Incoming request: {"request_id": "50e22424-b93c-4cc2-8d96-d76db4a4e7e6", "timestamp": "2025-07-22T19:33:17.846493+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5", "chat_history": []}}
2025-07-22 20:33:17,847 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 20:33:17,847 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gj0qnEDDwAwV0F7rNkWh0AcRM_Vqch0hfz0_Gre-6PN9Ghpx_KAU3ISJU2J4mFlUyqTNjIYwn4CYbeHQcnCVC9TG8q0ADK-fDuwpAjd43B7b7hQvBLumyroLvpp-ck-bA7FeUD7-cawX6D5ltTIXZUOV-kLJ4yceZzYjPuKze7oeqPzjAiYvgX8J2FSG4MBaovb1BjlR-AoNq_jsjcJryDf3ndrLxmRY2_r86KIuvX3nbm29snqIUaWed6ukFgi35NpPRt1QnbaLdGL1kNzv-epB_8HoN48szPVY5NXOb2i0peCGEc2w2C_AFWqHIFviUy3v1mhg7ZLNsHKd5xFJNw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '301', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 20:33:17,847 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 50e22424-b93c-4cc2-8d96-d76db4a4e7e6
2025-07-22 20:33:17,847 - INFO - [auth_decorator.py:74] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 20:33:17,848 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 20:33:17,848 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 20:33:17,848 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 20:33:17,848 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 20:33:17,849 - INFO - [auth_decorator.py:95] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6][require_auth] Development mode detected - bypassing authentication
2025-07-22 20:33:17,849 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 20:33:17,849 - INFO - [auth_decorator.py:121] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 20:33:30,475 - INFO - [main.py:71] - ============================================================
2025-07-22 20:33:30,476 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-22 20:33:30,476 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 20:33:30,476 - INFO - [main.py:74] - Platform: win32
2025-07-22 20:33:30,476 - INFO - [main.py:75] - ============================================================
2025-07-22 20:33:30,484 - INFO - [main.py:216] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-22 20:33:30,484 - INFO - [main.py:536] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-22 20:33:30,485 - INFO - [main.py:817] - ================================================================================
2025-07-22 20:33:30,486 - INFO - [main.py:818] - LESSON MANAGER BACKEND STARTING UP
2025-07-22 20:33:30,486 - INFO - [main.py:819] - ================================================================================
2025-07-22 20:33:30,487 - INFO - [main.py:820] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 20:33:30,487 - INFO - [main.py:821] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager
2025-07-22 20:33:30,487 - INFO - [main.py:822] - Log level: DEBUG
2025-07-22 20:33:30,487 - INFO - [main.py:823] - ================================================================================
2025-07-22 20:33:30,488 - INFO - [main.py:825] - Logging configuration complete with immediate console output
2025-07-22 20:33:30,488 - INFO - [main.py:826] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-22 20:33:30,491 - INFO - [main.py:1522] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-22 20:33:30,491 - INFO - [main.py:1529] - [OK] Enhanced state and auth managers initialized successfully
2025-07-22 20:33:30,498 - INFO - [main.py:1761] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-22 20:33:30,498 - INFO - [main.py:1790] - Phase transition fixes imported successfully
2025-07-22 20:33:30,501 - INFO - [main.py:5274] - Successfully imported utils functions
2025-07-22 20:33:30,502 - INFO - [main.py:5282] - Successfully imported extract_ai_state functions
2025-07-22 20:33:30,505 - INFO - [main.py:5773] - FLASK: Using unified Firebase initialization approach...
2025-07-22 20:33:30,505 - INFO - [unified_firebase_init.py:42] - Firebase already initialized
2025-07-22 20:33:30,506 - INFO - [main.py:5781] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-22 20:33:30,506 - INFO - [main.py:5871] - Gemini API will be initialized on first use (lazy loading).
2025-07-22 20:33:30,524 - INFO - [main.py:20766] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-22 20:33:30,525 - INFO - [main.py:20809] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-22 20:33:30,536 - INFO - [main.py:2249] - Successfully imported timetable_generator functions
2025-07-22 20:33:30,549 - INFO - [main.py:27038] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-22 20:33:30,702 - INFO - [main.py:27041] - Google Cloud Storage client initialized successfully.
2025-07-22 20:33:30,994 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:33:30,995 - INFO - [auth_decorator.py:164] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:33:30,996 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 20:33:31,000 - INFO - [main.py:7327] -
================================================================================
2025-07-22 20:33:31,001 - WARNING - [main.py:7328] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 20:33:31,001 - WARNING - [main.py:7329] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 20:33:31,002 - DEBUG - [main.py:7337] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Console output error: I/O operation on closed file.
2025-07-22 20:33:31,002 - INFO - [main.py:8179] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"Start diagnostic assessment","country":"Nigeria","curriculum":"National Curriculum","grade":"primary-5","subject":"Artificial Intelligence","session_id":"fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5","chat_history":[]}...
2025-07-22 20:33:31,002 - INFO - [main.py:8181] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': 'Start diagnostic assessment', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', 'chat_history': []}
2025-07-22 20:33:31,003 - INFO - [main.py:8183] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍  - Session ID from payload: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:33:31,003 - INFO - [main.py:8184] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 20:33:31,003 - INFO - [main.py:8185] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 20:33:31,003 - DEBUG - [main.py:8223] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:33:31,003 - INFO - [main.py:8224] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:33:31,004 - INFO - [main.py:8264] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Level not provided, determined from grade 'primary-5': 5
2025-07-22 20:33:31,396 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-22 20:33:31,834 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-22 20:33:32,711 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 20:33:32,712 - INFO - [main.py:8281] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 20:33:32,712 - INFO - [main.py:8282] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 20:33:32,713 - INFO - [main.py:2639] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 PARAMETER MAPPING:
2025-07-22 20:33:32,713 - INFO - [main.py:2640] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 📥 Original: country=Nigeria, curriculum=National Curriculum, subject=Artificial Intelligence, level=5
2025-07-22 20:33:32,713 - INFO - [main.py:2641] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 📤 Mapped: country=Nigeria, curriculum=National Curriculum, subject=Artificial Intelligence, level=P5
2025-07-22 20:33:32,714 - INFO - [main.py:2646] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🎓 Grade mapping: primary-5 → Primary 5
2025-07-22 20:33:32,714 - INFO - [main.py:2648] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] fetch_lesson_data: Fetching lesson with parameters:
2025-07-22 20:33:32,714 - INFO - [main.py:2649] -   • Country (original): Nigeria
2025-07-22 20:33:32,715 - INFO - [main.py:2650] -   • Country (for Firestore): Nigeria
2025-07-22 20:33:32,715 - INFO - [main.py:2651] -   • Curriculum (original): National Curriculum
2025-07-22 20:33:32,716 - INFO - [main.py:2652] -   • Curriculum (for Firestore): National Curriculum
2025-07-22 20:33:32,716 - INFO - [main.py:2653] -   • Grade (original): primary-5
2025-07-22 20:33:32,717 - INFO - [main.py:2654] -   • Grade (normalized for Firestore): Primary 5
2025-07-22 20:33:32,718 - INFO - [main.py:2655] -   • Level (original): 5
2025-07-22 20:33:32,719 - INFO - [main.py:2656] -   • Level (for Firestore): P5
2025-07-22 20:33:32,719 - INFO - [main.py:2657] -   • Subject (original): Artificial Intelligence
2025-07-22 20:33:32,720 - INFO - [main.py:2658] -   • Subject (for Firestore): Artificial Intelligence
2025-07-22 20:33:32,720 - INFO - [main.py:2659] -   • Lesson ID: P5-AI-089
2025-07-22 20:33:32,720 - INFO - [main.py:2668] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔧 PATH CONSTRUCTION:
2025-07-22 20:33:32,721 - INFO - [main.py:2669] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]    └─ countries/Nigeria
2025-07-22 20:33:32,721 - INFO - [main.py:2670] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]       └─ curriculums/National Curriculum
2025-07-22 20:33:32,721 - INFO - [main.py:2671] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]          └─ grades/Primary 5
2025-07-22 20:33:32,721 - INFO - [main.py:2672] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]             └─ levels/P5
2025-07-22 20:33:32,722 - INFO - [main.py:2673] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]                └─ subjects/Artificial Intelligence
2025-07-22 20:33:32,722 - INFO - [main.py:2674] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]                   └─ lessonRef/P5-AI-089
2025-07-22 20:33:32,722 - INFO - [main.py:2675] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 📍 FULL PATH: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Artificial Intelligence/lessonRef/P5-AI-089
2025-07-22 20:33:32,722 - INFO - [main.py:2676] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🆔 LESSON REF: P5-AI-089
2025-07-22 20:33:32,723 - INFO - [main.py:2689] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Attempting primary path: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Artificial Intelligence/lessonRef/P5-AI-089
2025-07-22 20:33:33,031 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,031 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:33,032 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,032 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,032 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:33,032 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,032 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,032 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,033 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,033 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:33,033 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,034 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:33,034 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:33,035 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:33,035 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:33,036 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:33,037 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,037 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:33,038 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:33,038 - INFO - [main.py:2758] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Successfully retrieved and sanitized document with keys: ['additionalNotes', 'content', 'subject', 'existingAssessments', 'id', 'instructionalSteps', 'lessonTimeLength', 'learningObjectives', 'metadata', 'digitalMaterials', 'gradeLevel', 'lessonRef', 'lessonTitle', 'topic', 'country', 'extensionActivities', 'theme', 'adaptiveStrategies', 'conclusion', 'curriculumType', 'quizzesAndAssessments', 'introduction']
2025-07-22 20:33:33,038 - INFO - [main.py:17581] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔧 Enhancing lesson data compatibility
2025-07-22 20:33:33,038 - INFO - [main.py:17626] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Set grade: Primary 5
2025-07-22 20:33:33,039 - INFO - [main.py:17694] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Lesson data enhancement complete
2025-07-22 20:33:33,039 - INFO - [main.py:2847] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🗺️ Starting robust field mapping for lesson P5-AI-089
2025-07-22 20:33:33,040 - INFO - [main.py:2875] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Core fields mapped: subject=Artificial Intelligence, topic=AI in Healthcare, grade=Primary 5
2025-07-22 20:33:33,040 - INFO - [main.py:3045] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 Extracting key concepts from lesson data
2025-07-22 20:33:33,041 - INFO - [main.py:3052] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Found 0 existing key concepts
2025-07-22 20:33:33,041 - INFO - [main.py:3068] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Extracted concepts from 2 learning objectives
2025-07-22 20:33:33,041 - INFO - [main.py:3094] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Extracted concepts from instructional steps
2025-07-22 20:33:33,042 - INFO - [main.py:3112] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Added topic and subject-based concepts
2025-07-22 20:33:33,042 - INFO - [main.py:3150] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Key concepts extraction complete: 10 unique concepts
2025-07-22 20:33:33,042 - INFO - [main.py:3324] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Universal content extraction: 1020 characters from 4 steps
2025-07-22 20:33:33,042 - INFO - [main.py:3432] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Universal conversion: 4 steps → 4 sections
2025-07-22 20:33:33,043 - INFO - [main.py:2987] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Field mapping completed successfully:
2025-07-22 20:33:33,043 - INFO - [main.py:2988] -   - Subject: Artificial Intelligence
2025-07-22 20:33:33,044 - INFO - [main.py:2989] -   - Topic: AI in Healthcare
2025-07-22 20:33:33,044 - INFO - [main.py:2990] -   - Grade: Primary 5
2025-07-22 20:33:33,044 - INFO - [main.py:2991] -   - Key Concepts: 10 extracted
2025-07-22 20:33:33,045 - INFO - [main.py:2992] -   - Instructional Steps: 4
2025-07-22 20:33:33,046 - INFO - [main.py:2993] -   - Total Fields: 38
2025-07-22 20:33:33,046 - INFO - [main.py:3479] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Universal content structure recognized: instructionalSteps (4 steps)
2025-07-22 20:33:33,046 - INFO - [main.py:3500] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ All required fields present after universal mapping
2025-07-22 20:33:33,047 - INFO - [main.py:3509] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 📊 Lesson validation complete: 38 fields available for processing
2025-07-22 20:33:33,047 - INFO - [main.py:2773] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Successfully mapped lesson fields for AI inference
2025-07-22 20:33:33,047 - DEBUG - [main.py:865] - Cached result for fetch_lesson_data
2025-07-22 20:33:33,503 - INFO - [main.py:8375] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 20:33:33,503 - INFO - [main.py:8500] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Successfully retrieved lesson from primary path
2025-07-22 20:33:33,504 - INFO - [main.py:8511] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 20:33:33,504 - INFO - [main.py:8550] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 20:33:33,504 - INFO - [main.py:4456] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 20:33:33,505 - INFO - [main.py:5862] - Gemini API configured successfully with models/gemini-2.5-flash-lite-preview-06-17 and safety filters disabled.
2025-07-22 20:33:33,831 - INFO - [main.py:4522] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 20:33:33,832 - INFO - [main.py:4522] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 20:33:33,832 - INFO - [main.py:4522] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 20:33:33,832 - INFO - [main.py:4522] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 20:33:33,832 - INFO - [main.py:4522] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 20:33:33,832 - INFO - [main.py:4591] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 20:33:33,833 - DEBUG - [main.py:4605] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 20:33:33,833 - DEBUG - [main.py:4608] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 20:33:33,833 - DEBUG - [main.py:4609] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 20:33:33,833 - DEBUG - [main.py:4610] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 20:33:33,833 - INFO - [main.py:4614] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Calling Gemini API for module inference...
2025-07-22 20:33:34,895 - INFO - [main.py:4624] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Gemini API call completed in 1.06s. Raw response: 'ai_tools_and_applications'
2025-07-22 20:33:34,896 - DEBUG - [main.py:4646] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 20:33:34,897 - INFO - [main.py:4651] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 20:33:34,897 - INFO - [main.py:8584] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 20:33:34,898 - INFO - [main.py:4700] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] CACHE MISS or fetch: Getting GS levels for subject 'artificial_intelligence', module 'ai_tools_and_applications'.
2025-07-22 20:33:35,185 - INFO - [main.py:4723] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Fetched metadata for module: 'AI Tools & Applications'
2025-07-22 20:33:35,724 - INFO - [main.py:4755] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Successfully fetched 10 levels for module 'ai_tools_and_applications'.
2025-07-22 20:33:35,724 - INFO - [main.py:8621] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 20:33:36,008 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 20:33:36,501 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 20:33:36,501 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:33:36,502 - DEBUG - [main.py:8676] - 🔍   - Document Exists: False
2025-07-22 20:33:36,502 - DEBUG - [main.py:8677] - 🔍   - Current Phase: Not found
2025-07-22 20:33:36,502 - DEBUG - [main.py:8678] - 🔍   - Probing Level: Not found
2025-07-22 20:33:36,503 - DEBUG - [main.py:8679] - 🔍   - Question Index: Not found
2025-07-22 20:33:36,503 - WARNING - [main.py:8685] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 SESSION STATE DEBUG:
2025-07-22 20:33:36,504 - WARNING - [main.py:8686] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   - Session exists: False
2025-07-22 20:33:36,504 - WARNING - [main.py:8687] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   - Current phase: None
2025-07-22 20:33:36,505 - WARNING - [main.py:8688] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   - State data keys: []
2025-07-22 20:33:36,505 - DEBUG - [main.py:8706] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 20:33:36,506 - DEBUG - [main.py:8707] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Retrieved Phase: 'None'
2025-07-22 20:33:36,506 - DEBUG - [main.py:8708] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Diagnostic Completed: False
2025-07-22 20:33:36,506 - DEBUG - [main.py:8709] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Assigned Level: None
2025-07-22 20:33:36,507 - WARNING - [main.py:8710] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔒 STATE PROTECTION: phase='None', diagnostic_done=False, level=None
2025-07-22 20:33:36,507 - INFO - [main.py:8742] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 20:33:36,507 - INFO - [main.py:8743] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] State protection not triggered
2025-07-22 20:33:36,508 - INFO - [main.py:8791] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 20:33:36,508 - INFO - [main.py:8792] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   assigned_level_for_teaching (session): None
2025-07-22 20:33:36,509 - INFO - [main.py:8793] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   latest_assessed_level (profile): None
2025-07-22 20:33:36,509 - INFO - [main.py:8794] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   teaching_level_for_returning_student: None
2025-07-22 20:33:36,509 - INFO - [main.py:8795] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   has_completed_diagnostic_before: False
2025-07-22 20:33:36,510 - INFO - [main.py:8796] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   is_first_encounter_for_module: True
2025-07-22 20:33:36,511 - WARNING - [main.py:8801] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 20:33:36,512 - INFO - [main.py:8809] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 PHASE INVESTIGATION:
2025-07-22 20:33:36,512 - INFO - [main.py:8810] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Retrieved from Firestore: 'None'
2025-07-22 20:33:36,513 - INFO - [main.py:8811] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 20:33:36,513 - INFO - [main.py:8812] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Is first encounter: True
2025-07-22 20:33:36,513 - INFO - [main.py:8813] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Diagnostic completed: False
2025-07-22 20:33:36,514 - INFO - [main.py:8826] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   No stored phase found, starting with: 'smart_diagnostic_start'
2025-07-22 20:33:36,514 - INFO - [main.py:8833] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 20:33:36,515 - INFO - [main.py:8835] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Final phase for AI logic: smart_diagnostic_start
2025-07-22 20:33:36,515 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 20:33:36,515 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 20:33:36,516 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 20:33:36,516 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 20:33:36,516 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 20:33:36,517 - WARNING - [main.py:4150] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] State key 'current_probing_level_number' had non-numeric value 'None', using default 2.
2025-07-22 20:33:36,517 - INFO - [main.py:8855] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] NEW SESSION: Forcing question_index to 0 (was: N/A)
2025-07-22 20:33:36,517 - INFO - [main.py:6144] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Diagnostic context validation passed
2025-07-22 20:33:36,518 - WARNING - [main.py:9007] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_start' for first encounter
2025-07-22 20:33:36,518 - INFO - [main.py:9030] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_start
2025-07-22 20:33:36,518 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_start'
2025-07-22 20:33:36,518 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_start'
2025-07-22 20:33:36,519 - INFO - [main.py:9042] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Robust context prepared successfully. Phase: smart_diagnostic_start
2025-07-22 20:33:36,519 - DEBUG - [main.py:9043] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 20:33:36,519 - INFO - [main.py:9285] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_start
2025-07-22 20:33:36,520 - INFO - [main.py:9286] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🎯 UNIFIED STATE MACHINE: Processing user query: Start diagnostic assessment...
2025-07-22 20:33:36,520 - INFO - [main.py:9329] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🎯 ROUTING: Smart Diagnostic phase 'smart_diagnostic_start' - proceeding to main AI processing
2025-07-22 20:33:36,520 - INFO - [main.py:9349] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🤖 MAIN AI PROCESSING: No specific handler, calling main AI for phase 'smart_diagnostic_start'
2025-07-22 20:33:36,522 - INFO - [main.py:11193] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:33:36,522 - DEBUG - [main.py:11202] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Console output error: I/O operation on closed file.
2025-07-22 20:33:36,523 - INFO - [main.py:11206] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🚀 First interaction - initializing chat session
2025-07-22 20:33:36,523 - INFO - [main.py:5921] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🚀 Initializing chat session for lesson: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:33:36,523 - INFO - [main.py:6005] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Creating system prompt for Andrea, Grade primary-5, Topic: AI in Healthcare
2025-07-22 20:33:36,524 - INFO - [main.py:5936] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 💰 Making SINGLE API call to initialize lesson session
2025-07-22 20:33:37,109 - INFO - [main.py:5944] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ Lesson session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5 initialized successfully
2025-07-22 20:33:37,109 - INFO - [main.py:5945] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 💰 COST OPTIMIZATION: All subsequent interactions will use NO additional API calls
2025-07-22 20:33:37,110 - INFO - [main.py:9360] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ MAIN AI PROCESSING: Completed successfully
2025-07-22 20:33:37,110 - INFO - [main.py:9371] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 20:33:37,111 - INFO - [main.py:5710] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔄 PHASE TRANSITION: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 20:33:37,111 - INFO - [phase_transition_integrity.py:153] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 20:33:37,111 - INFO - [phase_transition_integrity.py:220] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ TRANSITION VALIDATED: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 20:33:37,112 - INFO - [main.py:5718] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ VALID TRANSITION: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 20:33:37,112 - INFO - [main.py:9387] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 20:33:37,112 - INFO - [main.py:9388] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 20:33:37,480 - INFO - [main.py:9412] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 20:33:37,481 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:33:37,482 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:33:37,482 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:33:37,482 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:33:37,482 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:33:37,483 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:33:37,483 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:33:37,484 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:33:37,485 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:33:37,485 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:33:37,486 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:33:37,486 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:33:37,486 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:33:37,487 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:33:37,487 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:33:37,488 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:33:37,488 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:33:37,488 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:33:37,489 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:33:37,489 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:33:37,489 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:33:37,490 - INFO - [intelligent_guardrails.py:130] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:33:37,491 - INFO - [intelligent_guardrails.py:183] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:33:37,491 - INFO - [intelligent_guardrails.py:675] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 20:33:37,492 - INFO - [main.py:9544] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 20:33:37,492 - WARNING - [main.py:9593] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🤖 AI RESPONSE RECEIVED:
2025-07-22 20:33:37,492 - WARNING - [main.py:9594] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🤖   - Content length: 289 chars
2025-07-22 20:33:37,493 - WARNING - [main.py:9595] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q1'}
2025-07-22 20:33:37,494 - WARNING - [main.py:9596] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🤖   - Raw state block: None...
2025-07-22 20:33:37,494 - DEBUG - [main.py:10129] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 20:33:37,495 - DEBUG - [main.py:10130] - 🤖   Content: Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions t...
2025-07-22 20:33:37,495 - DEBUG - [main.py:10131] - 🤖   State: {'new_phase': 'smart_diagnostic_q1'}
2025-07-22 20:33:37,495 - INFO - [main.py:10157] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 20:33:37,495 - INFO - [main.py:10158] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q1, Session=smart_diagnostic_start, Final=smart_diagnostic_q1
2025-07-22 20:33:37,880 - WARNING - [main.py:10260] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_start', new_phase='smart_diagnostic_q1'
2025-07-22 20:33:37,881 - INFO - [main.py:6365] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI state update validation passed: smart_diagnostic_start → smart_diagnostic_q1
2025-07-22 20:33:37,881 - WARNING - [main.py:10269] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 20:33:37,882 - WARNING - [main.py:10288] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔄 PHASE TRANSITION: smart_diagnostic_start → smart_diagnostic_q1
2025-07-22 20:33:37,883 - INFO - [main.py:10319] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 20:33:37,884 - INFO - [main.py:10324] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 20:33:37,885 - INFO - [phase_transition_integrity.py:315] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_start → teaching_start
2025-07-22 20:33:37,886 - INFO - [phase_transition_integrity.py:278] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 📸 DATA SNAPSHOT CREATED: Session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase smart_diagnostic_start
2025-07-22 20:33:37,887 - WARNING - [phase_transition_integrity.py:327] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 20:33:37,888 - WARNING - [phase_transition_integrity.py:328] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 20:33:37,888 - WARNING - [phase_transition_integrity.py:329] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 20:33:37,889 - WARNING - [phase_transition_integrity.py:330] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 20:33:37,890 - WARNING - [phase_transition_integrity.py:332] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   - assigned_level_for_teaching value: None
2025-07-22 20:33:37,890 - WARNING - [phase_transition_integrity.py:333] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   - state_updates keys: ['new_phase']
2025-07-22 20:33:37,891 - WARNING - [phase_transition_integrity.py:334] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 20:33:37,891 - INFO - [phase_transition_integrity.py:153] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_start → teaching_start
2025-07-22 20:33:37,892 - WARNING - [phase_transition_integrity.py:169] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ❌ INVALID TRANSITION: smart_diagnostic_start → teaching_start
2025-07-22 20:33:37,892 - ERROR - [phase_transition_integrity.py:374] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔧 TRANSITION RECOVERY: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 20:33:37,893 - DEBUG - [phase_transition_integrity.py:832] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 20:33:37,894 - DEBUG - [phase_transition_integrity.py:865] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 📝 TRANSITION RECORDED: smart_diagnostic_start → smart_diagnostic_start (invalid)
2025-07-22 20:33:37,895 - INFO - [phase_transition_integrity.py:391] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 20:33:37,896 - WARNING - [main.py:10366] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q1
2025-07-22 20:33:37,896 - WARNING - [main.py:10393] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔧 TRANSITION RECOVERED: Invalid transition: smart_diagnostic_start → teaching_start not allowed
2025-07-22 20:33:37,897 - WARNING - [main.py:10402] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 20:33:37,898 - WARNING - [main.py:10403] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   1. Input phase: 'smart_diagnostic_start'
2025-07-22 20:33:37,899 - WARNING - [main.py:10404] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 20:33:37,899 - WARNING - [main.py:10405] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 20:33:37,900 - WARNING - [main.py:10406] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 20:33:37,901 - WARNING - [main.py:10407] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍   5. Final phase to save: 'smart_diagnostic_q1'
2025-07-22 20:33:37,902 - WARNING - [main.py:10410] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 💾 FINAL STATE APPLICATION:
2025-07-22 20:33:37,903 - WARNING - [main.py:10411] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 💾   - Current phase input: 'smart_diagnostic_start'
2025-07-22 20:33:37,903 - WARNING - [main.py:10412] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 💾   - Validated state updates: 26 fields
2025-07-22 20:33:37,904 - WARNING - [main.py:10413] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 💾   - Final phase to save: 'smart_diagnostic_q1'
2025-07-22 20:33:37,905 - WARNING - [main.py:10414] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 💾   - Phase change: True
2025-07-22 20:33:37,906 - WARNING - [main.py:10415] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 💾   - Integrity applied: True
2025-07-22 20:33:37,906 - INFO - [main.py:6397] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 20:33:37,907 - INFO - [main.py:6398] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Phase transition: smart_diagnostic_start -> smart_diagnostic_start
2025-07-22 20:33:37,907 - INFO - [main.py:6399] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Current level: 2
2025-07-22 20:33:37,908 - INFO - [main.py:6400] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Question index: 0
2025-07-22 20:33:37,909 - INFO - [main.py:6401] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   First encounter: True
2025-07-22 20:33:37,909 - INFO - [main.py:6406] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Answers collected: 0
2025-07-22 20:33:37,909 - INFO - [main.py:6407] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Levels failed: 0
2025-07-22 20:33:37,910 - INFO - [main.py:6365] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI state update validation passed: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 20:33:37,911 - INFO - [main.py:6411] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   State update valid: True
2025-07-22 20:33:37,911 - INFO - [main.py:6418] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6]   Diagnostic complete: False
2025-07-22 20:33:37,912 - WARNING - [main.py:10433] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 20:33:37,913 - INFO - [main.py:10442] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-22 20:33:37,913 - INFO - [main.py:10443] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 20:33:38,320 - WARNING - [main.py:10493] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 20:33:38,320 - WARNING - [main.py:10494] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅   - Phase: smart_diagnostic_q1
2025-07-22 20:33:38,321 - WARNING - [main.py:10495] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅   - Probing Level: 2
2025-07-22 20:33:38,321 - WARNING - [main.py:10496] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅   - Question Index: 0
2025-07-22 20:33:38,321 - WARNING - [main.py:10497] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅   - Diagnostic Complete: False
2025-07-22 20:33:38,322 - WARNING - [main.py:10504] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅   - Quiz Questions Saved: 0
2025-07-22 20:33:38,322 - WARNING - [main.py:10505] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅   - Quiz Answers Saved: 0
2025-07-22 20:33:38,323 - WARNING - [main.py:10506] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅   - Quiz Started: False
2025-07-22 20:33:38,324 - DEBUG - [main.py:10555] - 🔥 STATE SAVED - Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: smart_diagnostic_q1
2025-07-22 20:33:38,325 - DEBUG - [main.py:10556] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 20:33:39,137 - INFO - [main.py:10600] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Created new session document: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:33:39,138 - INFO - [main.py:18535] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 20:33:39,138 - DEBUG - [main.py:4943] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 20:33:39,139 - DEBUG - [main.py:10708] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] No final assessment data found in AI response
2025-07-22 20:33:39,139 - DEBUG - [main.py:10731] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 20:33:39,140 - DEBUG - [main.py:10755] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 20:33:39,140 - DEBUG - [main.py:10756] - 🔒   Current Phase: smart_diagnostic_start
2025-07-22 20:33:39,141 - DEBUG - [main.py:10757] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 20:33:39,141 - DEBUG - [main.py:10758] - 🔒   Diagnostic Complete: False
2025-07-22 20:33:39,143 - DEBUG - [main.py:10759] - 🔒   Assigned Level: None
2025-07-22 20:33:39,143 - INFO - [main.py:10832] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 20:33:39,144 - INFO - [main.py:10866] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 20:33:39,144 - INFO - [main.py:10874] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 20:33:39,145 - INFO - [main.py:10879] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 20:33:39,145 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:33:39,145 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:33:39,145 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:33:39,146 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:33:39,146 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:33:39,146 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:33:39,146 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:33:39,147 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:33:39,147 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:33:39,147 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:33:39,148 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:33:39,148 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:33:39,148 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:33:39,148 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:33:39,149 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:33:39,149 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:33:39,149 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:33:39,149 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:33:39,150 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:33:39,150 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:33:39,150 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:33:39,150 - INFO - [main.py:10888] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 20:33:39,151 - INFO - [intelligent_guardrails.py:130] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:33:39,151 - INFO - [intelligent_guardrails.py:183] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:33:39,151 - INFO - [main.py:10950] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 20:33:39,152 - INFO - [main.py:10978] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 20:33:39,152 - DEBUG - [main.py:11035] - 🎯 RESPONSE READY:
2025-07-22 20:33:39,152 - DEBUG - [main.py:11036] - 🎯   Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:33:39,153 - DEBUG - [main.py:11037] - 🎯   Phase: smart_diagnostic_start → smart_diagnostic_start
2025-07-22 20:33:39,153 - DEBUG - [main.py:11038] - 🎯   Content: Hello Andrea! Welcome to your lesson on AI in Heal...
2025-07-22 20:33:39,153 - DEBUG - [main.py:11039] - 🎯   Request ID: 50e22424-b93c-4cc2-8d96-d76db4a4e7e6
2025-07-22 20:33:39,153 - INFO - [main.py:11045] - [50e22424-b93c-4cc2-8d96-d76db4a4e7e6] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 20:33:39,154 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,154 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,154 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,154 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:39,154 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:39,155 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:39,155 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:39,155 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,155 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:33:39,155 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,155 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,156 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,156 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,156 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,156 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:33:39,157 - WARNING - [main.py:888] - High response time detected: 8.16s for enhance_content_api
2025-07-22 20:34:31,365 - INFO - [main.py:7143] - Incoming request: {"request_id": "f9f0360e-a576-470e-844c-fc8c4f14174d", "timestamp": "2025-07-22T19:34:31.365133+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-22T19:33:39.209Z"}]}}
2025-07-22 20:34:31,366 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 20:34:31,366 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gj0qnEDDwAwV0F7rNkWh0AcRM_Vqch0hfz0_Gre-6PN9Ghpx_KAU3ISJU2J4mFlUyqTNjIYwn4CYbeHQcnCVC9TG8q0ADK-fDuwpAjd43B7b7hQvBLumyroLvpp-ck-bA7FeUD7-cawX6D5ltTIXZUOV-kLJ4yceZzYjPuKze7oeqPzjAiYvgX8J2FSG4MBaovb1BjlR-AoNq_jsjcJryDf3ndrLxmRY2_r86KIuvX3nbm29snqIUaWed6ukFgi35NpPRt1QnbaLdGL1kNzv-epB_8HoN48szPVY5NXOb2i0peCGEc2w2C_AFWqHIFviUy3v1mhg7ZLNsHKd5xFJNw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '1132', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 20:34:31,366 - INFO - [auth_decorator.py:70] - 🔒 Request ID: f9f0360e-a576-470e-844c-fc8c4f14174d
2025-07-22 20:34:31,367 - INFO - [auth_decorator.py:74] - [f9f0360e-a576-470e-844c-fc8c4f14174d][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 20:34:31,367 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 20:34:31,367 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 20:34:31,367 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 20:34:31,367 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 20:34:31,368 - INFO - [auth_decorator.py:95] - [f9f0360e-a576-470e-844c-fc8c4f14174d][require_auth] Development mode detected - bypassing authentication
2025-07-22 20:34:31,368 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 20:34:31,368 - INFO - [auth_decorator.py:121] - [f9f0360e-a576-470e-844c-fc8c4f14174d][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 20:34:31,699 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:34:31,700 - INFO - [auth_decorator.py:164] - [f9f0360e-a576-470e-844c-fc8c4f14174d][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:34:31,701 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 20:34:31,703 - INFO - [main.py:7327] -
================================================================================
2025-07-22 20:34:31,703 - WARNING - [main.py:7328] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 20:34:31,703 - WARNING - [main.py:7329] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 20:34:31,704 - DEBUG - [main.py:7337] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Console output error: I/O operation on closed file.
2025-07-22 20:34:31,705 - INFO - [main.py:8179] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when t...
2025-07-22 20:34:31,706 - INFO - [main.py:8181] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-22T19:33:39.209Z'}]}
2025-07-22 20:34:31,706 - INFO - [main.py:8183] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍  - Session ID from payload: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:34:31,707 - INFO - [main.py:8184] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 20:34:31,707 - INFO - [main.py:8185] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 20:34:31,707 - DEBUG - [main.py:8223] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:34:31,708 - INFO - [main.py:8224] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:34:31,708 - INFO - [main.py:8264] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Level not provided, determined from grade 'primary-5': 5
2025-07-22 20:34:31,998 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 20:34:31,999 - INFO - [main.py:8281] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 20:34:31,999 - INFO - [main.py:8282] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 20:34:32,000 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 20:34:32,524 - INFO - [main.py:8312] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 20:34:32,524 - INFO - [main.py:8375] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 20:34:32,524 - INFO - [main.py:8500] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ Successfully retrieved lesson from primary path
2025-07-22 20:34:32,525 - INFO - [main.py:8511] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 20:34:32,525 - INFO - [main.py:8550] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 20:34:32,525 - INFO - [main.py:4456] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 20:34:32,898 - INFO - [main.py:4522] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 20:34:32,898 - INFO - [main.py:4522] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 20:34:32,899 - INFO - [main.py:4522] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 20:34:32,899 - INFO - [main.py:4522] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 20:34:32,900 - INFO - [main.py:4522] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 20:34:32,900 - INFO - [main.py:4591] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 20:34:32,900 - DEBUG - [main.py:4605] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 20:34:32,901 - DEBUG - [main.py:4608] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 20:34:32,902 - DEBUG - [main.py:4609] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 20:34:32,902 - DEBUG - [main.py:4610] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 20:34:32,903 - INFO - [main.py:4614] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Calling Gemini API for module inference...
2025-07-22 20:34:33,286 - INFO - [main.py:4624] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Gemini API call completed in 0.38s. Raw response: 'ai_tools_and_applications'
2025-07-22 20:34:33,286 - DEBUG - [main.py:4646] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 20:34:33,287 - INFO - [main.py:4651] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 20:34:33,287 - INFO - [main.py:8584] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 20:34:33,287 - INFO - [main.py:8621] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 20:34:33,568 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 20:34:34,095 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 20:34:34,095 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:34:34,096 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 20:34:34,096 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q1
2025-07-22 20:34:34,096 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 20:34:34,096 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 20:34:34,097 - WARNING - [main.py:8685] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 SESSION STATE DEBUG:
2025-07-22 20:34:34,097 - WARNING - [main.py:8686] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   - Session exists: True
2025-07-22 20:34:34,097 - WARNING - [main.py:8687] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   - Current phase: smart_diagnostic_q1
2025-07-22 20:34:34,098 - WARNING - [main.py:8688] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   - State data keys: ['created_at', 'session_id', 'quiz_complete', 'quiz_questions_generated', 'lesson_start_time', 'last_diagnostic_question_text_asked', 'last_updated', 'teaching_complete', 'current_phase', 'quiz_performance', 'diagnostic_completed_this_session', 'is_first_encounter_for_module', 'current_probing_level_number', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'current_quiz_question', 'quiz_interactions', 'student_id', 'teaching_interactions', 'quiz_started', 'latest_assessed_level_for_module', 'student_answers_for_probing_level', 'quiz_answers', 'current_session_working_level']
2025-07-22 20:34:34,098 - DEBUG - [main.py:8706] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 20:34:34,099 - DEBUG - [main.py:8707] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Retrieved Phase: 'smart_diagnostic_q1'
2025-07-22 20:34:34,099 - DEBUG - [main.py:8708] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Diagnostic Completed: False
2025-07-22 20:34:34,100 - DEBUG - [main.py:8709] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Assigned Level: None
2025-07-22 20:34:34,101 - WARNING - [main.py:8710] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔒 STATE PROTECTION: phase='smart_diagnostic_q1', diagnostic_done=False, level=None
2025-07-22 20:34:34,101 - INFO - [main.py:8742] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 20:34:34,102 - INFO - [main.py:8743] - [f9f0360e-a576-470e-844c-fc8c4f14174d] State protection not triggered
2025-07-22 20:34:34,102 - INFO - [main.py:8791] - [f9f0360e-a576-470e-844c-fc8c4f14174d]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 20:34:34,103 - INFO - [main.py:8792] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   assigned_level_for_teaching (session): None
2025-07-22 20:34:34,103 - INFO - [main.py:8793] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   latest_assessed_level (profile): None
2025-07-22 20:34:34,104 - INFO - [main.py:8794] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   teaching_level_for_returning_student: None
2025-07-22 20:34:34,104 - INFO - [main.py:8795] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   has_completed_diagnostic_before: False
2025-07-22 20:34:34,104 - INFO - [main.py:8796] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   is_first_encounter_for_module: True
2025-07-22 20:34:34,105 - WARNING - [main.py:8801] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 20:34:34,105 - INFO - [main.py:8809] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 PHASE INVESTIGATION:
2025-07-22 20:34:34,105 - INFO - [main.py:8810] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Retrieved from Firestore: 'smart_diagnostic_q1'
2025-07-22 20:34:34,106 - INFO - [main.py:8811] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 20:34:34,106 - INFO - [main.py:8812] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Is first encounter: True
2025-07-22 20:34:34,106 - INFO - [main.py:8813] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Diagnostic completed: False
2025-07-22 20:34:34,106 - INFO - [main.py:8819] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ Using stored phase from Firestore: 'smart_diagnostic_q1'
2025-07-22 20:34:34,107 - INFO - [main.py:8833] - [f9f0360e-a576-470e-844c-fc8c4f14174d] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 20:34:34,107 - INFO - [main.py:8835] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Final phase for AI logic: smart_diagnostic_q1
2025-07-22 20:34:34,107 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 20:34:34,107 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 20:34:34,108 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 20:34:34,108 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 20:34:34,108 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 20:34:34,108 - INFO - [main.py:8855] - [f9f0360e-a576-470e-844c-fc8c4f14174d] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 20:34:34,108 - INFO - [main.py:6144] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Diagnostic context validation passed
2025-07-22 20:34:34,109 - WARNING - [main.py:9007] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q1' for first encounter
2025-07-22 20:34:34,109 - INFO - [main.py:9030] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q1
2025-07-22 20:34:34,109 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q1'
2025-07-22 20:34:34,110 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q1'
2025-07-22 20:34:34,110 - INFO - [main.py:9042] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Robust context prepared successfully. Phase: smart_diagnostic_q1
2025-07-22 20:34:34,111 - DEBUG - [main.py:9043] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 20:34:34,112 - INFO - [main.py:9285] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q1
2025-07-22 20:34:34,113 - INFO - [main.py:9286] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎯 UNIFIED STATE MACHINE: Processing user query: Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctor...
2025-07-22 20:34:34,113 - INFO - [main.py:9329] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎯 ROUTING: Smart Diagnostic phase 'smart_diagnostic_q1' - proceeding to main AI processing
2025-07-22 20:34:34,114 - INFO - [main.py:9349] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🤖 MAIN AI PROCESSING: No specific handler, calling main AI for phase 'smart_diagnostic_q1'
2025-07-22 20:34:34,114 - INFO - [main.py:11193] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:34:34,114 - DEBUG - [main.py:11202] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Console output error: I/O operation on closed file.
2025-07-22 20:34:34,115 - INFO - [main.py:11235] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 📤 Sending message to existing session: Hello again! I'm ready for my lesson! AI in health...
2025-07-22 20:34:34,115 - INFO - [main.py:5965] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 💰 Sending message to existing session (NO API CALL)
2025-07-22 20:34:34,863 - INFO - [main.py:5970] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ Response received from session (NO API CALL COST)
2025-07-22 20:34:34,863 - INFO - [main.py:11242] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 📥 Received response from session: That's a fantastic start, Andrea! You've already grasped some really important ideas about how AI ca...
2025-07-22 20:34:34,868 - INFO - [main.py:11333] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q1
2025-07-22 20:34:34,869 - INFO - [main.py:11391] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 📝 Diagnostic in progress, checking for question progression
2025-07-22 20:34:34,869 - INFO - [main.py:11422] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-22 20:34:34,869 - INFO - [main.py:9360] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ MAIN AI PROCESSING: Completed successfully
2025-07-22 20:34:34,870 - INFO - [main.py:9371] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 20:34:34,870 - INFO - [main.py:5710] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔄 PHASE TRANSITION: smart_diagnostic_q1 → smart_diagnostic_q1
2025-07-22 20:34:34,870 - INFO - [phase_transition_integrity.py:153] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q1 → smart_diagnostic_q1
2025-07-22 20:34:34,870 - INFO - [phase_transition_integrity.py:220] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ TRANSITION VALIDATED: smart_diagnostic_q1 → smart_diagnostic_q1
2025-07-22 20:34:34,870 - INFO - [main.py:5718] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ VALID TRANSITION: smart_diagnostic_q1 → smart_diagnostic_q1
2025-07-22 20:34:34,871 - INFO - [main.py:9387] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q1 → smart_diagnostic_q1
2025-07-22 20:34:34,871 - INFO - [main.py:9388] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 20:34:35,674 - INFO - [main.py:9402] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ UNIFIED STATE MACHINE: Session state updated with phase smart_diagnostic_q1
2025-07-22 20:34:35,674 - INFO - [main.py:9412] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 20:34:35,675 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:34:35,675 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:34:35,675 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:34:35,675 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:34:35,675 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:34:35,675 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:34:35,675 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:34:35,676 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:34:35,676 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:34:35,676 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:34:35,677 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:34:35,677 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:34:35,678 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:34:35,678 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:34:35,679 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:34:35,679 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:34:35,680 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:34:35,680 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:34:35,680 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:34:35,681 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:34:35,681 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:34:35,682 - INFO - [intelligent_guardrails.py:130] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:34:35,683 - INFO - [intelligent_guardrails.py:183] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:34:35,683 - INFO - [intelligent_guardrails.py:675] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 20:34:35,684 - INFO - [main.py:9544] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 20:34:35,684 - WARNING - [main.py:9593] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🤖 AI RESPONSE RECEIVED:
2025-07-22 20:34:35,684 - WARNING - [main.py:9594] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🤖   - Content length: 446 chars
2025-07-22 20:34:35,684 - WARNING - [main.py:9595] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q2'}
2025-07-22 20:34:35,685 - WARNING - [main.py:9596] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🤖   - Raw state block: None...
2025-07-22 20:34:35,685 - DEBUG - [main.py:10129] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 20:34:35,685 - DEBUG - [main.py:10130] - 🤖   Content: That's a fantastic start, Andrea! You've already grasped some really important ideas about how AI ca...
2025-07-22 20:34:35,685 - DEBUG - [main.py:10131] - 🤖   State: {'new_phase': 'smart_diagnostic_q2'}
2025-07-22 20:34:35,686 - INFO - [main.py:10157] - [f9f0360e-a576-470e-844c-fc8c4f14174d] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 20:34:35,686 - INFO - [main.py:10158] - [f9f0360e-a576-470e-844c-fc8c4f14174d] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q2, Session=smart_diagnostic_q1, Final=smart_diagnostic_q2
2025-07-22 20:34:35,976 - WARNING - [main.py:10260] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q1', new_phase='smart_diagnostic_q2'
2025-07-22 20:34:35,977 - INFO - [main.py:6365] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI state update validation passed: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-22 20:34:35,978 - WARNING - [main.py:10269] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 20:34:35,979 - WARNING - [main.py:10288] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔄 PHASE TRANSITION: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-22 20:34:35,980 - INFO - [main.py:10319] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 20:34:35,981 - INFO - [main.py:10324] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 20:34:35,982 - INFO - [phase_transition_integrity.py:315] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q1 → teaching_start
2025-07-22 20:34:35,983 - INFO - [phase_transition_integrity.py:278] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 📸 DATA SNAPSHOT CREATED: Session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase smart_diagnostic_q1
2025-07-22 20:34:35,984 - WARNING - [phase_transition_integrity.py:327] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 20:34:35,985 - WARNING - [phase_transition_integrity.py:328] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 20:34:35,985 - WARNING - [phase_transition_integrity.py:329] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 20:34:35,985 - WARNING - [phase_transition_integrity.py:330] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 20:34:35,986 - WARNING - [phase_transition_integrity.py:332] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   - assigned_level_for_teaching value: None
2025-07-22 20:34:35,986 - WARNING - [phase_transition_integrity.py:333] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   - state_updates keys: ['new_phase']
2025-07-22 20:34:35,987 - WARNING - [phase_transition_integrity.py:334] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 20:34:35,987 - INFO - [phase_transition_integrity.py:153] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q1 → teaching_start
2025-07-22 20:34:35,988 - WARNING - [phase_transition_integrity.py:169] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ❌ INVALID TRANSITION: smart_diagnostic_q1 → teaching_start
2025-07-22 20:34:35,989 - ERROR - [phase_transition_integrity.py:374] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔧 TRANSITION RECOVERY: smart_diagnostic_q1 → smart_diagnostic_start
2025-07-22 20:34:35,989 - DEBUG - [phase_transition_integrity.py:832] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 20:34:35,990 - DEBUG - [phase_transition_integrity.py:865] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 📝 TRANSITION RECORDED: smart_diagnostic_q1 → smart_diagnostic_start (invalid)
2025-07-22 20:34:35,990 - INFO - [phase_transition_integrity.py:391] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 20:34:35,991 - WARNING - [main.py:10366] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q2
2025-07-22 20:34:35,991 - WARNING - [main.py:10393] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔧 TRANSITION RECOVERED: Invalid transition: smart_diagnostic_q1 → teaching_start not allowed
2025-07-22 20:34:35,992 - WARNING - [main.py:10402] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 20:34:35,993 - WARNING - [main.py:10403] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   1. Input phase: 'smart_diagnostic_q1'
2025-07-22 20:34:35,993 - WARNING - [main.py:10404] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 20:34:35,995 - WARNING - [main.py:10405] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 20:34:35,996 - WARNING - [main.py:10406] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 20:34:35,997 - WARNING - [main.py:10407] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍   5. Final phase to save: 'smart_diagnostic_q2'
2025-07-22 20:34:35,997 - WARNING - [main.py:10410] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 💾 FINAL STATE APPLICATION:
2025-07-22 20:34:35,998 - WARNING - [main.py:10411] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 💾   - Current phase input: 'smart_diagnostic_q1'
2025-07-22 20:34:35,998 - WARNING - [main.py:10412] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 💾   - Validated state updates: 26 fields
2025-07-22 20:34:35,999 - WARNING - [main.py:10413] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 💾   - Final phase to save: 'smart_diagnostic_q2'
2025-07-22 20:34:35,999 - WARNING - [main.py:10414] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 💾   - Phase change: True
2025-07-22 20:34:36,000 - WARNING - [main.py:10415] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 💾   - Integrity applied: True
2025-07-22 20:34:36,000 - INFO - [main.py:6397] - [f9f0360e-a576-470e-844c-fc8c4f14174d] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 20:34:36,000 - INFO - [main.py:6398] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Phase transition: smart_diagnostic_q1 -> smart_diagnostic_start
2025-07-22 20:34:36,001 - INFO - [main.py:6399] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Current level: 2
2025-07-22 20:34:36,001 - INFO - [main.py:6400] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Question index: 0
2025-07-22 20:34:36,001 - INFO - [main.py:6401] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   First encounter: True
2025-07-22 20:34:36,002 - INFO - [main.py:6406] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Answers collected: 0
2025-07-22 20:34:36,002 - INFO - [main.py:6407] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Levels failed: 0
2025-07-22 20:34:36,002 - INFO - [main.py:6365] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI state update validation passed: smart_diagnostic_q1 → smart_diagnostic_start
2025-07-22 20:34:36,002 - INFO - [main.py:6411] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   State update valid: True
2025-07-22 20:34:36,003 - INFO - [main.py:6418] - [f9f0360e-a576-470e-844c-fc8c4f14174d]   Diagnostic complete: False
2025-07-22 20:34:36,003 - WARNING - [main.py:10433] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 20:34:36,003 - INFO - [main.py:10442] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-22 20:34:36,004 - INFO - [main.py:10443] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 20:34:36,487 - WARNING - [main.py:10493] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 20:34:36,487 - WARNING - [main.py:10494] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅   - Phase: smart_diagnostic_q2
2025-07-22 20:34:36,487 - WARNING - [main.py:10495] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅   - Probing Level: 2
2025-07-22 20:34:36,488 - WARNING - [main.py:10496] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅   - Question Index: 0
2025-07-22 20:34:36,488 - WARNING - [main.py:10497] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅   - Diagnostic Complete: False
2025-07-22 20:34:36,488 - WARNING - [main.py:10504] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅   - Quiz Questions Saved: 0
2025-07-22 20:34:36,488 - WARNING - [main.py:10505] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅   - Quiz Answers Saved: 0
2025-07-22 20:34:36,489 - WARNING - [main.py:10506] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅   - Quiz Started: False
2025-07-22 20:34:36,489 - DEBUG - [main.py:10555] - 🔥 STATE SAVED - Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: smart_diagnostic_q2
2025-07-22 20:34:36,489 - DEBUG - [main.py:10556] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 20:34:37,346 - DEBUG - [main.py:10614] - ✅ SESSION UPDATED - ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: smart_diagnostic_q2
2025-07-22 20:34:37,346 - DEBUG - [main.py:10615] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q1 → smart_diagnostic_q2
2025-07-22 20:34:37,346 - INFO - [main.py:10621] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ Updated existing session document: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:34:37,347 - INFO - [main.py:18535] - [f9f0360e-a576-470e-844c-fc8c4f14174d] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 20:34:37,347 - DEBUG - [main.py:4943] - [f9f0360e-a576-470e-844c-fc8c4f14174d] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 20:34:37,347 - DEBUG - [main.py:10708] - [f9f0360e-a576-470e-844c-fc8c4f14174d] No final assessment data found in AI response
2025-07-22 20:34:37,347 - DEBUG - [main.py:10731] - [f9f0360e-a576-470e-844c-fc8c4f14174d] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 20:34:37,348 - DEBUG - [main.py:10755] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 20:34:37,348 - DEBUG - [main.py:10756] - 🔒   Current Phase: smart_diagnostic_q1
2025-07-22 20:34:37,348 - DEBUG - [main.py:10757] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 20:34:37,349 - DEBUG - [main.py:10758] - 🔒   Diagnostic Complete: False
2025-07-22 20:34:37,349 - DEBUG - [main.py:10759] - 🔒   Assigned Level: None
2025-07-22 20:34:37,350 - INFO - [main.py:10832] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 20:34:37,350 - INFO - [main.py:10866] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 20:34:37,351 - INFO - [main.py:10874] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 20:34:37,351 - INFO - [main.py:10879] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 20:34:37,352 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:34:37,352 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:34:37,353 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:34:37,353 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:34:37,353 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:34:37,354 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:34:37,354 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:34:37,354 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:34:37,355 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:34:37,355 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:34:37,355 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:34:37,355 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:34:37,356 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:34:37,356 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:34:37,356 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:34:37,356 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:34:37,357 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:34:37,357 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:34:37,357 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:34:37,358 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:34:37,358 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:34:37,358 - INFO - [main.py:10888] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 20:34:37,358 - INFO - [intelligent_guardrails.py:130] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:34:37,358 - INFO - [intelligent_guardrails.py:183] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:34:37,359 - INFO - [main.py:10950] - [f9f0360e-a576-470e-844c-fc8c4f14174d] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 20:34:37,359 - INFO - [main.py:10978] - [f9f0360e-a576-470e-844c-fc8c4f14174d] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 20:34:37,359 - DEBUG - [main.py:11035] - 🎯 RESPONSE READY:
2025-07-22 20:34:37,360 - DEBUG - [main.py:11036] - 🎯   Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:34:37,361 - DEBUG - [main.py:11037] - 🎯   Phase: smart_diagnostic_q1 → smart_diagnostic_start
2025-07-22 20:34:37,361 - DEBUG - [main.py:11038] - 🎯   Content: That's a fantastic start, Andrea! You've already g...
2025-07-22 20:34:37,362 - DEBUG - [main.py:11039] - 🎯   Request ID: f9f0360e-a576-470e-844c-fc8c4f14174d
2025-07-22 20:34:37,362 - INFO - [main.py:11045] - [f9f0360e-a576-470e-844c-fc8c4f14174d] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 20:34:37,363 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,363 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,364 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,364 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:34:37,364 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:34:37,365 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:34:37,365 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:34:37,365 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,365 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:34:37,365 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,366 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,366 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,366 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,366 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,367 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:34:37,367 - WARNING - [main.py:888] - High response time detected: 5.67s for enhance_content_api
2025-07-22 20:35:05,039 - INFO - [main.py:7143] - Incoming request: {"request_id": "97b548f1-3af4-43e4-bfe1-e5393dce55dd", "timestamp": "2025-07-22T19:35:05.039473+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-22T19:33:39.209Z"}, {"role": "user", "content": "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", "timestamp": "2025-07-22T19:34:31.251Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You've already grasped some really important ideas about how AI can be a \"super-smart helper\" in healthcare. You're right, it's all about computers learning and processing lots of information to help doctors.\n\nLet's move on to our next question!\n\n**Question 2 of 5 (Level 3 Concept Exploration):** When you hear \"AI in healthcare,\" what's the first specific thing that comes to your mind that AI might help with?", "timestamp": "2025-07-22T19:34:37.380Z"}]}}
2025-07-22 20:35:05,040 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 20:35:05,040 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gj0qnEDDwAwV0F7rNkWh0AcRM_Vqch0hfz0_Gre-6PN9Ghpx_KAU3ISJU2J4mFlUyqTNjIYwn4CYbeHQcnCVC9TG8q0ADK-fDuwpAjd43B7b7hQvBLumyroLvpp-ck-bA7FeUD7-cawX6D5ltTIXZUOV-kLJ4yceZzYjPuKze7oeqPzjAiYvgX8J2FSG4MBaovb1BjlR-AoNq_jsjcJryDf3ndrLxmRY2_r86KIuvX3nbm29snqIUaWed6ukFgi35NpPRt1QnbaLdGL1kNzv-epB_8HoN48szPVY5NXOb2i0peCGEc2w2C_AFWqHIFviUy3v1mhg7ZLNsHKd5xFJNw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '2052', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 20:35:05,041 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 97b548f1-3af4-43e4-bfe1-e5393dce55dd
2025-07-22 20:35:05,041 - INFO - [auth_decorator.py:74] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 20:35:05,041 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 20:35:05,041 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 20:35:05,041 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 20:35:05,042 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 20:35:05,042 - INFO - [auth_decorator.py:95] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd][require_auth] Development mode detected - bypassing authentication
2025-07-22 20:35:05,042 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 20:35:05,042 - INFO - [auth_decorator.py:121] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 20:35:05,337 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:35:05,337 - INFO - [auth_decorator.py:164] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:35:05,337 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 20:35:05,338 - INFO - [main.py:7327] -
================================================================================
2025-07-22 20:35:05,339 - WARNING - [main.py:7328] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 20:35:05,339 - WARNING - [main.py:7329] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 20:35:05,339 - DEBUG - [main.py:7337] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Console output error: I/O operation on closed file.
2025-07-22 20:35:05,340 - INFO - [main.py:8179] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!","country":"Nigeria","curriculum":"National Curriculum","grade":"primary-5","subject":"Artif...
2025-07-22 20:35:05,341 - INFO - [main.py:8181] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-22T19:33:39.209Z'}, {'role': 'user', 'content': "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", 'timestamp': '2025-07-22T19:34:31.251Z'}, {'role': 'assistant', 'content': 'That\'s a fantastic start, Andrea! You\'ve already grasped some really important ideas about how AI can be a "super-smart helper" in healthcare. You\'re right, it\'s all about computers learning and processing lots of information to help doctors.\n\nLet\'s move on to our next question!\n\n**Question 2 of 5 (Level 3 Concept Exploration):** When you hear "AI in healthcare," what\'s the first specific thing that comes to your mind that AI might help with?', 'timestamp': '2025-07-22T19:34:37.380Z'}]}
2025-07-22 20:35:05,341 - INFO - [main.py:8183] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍  - Session ID from payload: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:05,342 - INFO - [main.py:8184] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 20:35:05,342 - INFO - [main.py:8185] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 20:35:05,343 - DEBUG - [main.py:8223] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:35:05,343 - INFO - [main.py:8224] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:35:05,344 - INFO - [main.py:8264] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Level not provided, determined from grade 'primary-5': 5
2025-07-22 20:35:05,747 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 20:35:05,747 - INFO - [main.py:8281] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 20:35:05,747 - INFO - [main.py:8282] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 20:35:05,748 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 20:35:06,157 - INFO - [main.py:8312] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 20:35:06,157 - INFO - [main.py:8375] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 20:35:06,157 - INFO - [main.py:8500] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ Successfully retrieved lesson from primary path
2025-07-22 20:35:06,157 - INFO - [main.py:8511] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 20:35:06,158 - INFO - [main.py:8550] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 20:35:06,158 - INFO - [main.py:4456] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 20:35:06,522 - INFO - [main.py:4522] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 20:35:06,522 - INFO - [main.py:4522] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 20:35:06,522 - INFO - [main.py:4522] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 20:35:06,522 - INFO - [main.py:4522] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 20:35:06,522 - INFO - [main.py:4522] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 20:35:06,523 - INFO - [main.py:4591] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 20:35:06,523 - DEBUG - [main.py:4605] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 20:35:06,523 - DEBUG - [main.py:4608] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 20:35:06,523 - DEBUG - [main.py:4609] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 20:35:06,524 - DEBUG - [main.py:4610] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 20:35:06,524 - INFO - [main.py:4614] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Calling Gemini API for module inference...
2025-07-22 20:35:06,957 - INFO - [main.py:4624] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Gemini API call completed in 0.43s. Raw response: 'ai_tools_and_applications'
2025-07-22 20:35:06,957 - DEBUG - [main.py:4646] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 20:35:06,957 - INFO - [main.py:4651] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 20:35:06,957 - INFO - [main.py:8584] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 20:35:06,958 - INFO - [main.py:8621] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 20:35:07,242 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 20:35:07,737 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 20:35:07,737 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:07,738 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 20:35:07,738 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q2
2025-07-22 20:35:07,738 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 20:35:07,738 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 20:35:07,739 - WARNING - [main.py:8685] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 SESSION STATE DEBUG:
2025-07-22 20:35:07,739 - WARNING - [main.py:8686] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   - Session exists: True
2025-07-22 20:35:07,739 - WARNING - [main.py:8687] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   - Current phase: smart_diagnostic_q2
2025-07-22 20:35:07,740 - WARNING - [main.py:8688] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'new_phase', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'teaching_interactions', 'last_modified', 'latest_assessed_level_for_module', 'quiz_answers', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 20:35:07,740 - DEBUG - [main.py:8706] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 20:35:07,741 - DEBUG - [main.py:8707] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Retrieved Phase: 'smart_diagnostic_q2'
2025-07-22 20:35:07,741 - DEBUG - [main.py:8708] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Diagnostic Completed: False
2025-07-22 20:35:07,742 - DEBUG - [main.py:8709] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Assigned Level: None
2025-07-22 20:35:07,742 - WARNING - [main.py:8710] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔒 STATE PROTECTION: phase='smart_diagnostic_q2', diagnostic_done=False, level=None
2025-07-22 20:35:07,743 - INFO - [main.py:8742] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 20:35:07,743 - INFO - [main.py:8743] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] State protection not triggered
2025-07-22 20:35:07,744 - INFO - [main.py:8791] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 20:35:07,744 - INFO - [main.py:8792] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   assigned_level_for_teaching (session): None
2025-07-22 20:35:07,745 - INFO - [main.py:8793] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   latest_assessed_level (profile): None
2025-07-22 20:35:07,745 - INFO - [main.py:8794] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   teaching_level_for_returning_student: None
2025-07-22 20:35:07,746 - INFO - [main.py:8795] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   has_completed_diagnostic_before: False
2025-07-22 20:35:07,747 - INFO - [main.py:8796] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   is_first_encounter_for_module: True
2025-07-22 20:35:07,747 - WARNING - [main.py:8801] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 20:35:07,747 - INFO - [main.py:8809] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 PHASE INVESTIGATION:
2025-07-22 20:35:07,748 - INFO - [main.py:8810] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Retrieved from Firestore: 'smart_diagnostic_q2'
2025-07-22 20:35:07,748 - INFO - [main.py:8811] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 20:35:07,748 - INFO - [main.py:8812] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Is first encounter: True
2025-07-22 20:35:07,749 - INFO - [main.py:8813] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Diagnostic completed: False
2025-07-22 20:35:07,749 - INFO - [main.py:8819] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ Using stored phase from Firestore: 'smart_diagnostic_q2'
2025-07-22 20:35:07,749 - INFO - [main.py:8833] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 20:35:07,749 - INFO - [main.py:8835] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Final phase for AI logic: smart_diagnostic_q2
2025-07-22 20:35:07,750 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 20:35:07,750 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 20:35:07,750 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 20:35:07,750 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 20:35:07,751 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 20:35:07,751 - INFO - [main.py:8855] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 20:35:07,751 - INFO - [main.py:6144] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Diagnostic context validation passed
2025-07-22 20:35:07,752 - WARNING - [main.py:9007] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q2' for first encounter
2025-07-22 20:35:07,752 - INFO - [main.py:9030] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q2
2025-07-22 20:35:07,752 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q2'
2025-07-22 20:35:07,752 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q2'
2025-07-22 20:35:07,753 - INFO - [main.py:9042] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Robust context prepared successfully. Phase: smart_diagnostic_q2
2025-07-22 20:35:07,753 - DEBUG - [main.py:9043] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 20:35:07,753 - INFO - [main.py:9285] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q2
2025-07-22 20:35:07,754 - INFO - [main.py:9286] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎯 UNIFIED STATE MACHINE: Processing user query: The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans ...
2025-07-22 20:35:07,754 - INFO - [main.py:9329] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎯 ROUTING: Smart Diagnostic phase 'smart_diagnostic_q2' - proceeding to main AI processing
2025-07-22 20:35:07,754 - INFO - [main.py:9349] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🤖 MAIN AI PROCESSING: No specific handler, calling main AI for phase 'smart_diagnostic_q2'
2025-07-22 20:35:07,755 - INFO - [main.py:11193] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:07,755 - DEBUG - [main.py:11202] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Console output error: I/O operation on closed file.
2025-07-22 20:35:07,755 - INFO - [main.py:11235] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 📤 Sending message to existing session: The first thing that pops into my head is AI helpi...
2025-07-22 20:35:07,755 - INFO - [main.py:5965] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 💰 Sending message to existing session (NO API CALL)
2025-07-22 20:35:08,720 - INFO - [main.py:5970] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ Response received from session (NO API CALL COST)
2025-07-22 20:35:08,721 - INFO - [main.py:11242] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 📥 Received response from session: That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medi...
2025-07-22 20:35:08,721 - INFO - [main.py:11333] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q2
2025-07-22 20:35:08,721 - INFO - [main.py:11391] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 📝 Diagnostic in progress, checking for question progression
2025-07-22 20:35:08,722 - INFO - [main.py:11422] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-22 20:35:08,722 - INFO - [main.py:9360] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ MAIN AI PROCESSING: Completed successfully
2025-07-22 20:35:08,722 - INFO - [main.py:9371] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 20:35:08,723 - INFO - [main.py:5710] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔄 PHASE TRANSITION: smart_diagnostic_q2 → smart_diagnostic_q2
2025-07-22 20:35:08,723 - INFO - [phase_transition_integrity.py:153] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q2 → smart_diagnostic_q2
2025-07-22 20:35:08,724 - INFO - [phase_transition_integrity.py:220] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ TRANSITION VALIDATED: smart_diagnostic_q2 → smart_diagnostic_q2
2025-07-22 20:35:08,724 - INFO - [main.py:5718] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ VALID TRANSITION: smart_diagnostic_q2 → smart_diagnostic_q2
2025-07-22 20:35:08,725 - INFO - [main.py:9387] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q2 → smart_diagnostic_q2
2025-07-22 20:35:08,725 - INFO - [main.py:9388] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 20:35:09,547 - INFO - [main.py:9402] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ UNIFIED STATE MACHINE: Session state updated with phase smart_diagnostic_q2
2025-07-22 20:35:09,547 - INFO - [main.py:9412] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 20:35:09,548 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:35:09,548 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:35:09,548 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:35:09,548 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:35:09,549 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:35:09,549 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:35:09,549 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:35:09,550 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:35:09,550 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:35:09,551 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:35:09,551 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:35:09,551 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:35:09,552 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:35:09,552 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:35:09,553 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:35:09,553 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:35:09,553 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:35:09,554 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:35:09,554 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:35:09,554 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:35:09,555 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:35:09,555 - INFO - [intelligent_guardrails.py:130] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:35:09,556 - INFO - [intelligent_guardrails.py:183] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:35:09,556 - INFO - [intelligent_guardrails.py:675] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 20:35:09,556 - INFO - [main.py:9544] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 20:35:09,557 - WARNING - [main.py:9593] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🤖 AI RESPONSE RECEIVED:
2025-07-22 20:35:09,557 - WARNING - [main.py:9594] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🤖   - Content length: 547 chars
2025-07-22 20:35:09,557 - WARNING - [main.py:9595] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q3'}
2025-07-22 20:35:09,558 - WARNING - [main.py:9596] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🤖   - Raw state block: None...
2025-07-22 20:35:09,558 - DEBUG - [main.py:10129] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 20:35:09,559 - DEBUG - [main.py:10130] - 🤖   Content: That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medi...
2025-07-22 20:35:09,559 - DEBUG - [main.py:10131] - 🤖   State: {'new_phase': 'smart_diagnostic_q3'}
2025-07-22 20:35:09,559 - INFO - [main.py:10157] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 20:35:09,560 - INFO - [main.py:10158] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q3, Session=smart_diagnostic_q2, Final=smart_diagnostic_q3
2025-07-22 20:35:09,833 - WARNING - [main.py:10260] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q2', new_phase='smart_diagnostic_q3'
2025-07-22 20:35:09,833 - INFO - [main.py:6365] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI state update validation passed: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-22 20:35:09,833 - WARNING - [main.py:10269] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 20:35:09,834 - WARNING - [main.py:10288] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔄 PHASE TRANSITION: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-22 20:35:09,834 - INFO - [main.py:10319] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 20:35:09,835 - INFO - [main.py:10324] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 20:35:09,835 - INFO - [phase_transition_integrity.py:315] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q2 → teaching_start
2025-07-22 20:35:09,835 - INFO - [phase_transition_integrity.py:278] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 📸 DATA SNAPSHOT CREATED: Session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase smart_diagnostic_q2
2025-07-22 20:35:09,835 - WARNING - [phase_transition_integrity.py:327] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 20:35:09,836 - WARNING - [phase_transition_integrity.py:328] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 20:35:09,836 - WARNING - [phase_transition_integrity.py:329] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 20:35:09,837 - WARNING - [phase_transition_integrity.py:330] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 20:35:09,837 - WARNING - [phase_transition_integrity.py:332] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   - assigned_level_for_teaching value: None
2025-07-22 20:35:09,838 - WARNING - [phase_transition_integrity.py:333] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   - state_updates keys: ['new_phase']
2025-07-22 20:35:09,838 - WARNING - [phase_transition_integrity.py:334] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 20:35:09,839 - INFO - [phase_transition_integrity.py:153] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q2 → teaching_start
2025-07-22 20:35:09,839 - WARNING - [phase_transition_integrity.py:169] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ❌ INVALID TRANSITION: smart_diagnostic_q2 → teaching_start
2025-07-22 20:35:09,840 - ERROR - [phase_transition_integrity.py:374] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔧 TRANSITION RECOVERY: smart_diagnostic_q2 → smart_diagnostic_start
2025-07-22 20:35:09,840 - DEBUG - [phase_transition_integrity.py:832] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 20:35:09,840 - DEBUG - [phase_transition_integrity.py:865] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 📝 TRANSITION RECORDED: smart_diagnostic_q2 → smart_diagnostic_start (invalid)
2025-07-22 20:35:09,841 - INFO - [phase_transition_integrity.py:391] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 20:35:09,841 - WARNING - [main.py:10366] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q3
2025-07-22 20:35:09,842 - WARNING - [main.py:10393] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔧 TRANSITION RECOVERED: Invalid transition: smart_diagnostic_q2 → teaching_start not allowed
2025-07-22 20:35:09,842 - WARNING - [main.py:10402] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 20:35:09,842 - WARNING - [main.py:10403] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   1. Input phase: 'smart_diagnostic_q2'
2025-07-22 20:35:09,843 - WARNING - [main.py:10404] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 20:35:09,843 - WARNING - [main.py:10405] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 20:35:09,844 - WARNING - [main.py:10406] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 20:35:09,845 - WARNING - [main.py:10407] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍   5. Final phase to save: 'smart_diagnostic_q3'
2025-07-22 20:35:09,846 - WARNING - [main.py:10410] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 💾 FINAL STATE APPLICATION:
2025-07-22 20:35:09,847 - WARNING - [main.py:10411] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 💾   - Current phase input: 'smart_diagnostic_q2'
2025-07-22 20:35:09,848 - WARNING - [main.py:10412] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 💾   - Validated state updates: 26 fields
2025-07-22 20:35:09,848 - WARNING - [main.py:10413] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 💾   - Final phase to save: 'smart_diagnostic_q3'
2025-07-22 20:35:09,849 - WARNING - [main.py:10414] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 💾   - Phase change: True
2025-07-22 20:35:09,849 - WARNING - [main.py:10415] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 💾   - Integrity applied: True
2025-07-22 20:35:09,849 - INFO - [main.py:6397] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 20:35:09,850 - INFO - [main.py:6398] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Phase transition: smart_diagnostic_q2 -> smart_diagnostic_start
2025-07-22 20:35:09,850 - INFO - [main.py:6399] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Current level: 2
2025-07-22 20:35:09,850 - INFO - [main.py:6400] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Question index: 0
2025-07-22 20:35:09,850 - INFO - [main.py:6401] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   First encounter: True
2025-07-22 20:35:09,851 - INFO - [main.py:6406] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Answers collected: 0
2025-07-22 20:35:09,851 - INFO - [main.py:6407] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Levels failed: 0
2025-07-22 20:35:09,851 - INFO - [main.py:6365] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI state update validation passed: smart_diagnostic_q2 → smart_diagnostic_start
2025-07-22 20:35:09,851 - INFO - [main.py:6411] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   State update valid: True
2025-07-22 20:35:09,852 - INFO - [main.py:6418] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd]   Diagnostic complete: False
2025-07-22 20:35:09,852 - WARNING - [main.py:10433] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 20:35:09,852 - INFO - [main.py:10442] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-22 20:35:09,853 - INFO - [main.py:10443] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 20:35:10,374 - WARNING - [main.py:10493] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 20:35:10,374 - WARNING - [main.py:10494] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅   - Phase: smart_diagnostic_q3
2025-07-22 20:35:10,375 - WARNING - [main.py:10495] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅   - Probing Level: 2
2025-07-22 20:35:10,375 - WARNING - [main.py:10496] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅   - Question Index: 0
2025-07-22 20:35:10,376 - WARNING - [main.py:10497] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅   - Diagnostic Complete: False
2025-07-22 20:35:10,376 - WARNING - [main.py:10504] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅   - Quiz Questions Saved: 0
2025-07-22 20:35:10,376 - WARNING - [main.py:10505] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅   - Quiz Answers Saved: 0
2025-07-22 20:35:10,377 - WARNING - [main.py:10506] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅   - Quiz Started: False
2025-07-22 20:35:10,377 - DEBUG - [main.py:10555] - 🔥 STATE SAVED - Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: smart_diagnostic_q3
2025-07-22 20:35:10,378 - DEBUG - [main.py:10556] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 20:35:11,206 - DEBUG - [main.py:10614] - ✅ SESSION UPDATED - ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: smart_diagnostic_q3
2025-07-22 20:35:11,206 - DEBUG - [main.py:10615] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q2 → smart_diagnostic_q3
2025-07-22 20:35:11,207 - INFO - [main.py:10621] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ Updated existing session document: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:11,207 - INFO - [main.py:18535] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 20:35:11,207 - DEBUG - [main.py:4943] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 20:35:11,208 - DEBUG - [main.py:10708] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] No final assessment data found in AI response
2025-07-22 20:35:11,208 - DEBUG - [main.py:10731] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 20:35:11,208 - DEBUG - [main.py:10755] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 20:35:11,209 - DEBUG - [main.py:10756] - 🔒   Current Phase: smart_diagnostic_q2
2025-07-22 20:35:11,209 - DEBUG - [main.py:10757] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 20:35:11,210 - DEBUG - [main.py:10758] - 🔒   Diagnostic Complete: False
2025-07-22 20:35:11,211 - DEBUG - [main.py:10759] - 🔒   Assigned Level: None
2025-07-22 20:35:11,212 - INFO - [main.py:10832] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 20:35:11,213 - INFO - [main.py:10866] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 20:35:11,214 - INFO - [main.py:10874] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 20:35:11,215 - INFO - [main.py:10879] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 20:35:11,215 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:35:11,215 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:35:11,216 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:35:11,216 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:35:11,216 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:35:11,217 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:35:11,217 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:35:11,217 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:35:11,217 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:35:11,218 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:35:11,218 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:35:11,218 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:35:11,218 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:35:11,219 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:35:11,219 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:35:11,219 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:35:11,219 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:35:11,220 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:35:11,220 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:35:11,220 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:35:11,220 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:35:11,221 - INFO - [main.py:10888] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 20:35:11,221 - INFO - [intelligent_guardrails.py:130] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:35:11,221 - INFO - [intelligent_guardrails.py:183] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:35:11,222 - INFO - [main.py:10950] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 20:35:11,222 - INFO - [main.py:10978] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 20:35:11,222 - DEBUG - [main.py:11035] - 🎯 RESPONSE READY:
2025-07-22 20:35:11,223 - DEBUG - [main.py:11036] - 🎯   Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:11,223 - DEBUG - [main.py:11037] - 🎯   Phase: smart_diagnostic_q2 → smart_diagnostic_start
2025-07-22 20:35:11,223 - DEBUG - [main.py:11038] - 🎯   Content: That's a brilliant example, Andrea! You're absolut...
2025-07-22 20:35:11,223 - DEBUG - [main.py:11039] - 🎯   Request ID: 97b548f1-3af4-43e4-bfe1-e5393dce55dd
2025-07-22 20:35:11,224 - INFO - [main.py:11045] - [97b548f1-3af4-43e4-bfe1-e5393dce55dd] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 20:35:11,224 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,224 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,224 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,224 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:11,224 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:11,225 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:11,225 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:11,225 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,225 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:11,225 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,226 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,226 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,226 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,226 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,226 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:11,229 - WARNING - [main.py:888] - High response time detected: 5.89s for enhance_content_api
2025-07-22 20:35:38,957 - INFO - [main.py:7143] - Incoming request: {"request_id": "d62910be-7833-46a7-976e-b9ff16a9b05f", "timestamp": "2025-07-22T19:35:38.956954+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-22T19:33:39.209Z"}, {"role": "user", "content": "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", "timestamp": "2025-07-22T19:34:31.251Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You've already grasped some really important ideas about how AI can be a \"super-smart helper\" in healthcare. You're right, it's all about computers learning and processing lots of information to help doctors.\n\nLet's move on to our next question!\n\n**Question 2 of 5 (Level 3 Concept Exploration):** When you hear \"AI in healthcare,\" what's the first specific thing that comes to your mind that AI might help with?", "timestamp": "2025-07-22T19:34:37.380Z"}, {"role": "user", "content": "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", "timestamp": "2025-07-22T19:35:04.917Z"}, {"role": "assistant", "content": "That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medical images like X-rays and scans. It's like having a super-powered magnifying glass for doctors, helping them detect even the smallest signs of a **disease**. This is a key part of **diagnosis**, which means figuring out what's wrong.\n\nYou're doing great! Let's try another question.\n\n**Question 3 of 5 (Level 4 Application Insight):** Besides looking at pictures, can you think of another way AI might help doctors when someone is feeling unwell?", "timestamp": "2025-07-22T19:35:11.242Z"}]}}
2025-07-22 20:35:38,959 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 20:35:38,959 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gj0qnEDDwAwV0F7rNkWh0AcRM_Vqch0hfz0_Gre-6PN9Ghpx_KAU3ISJU2J4mFlUyqTNjIYwn4CYbeHQcnCVC9TG8q0ADK-fDuwpAjd43B7b7hQvBLumyroLvpp-ck-bA7FeUD7-cawX6D5ltTIXZUOV-kLJ4yceZzYjPuKze7oeqPzjAiYvgX8J2FSG4MBaovb1BjlR-AoNq_jsjcJryDf3ndrLxmRY2_r86KIuvX3nbm29snqIUaWed6ukFgi35NpPRt1QnbaLdGL1kNzv-epB_8HoN48szPVY5NXOb2i0peCGEc2w2C_AFWqHIFviUy3v1mhg7ZLNsHKd5xFJNw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '3243', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 20:35:38,960 - INFO - [auth_decorator.py:70] - 🔒 Request ID: d62910be-7833-46a7-976e-b9ff16a9b05f
2025-07-22 20:35:38,960 - INFO - [auth_decorator.py:74] - [d62910be-7833-46a7-976e-b9ff16a9b05f][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 20:35:38,960 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 20:35:38,960 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 20:35:38,960 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 20:35:38,961 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 20:35:38,961 - INFO - [auth_decorator.py:95] - [d62910be-7833-46a7-976e-b9ff16a9b05f][require_auth] Development mode detected - bypassing authentication
2025-07-22 20:35:38,963 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 20:35:38,963 - INFO - [auth_decorator.py:121] - [d62910be-7833-46a7-976e-b9ff16a9b05f][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 20:35:39,264 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:35:39,265 - INFO - [auth_decorator.py:164] - [d62910be-7833-46a7-976e-b9ff16a9b05f][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:35:39,265 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 20:35:39,266 - INFO - [main.py:7327] -
================================================================================
2025-07-22 20:35:39,266 - WARNING - [main.py:7328] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 20:35:39,267 - WARNING - [main.py:7329] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 20:35:39,267 - DEBUG - [main.py:7337] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Console output error: I/O operation on closed file.
2025-07-22 20:35:39,268 - INFO - [main.py:8179] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, ra...
2025-07-22 20:35:39,269 - INFO - [main.py:8181] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-22T19:33:39.209Z'}, {'role': 'user', 'content': "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", 'timestamp': '2025-07-22T19:34:31.251Z'}, {'role': 'assistant', 'content': 'That\'s a fantastic start, Andrea! You\'ve already grasped some really important ideas about how AI can be a "super-smart helper" in healthcare. You\'re right, it\'s all about computers learning and processing lots of information to help doctors.\n\nLet\'s move on to our next question!\n\n**Question 2 of 5 (Level 3 Concept Exploration):** When you hear "AI in healthcare," what\'s the first specific thing that comes to your mind that AI might help with?', 'timestamp': '2025-07-22T19:34:37.380Z'}, {'role': 'user', 'content': "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", 'timestamp': '2025-07-22T19:35:04.917Z'}, {'role': 'assistant', 'content': "That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medical images like X-rays and scans. It's like having a super-powered magnifying glass for doctors, helping them detect even the smallest signs of a **disease**. This is a key part of **diagnosis**, which means figuring out what's wrong.\n\nYou're doing great! Let's try another question.\n\n**Question 3 of 5 (Level 4 Application Insight):** Besides looking at pictures, can you think of another way AI might help doctors when someone is feeling unwell?", 'timestamp': '2025-07-22T19:35:11.242Z'}]}
2025-07-22 20:35:39,270 - INFO - [main.py:8183] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍  - Session ID from payload: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:39,270 - INFO - [main.py:8184] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 20:35:39,271 - INFO - [main.py:8185] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 20:35:39,271 - DEBUG - [main.py:8223] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:35:39,272 - INFO - [main.py:8224] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:35:39,272 - INFO - [main.py:8264] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Level not provided, determined from grade 'primary-5': 5
2025-07-22 20:35:39,619 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 20:35:39,619 - INFO - [main.py:8281] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 20:35:39,619 - INFO - [main.py:8282] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 20:35:39,620 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 20:35:40,070 - INFO - [main.py:8312] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 20:35:40,071 - INFO - [main.py:8375] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 20:35:40,071 - INFO - [main.py:8500] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ Successfully retrieved lesson from primary path
2025-07-22 20:35:40,072 - INFO - [main.py:8511] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 20:35:40,072 - INFO - [main.py:8550] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 20:35:40,072 - INFO - [main.py:4456] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 20:35:40,436 - INFO - [main.py:4522] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 20:35:40,437 - INFO - [main.py:4522] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 20:35:40,437 - INFO - [main.py:4522] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 20:35:40,438 - INFO - [main.py:4522] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 20:35:40,438 - INFO - [main.py:4522] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 20:35:40,439 - INFO - [main.py:4591] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 20:35:40,439 - DEBUG - [main.py:4605] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 20:35:40,440 - DEBUG - [main.py:4608] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 20:35:40,440 - DEBUG - [main.py:4609] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 20:35:40,441 - DEBUG - [main.py:4610] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 20:35:40,441 - INFO - [main.py:4614] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Calling Gemini API for module inference...
2025-07-22 20:35:40,963 - INFO - [main.py:4624] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Gemini API call completed in 0.52s. Raw response: 'ai_tools_and_applications'
2025-07-22 20:35:40,964 - DEBUG - [main.py:4646] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 20:35:40,964 - INFO - [main.py:4651] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 20:35:40,964 - INFO - [main.py:8584] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 20:35:40,965 - INFO - [main.py:8621] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 20:35:41,246 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 20:35:41,764 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 20:35:41,765 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:41,765 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 20:35:41,766 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q3
2025-07-22 20:35:41,766 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 20:35:41,766 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 20:35:41,767 - WARNING - [main.py:8685] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 SESSION STATE DEBUG:
2025-07-22 20:35:41,767 - WARNING - [main.py:8686] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   - Session exists: True
2025-07-22 20:35:41,768 - WARNING - [main.py:8687] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   - Current phase: smart_diagnostic_q3
2025-07-22 20:35:41,768 - WARNING - [main.py:8688] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'new_phase', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'teaching_interactions', 'last_modified', 'latest_assessed_level_for_module', 'quiz_answers', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 20:35:41,769 - DEBUG - [main.py:8706] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 20:35:41,769 - DEBUG - [main.py:8707] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Retrieved Phase: 'smart_diagnostic_q3'
2025-07-22 20:35:41,770 - DEBUG - [main.py:8708] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Diagnostic Completed: False
2025-07-22 20:35:41,771 - DEBUG - [main.py:8709] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Assigned Level: None
2025-07-22 20:35:41,771 - WARNING - [main.py:8710] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔒 STATE PROTECTION: phase='smart_diagnostic_q3', diagnostic_done=False, level=None
2025-07-22 20:35:41,772 - INFO - [main.py:8742] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 20:35:41,772 - INFO - [main.py:8743] - [d62910be-7833-46a7-976e-b9ff16a9b05f] State protection not triggered
2025-07-22 20:35:41,773 - INFO - [main.py:8791] - [d62910be-7833-46a7-976e-b9ff16a9b05f]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 20:35:41,773 - INFO - [main.py:8792] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   assigned_level_for_teaching (session): None
2025-07-22 20:35:41,774 - INFO - [main.py:8793] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   latest_assessed_level (profile): None
2025-07-22 20:35:41,774 - INFO - [main.py:8794] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   teaching_level_for_returning_student: None
2025-07-22 20:35:41,774 - INFO - [main.py:8795] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   has_completed_diagnostic_before: False
2025-07-22 20:35:41,774 - INFO - [main.py:8796] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   is_first_encounter_for_module: True
2025-07-22 20:35:41,775 - WARNING - [main.py:8801] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 20:35:41,775 - INFO - [main.py:8809] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 PHASE INVESTIGATION:
2025-07-22 20:35:41,775 - INFO - [main.py:8810] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Retrieved from Firestore: 'smart_diagnostic_q3'
2025-07-22 20:35:41,775 - INFO - [main.py:8811] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 20:35:41,776 - INFO - [main.py:8812] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Is first encounter: True
2025-07-22 20:35:41,776 - INFO - [main.py:8813] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Diagnostic completed: False
2025-07-22 20:35:41,776 - INFO - [main.py:8819] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ Using stored phase from Firestore: 'smart_diagnostic_q3'
2025-07-22 20:35:41,777 - INFO - [main.py:8833] - [d62910be-7833-46a7-976e-b9ff16a9b05f] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 20:35:41,777 - INFO - [main.py:8835] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Final phase for AI logic: smart_diagnostic_q3
2025-07-22 20:35:41,777 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 20:35:41,778 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 20:35:41,779 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 20:35:41,780 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 20:35:41,780 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 20:35:41,781 - INFO - [main.py:8855] - [d62910be-7833-46a7-976e-b9ff16a9b05f] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 20:35:41,782 - INFO - [main.py:6144] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Diagnostic context validation passed
2025-07-22 20:35:41,782 - WARNING - [main.py:9007] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q3' for first encounter
2025-07-22 20:35:41,782 - INFO - [main.py:9030] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q3
2025-07-22 20:35:41,782 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q3'
2025-07-22 20:35:41,783 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q3'
2025-07-22 20:35:41,783 - INFO - [main.py:9042] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Robust context prepared successfully. Phase: smart_diagnostic_q3
2025-07-22 20:35:41,783 - DEBUG - [main.py:9043] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 20:35:41,784 - INFO - [main.py:9285] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q3
2025-07-22 20:35:41,784 - INFO - [main.py:9286] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎯 UNIFIED STATE MACHINE: Processing user query: I know! AI can also be like a super-organiser for all the information about someone who is sick. It ...
2025-07-22 20:35:41,784 - INFO - [main.py:9329] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎯 ROUTING: Smart Diagnostic phase 'smart_diagnostic_q3' - proceeding to main AI processing
2025-07-22 20:35:41,784 - INFO - [main.py:9349] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🤖 MAIN AI PROCESSING: No specific handler, calling main AI for phase 'smart_diagnostic_q3'
2025-07-22 20:35:41,785 - INFO - [main.py:11193] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:41,785 - DEBUG - [main.py:11202] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Console output error: I/O operation on closed file.
2025-07-22 20:35:41,785 - INFO - [main.py:11235] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 📤 Sending message to existing session: I know! AI can also be like a super-organiser for ...
2025-07-22 20:35:41,785 - INFO - [main.py:5965] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 💰 Sending message to existing session (NO API CALL)
2025-07-22 20:35:42,756 - INFO - [main.py:5970] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ Response received from session (NO API CALL COST)
2025-07-22 20:35:42,757 - INFO - [main.py:11242] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 📥 Received response from session: That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and ...
2025-07-22 20:35:42,757 - INFO - [main.py:11333] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q3
2025-07-22 20:35:42,758 - INFO - [main.py:11391] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 📝 Diagnostic in progress, checking for question progression
2025-07-22 20:35:42,758 - INFO - [main.py:11422] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-22 20:35:42,758 - INFO - [main.py:9360] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ MAIN AI PROCESSING: Completed successfully
2025-07-22 20:35:42,759 - INFO - [main.py:9371] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 20:35:42,759 - INFO - [main.py:5710] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔄 PHASE TRANSITION: smart_diagnostic_q3 → smart_diagnostic_q3
2025-07-22 20:35:42,759 - INFO - [phase_transition_integrity.py:153] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q3 → smart_diagnostic_q3
2025-07-22 20:35:42,760 - INFO - [phase_transition_integrity.py:220] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ TRANSITION VALIDATED: smart_diagnostic_q3 → smart_diagnostic_q3
2025-07-22 20:35:42,760 - INFO - [main.py:5718] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ VALID TRANSITION: smart_diagnostic_q3 → smart_diagnostic_q3
2025-07-22 20:35:42,761 - INFO - [main.py:9387] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q3 → smart_diagnostic_q3
2025-07-22 20:35:42,761 - INFO - [main.py:9388] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 20:35:43,588 - INFO - [main.py:9402] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ UNIFIED STATE MACHINE: Session state updated with phase smart_diagnostic_q3
2025-07-22 20:35:43,588 - INFO - [main.py:9412] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 20:35:43,589 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:35:43,589 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:35:43,589 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:35:43,589 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:35:43,590 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:35:43,590 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:35:43,590 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:35:43,590 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:35:43,591 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:35:43,591 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:35:43,592 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:35:43,592 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:35:43,592 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:35:43,592 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:35:43,593 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:35:43,593 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:35:43,594 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:35:43,594 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:35:43,595 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:35:43,595 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:35:43,595 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:35:43,596 - INFO - [intelligent_guardrails.py:130] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:35:43,597 - INFO - [intelligent_guardrails.py:183] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:35:43,597 - INFO - [intelligent_guardrails.py:675] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 20:35:43,598 - INFO - [main.py:9544] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 20:35:43,598 - WARNING - [main.py:9593] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🤖 AI RESPONSE RECEIVED:
2025-07-22 20:35:43,598 - WARNING - [main.py:9594] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🤖   - Content length: 641 chars
2025-07-22 20:35:43,599 - WARNING - [main.py:9595] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q4'}
2025-07-22 20:35:43,600 - WARNING - [main.py:9596] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🤖   - Raw state block: None...
2025-07-22 20:35:43,600 - DEBUG - [main.py:10129] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 20:35:43,601 - DEBUG - [main.py:10130] - 🤖   Content: That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and ...
2025-07-22 20:35:43,601 - DEBUG - [main.py:10131] - 🤖   State: {'new_phase': 'smart_diagnostic_q4'}
2025-07-22 20:35:43,601 - INFO - [main.py:10157] - [d62910be-7833-46a7-976e-b9ff16a9b05f] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 20:35:43,601 - INFO - [main.py:10158] - [d62910be-7833-46a7-976e-b9ff16a9b05f] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q4, Session=smart_diagnostic_q3, Final=smart_diagnostic_q4
2025-07-22 20:35:43,883 - WARNING - [main.py:10260] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q3', new_phase='smart_diagnostic_q4'
2025-07-22 20:35:43,884 - INFO - [main.py:6365] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI state update validation passed: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-22 20:35:43,884 - WARNING - [main.py:10269] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 20:35:43,884 - WARNING - [main.py:10288] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔄 PHASE TRANSITION: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-22 20:35:43,885 - INFO - [main.py:10319] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 20:35:43,885 - INFO - [main.py:10324] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 20:35:43,885 - INFO - [phase_transition_integrity.py:315] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q3 → teaching_start
2025-07-22 20:35:43,886 - INFO - [phase_transition_integrity.py:278] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 📸 DATA SNAPSHOT CREATED: Session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase smart_diagnostic_q3
2025-07-22 20:35:43,886 - WARNING - [phase_transition_integrity.py:327] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 20:35:43,887 - WARNING - [phase_transition_integrity.py:328] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 20:35:43,887 - WARNING - [phase_transition_integrity.py:329] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 20:35:43,888 - WARNING - [phase_transition_integrity.py:330] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 20:35:43,888 - WARNING - [phase_transition_integrity.py:332] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   - assigned_level_for_teaching value: None
2025-07-22 20:35:43,888 - WARNING - [phase_transition_integrity.py:333] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   - state_updates keys: ['new_phase']
2025-07-22 20:35:43,889 - WARNING - [phase_transition_integrity.py:334] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 20:35:43,889 - INFO - [phase_transition_integrity.py:153] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q3 → teaching_start
2025-07-22 20:35:43,890 - WARNING - [phase_transition_integrity.py:169] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ❌ INVALID TRANSITION: smart_diagnostic_q3 → teaching_start
2025-07-22 20:35:43,890 - ERROR - [phase_transition_integrity.py:374] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔧 TRANSITION RECOVERY: smart_diagnostic_q3 → smart_diagnostic_start
2025-07-22 20:35:43,891 - DEBUG - [phase_transition_integrity.py:832] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 20:35:43,891 - DEBUG - [phase_transition_integrity.py:865] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 📝 TRANSITION RECORDED: smart_diagnostic_q3 → smart_diagnostic_start (invalid)
2025-07-22 20:35:43,891 - INFO - [phase_transition_integrity.py:391] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 20:35:43,892 - WARNING - [main.py:10366] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q4
2025-07-22 20:35:43,892 - WARNING - [main.py:10393] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔧 TRANSITION RECOVERED: Invalid transition: smart_diagnostic_q3 → teaching_start not allowed
2025-07-22 20:35:43,892 - WARNING - [main.py:10402] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 20:35:43,893 - WARNING - [main.py:10403] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   1. Input phase: 'smart_diagnostic_q3'
2025-07-22 20:35:43,894 - WARNING - [main.py:10404] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 20:35:43,894 - WARNING - [main.py:10405] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 20:35:43,895 - WARNING - [main.py:10406] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 20:35:43,896 - WARNING - [main.py:10407] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍   5. Final phase to save: 'smart_diagnostic_q4'
2025-07-22 20:35:43,896 - WARNING - [main.py:10410] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 💾 FINAL STATE APPLICATION:
2025-07-22 20:35:43,897 - WARNING - [main.py:10411] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 💾   - Current phase input: 'smart_diagnostic_q3'
2025-07-22 20:35:43,897 - WARNING - [main.py:10412] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 💾   - Validated state updates: 26 fields
2025-07-22 20:35:43,898 - WARNING - [main.py:10413] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 💾   - Final phase to save: 'smart_diagnostic_q4'
2025-07-22 20:35:43,899 - WARNING - [main.py:10414] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 💾   - Phase change: True
2025-07-22 20:35:43,899 - WARNING - [main.py:10415] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 💾   - Integrity applied: True
2025-07-22 20:35:43,900 - INFO - [main.py:6397] - [d62910be-7833-46a7-976e-b9ff16a9b05f] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 20:35:43,900 - INFO - [main.py:6398] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Phase transition: smart_diagnostic_q3 -> smart_diagnostic_start
2025-07-22 20:35:43,900 - INFO - [main.py:6399] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Current level: 2
2025-07-22 20:35:43,900 - INFO - [main.py:6400] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Question index: 0
2025-07-22 20:35:43,901 - INFO - [main.py:6401] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   First encounter: True
2025-07-22 20:35:43,901 - INFO - [main.py:6406] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Answers collected: 0
2025-07-22 20:35:43,901 - INFO - [main.py:6407] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Levels failed: 0
2025-07-22 20:35:43,901 - INFO - [main.py:6365] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI state update validation passed: smart_diagnostic_q3 → smart_diagnostic_start
2025-07-22 20:35:43,902 - INFO - [main.py:6411] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   State update valid: True
2025-07-22 20:35:43,902 - INFO - [main.py:6418] - [d62910be-7833-46a7-976e-b9ff16a9b05f]   Diagnostic complete: False
2025-07-22 20:35:43,902 - WARNING - [main.py:10433] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 20:35:43,903 - INFO - [main.py:10442] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-22 20:35:43,903 - INFO - [main.py:10443] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 20:35:44,405 - WARNING - [main.py:10493] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 20:35:44,406 - WARNING - [main.py:10494] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅   - Phase: smart_diagnostic_q4
2025-07-22 20:35:44,406 - WARNING - [main.py:10495] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅   - Probing Level: 2
2025-07-22 20:35:44,406 - WARNING - [main.py:10496] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅   - Question Index: 0
2025-07-22 20:35:44,407 - WARNING - [main.py:10497] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅   - Diagnostic Complete: False
2025-07-22 20:35:44,407 - WARNING - [main.py:10504] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅   - Quiz Questions Saved: 0
2025-07-22 20:35:44,408 - WARNING - [main.py:10505] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅   - Quiz Answers Saved: 0
2025-07-22 20:35:44,408 - WARNING - [main.py:10506] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅   - Quiz Started: False
2025-07-22 20:35:44,408 - DEBUG - [main.py:10555] - 🔥 STATE SAVED - Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: smart_diagnostic_q4
2025-07-22 20:35:44,409 - DEBUG - [main.py:10556] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 20:35:45,221 - DEBUG - [main.py:10614] - ✅ SESSION UPDATED - ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: smart_diagnostic_q4
2025-07-22 20:35:45,222 - DEBUG - [main.py:10615] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q3 → smart_diagnostic_q4
2025-07-22 20:35:45,223 - INFO - [main.py:10621] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ Updated existing session document: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:45,223 - INFO - [main.py:18535] - [d62910be-7833-46a7-976e-b9ff16a9b05f] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 20:35:45,224 - DEBUG - [main.py:4943] - [d62910be-7833-46a7-976e-b9ff16a9b05f] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 20:35:45,224 - DEBUG - [main.py:10708] - [d62910be-7833-46a7-976e-b9ff16a9b05f] No final assessment data found in AI response
2025-07-22 20:35:45,224 - DEBUG - [main.py:10731] - [d62910be-7833-46a7-976e-b9ff16a9b05f] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 20:35:45,225 - DEBUG - [main.py:10755] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 20:35:45,226 - DEBUG - [main.py:10756] - 🔒   Current Phase: smart_diagnostic_q3
2025-07-22 20:35:45,226 - DEBUG - [main.py:10757] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 20:35:45,227 - DEBUG - [main.py:10758] - 🔒   Diagnostic Complete: False
2025-07-22 20:35:45,227 - DEBUG - [main.py:10759] - 🔒   Assigned Level: None
2025-07-22 20:35:45,228 - INFO - [main.py:10832] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 20:35:45,229 - INFO - [main.py:10866] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 20:35:45,229 - INFO - [main.py:10874] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 20:35:45,230 - INFO - [main.py:10879] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 20:35:45,231 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:35:45,231 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:35:45,232 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:35:45,232 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:35:45,233 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:35:45,234 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:35:45,234 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:35:45,234 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:35:45,235 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:35:45,235 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:35:45,235 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:35:45,235 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:35:45,236 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:35:45,236 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:35:45,236 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:35:45,236 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:35:45,236 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:35:45,237 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:35:45,237 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:35:45,237 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:35:45,237 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:35:45,238 - INFO - [main.py:10888] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 20:35:45,238 - INFO - [intelligent_guardrails.py:130] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:35:45,239 - INFO - [intelligent_guardrails.py:183] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:35:45,239 - INFO - [main.py:10950] - [d62910be-7833-46a7-976e-b9ff16a9b05f] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 20:35:45,240 - INFO - [main.py:10978] - [d62910be-7833-46a7-976e-b9ff16a9b05f] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 20:35:45,240 - DEBUG - [main.py:11035] - 🎯 RESPONSE READY:
2025-07-22 20:35:45,240 - DEBUG - [main.py:11036] - 🎯   Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:35:45,241 - DEBUG - [main.py:11037] - 🎯   Phase: smart_diagnostic_q3 → smart_diagnostic_start
2025-07-22 20:35:45,241 - DEBUG - [main.py:11038] - 🎯   Content: That's an excellent point, Andrea! You've hit on a...
2025-07-22 20:35:45,241 - DEBUG - [main.py:11039] - 🎯   Request ID: d62910be-7833-46a7-976e-b9ff16a9b05f
2025-07-22 20:35:45,241 - INFO - [main.py:11045] - [d62910be-7833-46a7-976e-b9ff16a9b05f] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 20:35:45,242 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,242 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,242 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,243 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:45,243 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:45,243 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:45,243 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:45,244 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,244 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:35:45,244 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,245 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,245 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,246 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,246 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,247 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:35:45,249 - WARNING - [main.py:888] - High response time detected: 5.98s for enhance_content_api
2025-07-22 20:36:11,297 - INFO - [main.py:7143] - Incoming request: {"request_id": "4bab24be-d5e0-4078-88eb-3d50da75bd53", "timestamp": "2025-07-22T19:36:11.296693+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.  You're building a great understanding! Here's your next question:  **Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5", "chat_history": [{"role": "assistant", "content": "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", "timestamp": "2025-07-22T19:33:39.209Z"}, {"role": "user", "content": "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", "timestamp": "2025-07-22T19:34:31.251Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You've already grasped some really important ideas about how AI can be a \"super-smart helper\" in healthcare. You're right, it's all about computers learning and processing lots of information to help doctors.\n\nLet's move on to our next question!\n\n**Question 2 of 5 (Level 3 Concept Exploration):** When you hear \"AI in healthcare,\" what's the first specific thing that comes to your mind that AI might help with?", "timestamp": "2025-07-22T19:34:37.380Z"}, {"role": "user", "content": "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", "timestamp": "2025-07-22T19:35:04.917Z"}, {"role": "assistant", "content": "That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medical images like X-rays and scans. It's like having a super-powered magnifying glass for doctors, helping them detect even the smallest signs of a **disease**. This is a key part of **diagnosis**, which means figuring out what's wrong.\n\nYou're doing great! Let's try another question.\n\n**Question 3 of 5 (Level 4 Application Insight):** Besides looking at pictures, can you think of another way AI might help doctors when someone is feeling unwell?", "timestamp": "2025-07-22T19:35:11.242Z"}, {"role": "user", "content": "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", "timestamp": "2025-07-22T19:35:38.820Z"}, {"role": "assistant", "content": "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.\n\nYou're building a great understanding! Here's your next question:\n\n**Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", "timestamp": "2025-07-22T19:35:45.259Z"}]}}
2025-07-22 20:36:11,300 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 20:36:11,300 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gj0qnEDDwAwV0F7rNkWh0AcRM_Vqch0hfz0_Gre-6PN9Ghpx_KAU3ISJU2J4mFlUyqTNjIYwn4CYbeHQcnCVC9TG8q0ADK-fDuwpAjd43B7b7hQvBLumyroLvpp-ck-bA7FeUD7-cawX6D5ltTIXZUOV-kLJ4yceZzYjPuKze7oeqPzjAiYvgX8J2FSG4MBaovb1BjlR-AoNq_jsjcJryDf3ndrLxmRY2_r86KIuvX3nbm29snqIUaWed6ukFgi35NpPRt1QnbaLdGL1kNzv-epB_8HoN48szPVY5NXOb2i0peCGEc2w2C_AFWqHIFviUy3v1mhg7ZLNsHKd5xFJNw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '4670', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 20:36:11,300 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 4bab24be-d5e0-4078-88eb-3d50da75bd53
2025-07-22 20:36:11,300 - INFO - [auth_decorator.py:74] - [4bab24be-d5e0-4078-88eb-3d50da75bd53][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 20:36:11,301 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 20:36:11,301 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 20:36:11,301 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 20:36:11,301 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 20:36:11,301 - INFO - [auth_decorator.py:95] - [4bab24be-d5e0-4078-88eb-3d50da75bd53][require_auth] Development mode detected - bypassing authentication
2025-07-22 20:36:11,301 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 20:36:11,302 - INFO - [auth_decorator.py:121] - [4bab24be-d5e0-4078-88eb-3d50da75bd53][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 20:36:11,587 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:36:11,587 - INFO - [auth_decorator.py:164] - [4bab24be-d5e0-4078-88eb-3d50da75bd53][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:36:11,588 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 20:36:11,589 - INFO - [main.py:7327] -
================================================================================
2025-07-22 20:36:11,589 - WARNING - [main.py:7328] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 20:36:11,590 - WARNING - [main.py:7329] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 20:36:11,590 - DEBUG - [main.py:7337] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Console output error: I/O operation on closed file.
2025-07-22 20:36:11,592 - INFO - [main.py:8179] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.  You're building a great unde...
2025-07-22 20:36:11,593 - INFO - [main.py:8181] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.  You're building a great understanding! Here's your next question:  **Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', 'chat_history': [{'role': 'assistant', 'content': "Hello Andrea! Welcome to your lesson on AI in Healthcare. To get started, I'll ask a few questions to understand your current knowledge. This will help me tailor the lesson to your needs.\n\n**Question 1 of 5 (Level 2 Foundation Check):** What can you tell me about the basics of this topic?", 'timestamp': '2025-07-22T19:33:39.209Z'}, {'role': 'user', 'content': "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", 'timestamp': '2025-07-22T19:34:31.251Z'}, {'role': 'assistant', 'content': 'That\'s a fantastic start, Andrea! You\'ve already grasped some really important ideas about how AI can be a "super-smart helper" in healthcare. You\'re right, it\'s all about computers learning and processing lots of information to help doctors.\n\nLet\'s move on to our next question!\n\n**Question 2 of 5 (Level 3 Concept Exploration):** When you hear "AI in healthcare," what\'s the first specific thing that comes to your mind that AI might help with?', 'timestamp': '2025-07-22T19:34:37.380Z'}, {'role': 'user', 'content': "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", 'timestamp': '2025-07-22T19:35:04.917Z'}, {'role': 'assistant', 'content': "That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medical images like X-rays and scans. It's like having a super-powered magnifying glass for doctors, helping them detect even the smallest signs of a **disease**. This is a key part of **diagnosis**, which means figuring out what's wrong.\n\nYou're doing great! Let's try another question.\n\n**Question 3 of 5 (Level 4 Application Insight):** Besides looking at pictures, can you think of another way AI might help doctors when someone is feeling unwell?", 'timestamp': '2025-07-22T19:35:11.242Z'}, {'role': 'user', 'content': "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", 'timestamp': '2025-07-22T19:35:38.820Z'}, {'role': 'assistant', 'content': "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.\n\nYou're building a great understanding! Here's your next question:\n\n**Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", 'timestamp': '2025-07-22T19:35:45.259Z'}]}
2025-07-22 20:36:11,594 - INFO - [main.py:8183] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍  - Session ID from payload: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:11,594 - INFO - [main.py:8184] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 20:36:11,595 - INFO - [main.py:8185] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 20:36:11,596 - DEBUG - [main.py:8223] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:36:11,597 - INFO - [main.py:8224] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:36:11,598 - INFO - [main.py:8264] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Level not provided, determined from grade 'primary-5': 5
2025-07-22 20:36:11,881 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 20:36:11,882 - INFO - [main.py:8281] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 20:36:11,882 - INFO - [main.py:8282] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 20:36:11,882 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 20:36:12,381 - INFO - [main.py:8312] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 20:36:12,381 - INFO - [main.py:8375] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 20:36:12,382 - INFO - [main.py:8500] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ Successfully retrieved lesson from primary path
2025-07-22 20:36:12,382 - INFO - [main.py:8511] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 20:36:12,382 - INFO - [main.py:8550] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 20:36:12,382 - INFO - [main.py:4456] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 20:36:12,678 - INFO - [main.py:4522] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 20:36:12,680 - INFO - [main.py:4522] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 20:36:12,680 - INFO - [main.py:4522] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 20:36:12,680 - INFO - [main.py:4522] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 20:36:12,681 - INFO - [main.py:4522] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 20:36:12,681 - INFO - [main.py:4591] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 20:36:12,681 - DEBUG - [main.py:4605] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 20:36:12,682 - DEBUG - [main.py:4608] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 20:36:12,682 - DEBUG - [main.py:4609] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 20:36:12,683 - DEBUG - [main.py:4610] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 20:36:12,683 - INFO - [main.py:4614] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Calling Gemini API for module inference...
2025-07-22 20:36:13,240 - INFO - [main.py:4624] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Gemini API call completed in 0.56s. Raw response: 'ai_tools_and_applications'
2025-07-22 20:36:13,240 - DEBUG - [main.py:4646] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 20:36:13,240 - INFO - [main.py:4651] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 20:36:13,240 - INFO - [main.py:8584] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 20:36:13,241 - INFO - [main.py:8621] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 20:36:13,519 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 20:36:14,024 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 20:36:14,024 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:14,025 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 20:36:14,025 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q4
2025-07-22 20:36:14,025 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 20:36:14,025 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 20:36:14,025 - WARNING - [main.py:8685] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 SESSION STATE DEBUG:
2025-07-22 20:36:14,026 - WARNING - [main.py:8686] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   - Session exists: True
2025-07-22 20:36:14,026 - WARNING - [main.py:8687] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   - Current phase: smart_diagnostic_q4
2025-07-22 20:36:14,026 - WARNING - [main.py:8688] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'new_phase', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'teaching_interactions', 'last_modified', 'latest_assessed_level_for_module', 'quiz_answers', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 20:36:14,027 - DEBUG - [main.py:8706] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 20:36:14,027 - DEBUG - [main.py:8707] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Retrieved Phase: 'smart_diagnostic_q4'
2025-07-22 20:36:14,028 - DEBUG - [main.py:8708] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Diagnostic Completed: False
2025-07-22 20:36:14,029 - DEBUG - [main.py:8709] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Assigned Level: None
2025-07-22 20:36:14,030 - WARNING - [main.py:8710] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔒 STATE PROTECTION: phase='smart_diagnostic_q4', diagnostic_done=False, level=None
2025-07-22 20:36:14,030 - INFO - [main.py:8742] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 20:36:14,031 - INFO - [main.py:8743] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] State protection not triggered
2025-07-22 20:36:14,032 - INFO - [main.py:8791] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 20:36:14,033 - INFO - [main.py:8792] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   assigned_level_for_teaching (session): None
2025-07-22 20:36:14,034 - INFO - [main.py:8793] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   latest_assessed_level (profile): None
2025-07-22 20:36:14,034 - INFO - [main.py:8794] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   teaching_level_for_returning_student: None
2025-07-22 20:36:14,035 - INFO - [main.py:8795] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   has_completed_diagnostic_before: False
2025-07-22 20:36:14,035 - INFO - [main.py:8796] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   is_first_encounter_for_module: True
2025-07-22 20:36:14,036 - WARNING - [main.py:8801] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 20:36:14,036 - INFO - [main.py:8809] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 PHASE INVESTIGATION:
2025-07-22 20:36:14,036 - INFO - [main.py:8810] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Retrieved from Firestore: 'smart_diagnostic_q4'
2025-07-22 20:36:14,036 - INFO - [main.py:8811] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 20:36:14,037 - INFO - [main.py:8812] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Is first encounter: True
2025-07-22 20:36:14,037 - INFO - [main.py:8813] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Diagnostic completed: False
2025-07-22 20:36:14,037 - INFO - [main.py:8819] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ Using stored phase from Firestore: 'smart_diagnostic_q4'
2025-07-22 20:36:14,038 - INFO - [main.py:8833] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 20:36:14,038 - INFO - [main.py:8835] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Final phase for AI logic: smart_diagnostic_q4
2025-07-22 20:36:14,038 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 20:36:14,038 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 20:36:14,039 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 20:36:14,039 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 20:36:14,039 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 20:36:14,039 - INFO - [main.py:8855] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 20:36:14,040 - INFO - [main.py:6144] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Diagnostic context validation passed
2025-07-22 20:36:14,040 - WARNING - [main.py:9007] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q4' for first encounter
2025-07-22 20:36:14,040 - INFO - [main.py:9030] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q4
2025-07-22 20:36:14,040 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q4'
2025-07-22 20:36:14,041 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q4'
2025-07-22 20:36:14,041 - INFO - [main.py:9042] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Robust context prepared successfully. Phase: smart_diagnostic_q4
2025-07-22 20:36:14,041 - DEBUG - [main.py:9043] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 20:36:14,042 - INFO - [main.py:9285] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q4
2025-07-22 20:36:14,042 - INFO - [main.py:9286] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎯 UNIFIED STATE MACHINE: Processing user query: That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and ...
2025-07-22 20:36:14,043 - INFO - [main.py:9329] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎯 ROUTING: Smart Diagnostic phase 'smart_diagnostic_q4' - proceeding to main AI processing
2025-07-22 20:36:14,043 - INFO - [main.py:9349] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🤖 MAIN AI PROCESSING: No specific handler, calling main AI for phase 'smart_diagnostic_q4'
2025-07-22 20:36:14,043 - INFO - [main.py:11193] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:14,043 - DEBUG - [main.py:11202] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Console output error: I/O operation on closed file.
2025-07-22 20:36:14,044 - INFO - [main.py:11235] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 📤 Sending message to existing session: That's an excellent point, Andrea! You've hit on a...
2025-07-22 20:36:14,044 - INFO - [main.py:5965] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 💰 Sending message to existing session (NO API CALL)
2025-07-22 20:36:14,887 - INFO - [main.py:5970] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ Response received from session (NO API CALL COST)
2025-07-22 20:36:14,888 - INFO - [main.py:11242] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 📥 Received response from session: You've got it exactly right, Andrea! You're thinking ahead about how AI can be used in **drug discov...
2025-07-22 20:36:14,889 - INFO - [main.py:11333] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q4
2025-07-22 20:36:14,889 - INFO - [main.py:11391] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 📝 Diagnostic in progress, checking for question progression
2025-07-22 20:36:14,889 - INFO - [main.py:11422] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ➡️ DIAGNOSTIC QUESTION PROGRESSION: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-22 20:36:14,890 - INFO - [main.py:9360] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ MAIN AI PROCESSING: Completed successfully
2025-07-22 20:36:14,890 - INFO - [main.py:9371] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 20:36:14,890 - INFO - [main.py:5710] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔄 PHASE TRANSITION: smart_diagnostic_q4 → smart_diagnostic_q4
2025-07-22 20:36:14,891 - INFO - [phase_transition_integrity.py:153] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q4 → smart_diagnostic_q4
2025-07-22 20:36:14,891 - INFO - [phase_transition_integrity.py:220] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ TRANSITION VALIDATED: smart_diagnostic_q4 → smart_diagnostic_q4
2025-07-22 20:36:14,892 - INFO - [main.py:5718] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ VALID TRANSITION: smart_diagnostic_q4 → smart_diagnostic_q4
2025-07-22 20:36:14,892 - INFO - [main.py:9387] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q4 → smart_diagnostic_q4
2025-07-22 20:36:14,892 - INFO - [main.py:9388] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 20:36:15,702 - INFO - [main.py:9402] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ UNIFIED STATE MACHINE: Session state updated with phase smart_diagnostic_q4
2025-07-22 20:36:15,702 - INFO - [main.py:9412] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 20:36:15,703 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:36:15,703 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:36:15,704 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:36:15,704 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:36:15,704 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:36:15,704 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:36:15,704 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:36:15,705 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:36:15,705 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:36:15,706 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:36:15,706 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:36:15,707 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:36:15,707 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:36:15,708 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:36:15,708 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:36:15,708 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:36:15,708 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:36:15,709 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:36:15,709 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:36:15,709 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:36:15,709 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:36:15,709 - INFO - [intelligent_guardrails.py:130] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:36:15,710 - INFO - [intelligent_guardrails.py:167] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAIL VIOLATION: scaffolding_progression - warning
2025-07-22 20:36:15,712 - INFO - [intelligent_guardrails.py:183] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=1
2025-07-22 20:36:15,713 - INFO - [intelligent_guardrails.py:675] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=1, Teaching Complete=False
2025-07-22 20:36:15,714 - INFO - [main.py:9514] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAIL VIOLATIONS: 1 found
2025-07-22 20:36:15,716 - INFO - [main.py:9519] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️   - WARNING: scaffolding_progression - May be introducing complex concepts too early
2025-07-22 20:36:15,716 - INFO - [main.py:9544] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 20:36:15,716 - WARNING - [main.py:9593] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🤖 AI RESPONSE RECEIVED:
2025-07-22 20:36:15,716 - WARNING - [main.py:9594] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🤖   - Content length: 648 chars
2025-07-22 20:36:15,717 - WARNING - [main.py:9595] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🤖   - State updates: {'new_phase': 'smart_diagnostic_q5'}
2025-07-22 20:36:15,717 - WARNING - [main.py:9596] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🤖   - Raw state block: None...
2025-07-22 20:36:15,717 - DEBUG - [main.py:10129] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 20:36:15,717 - DEBUG - [main.py:10130] - 🤖   Content: You've got it exactly right, Andrea! You're thinking ahead about how AI can be used in **drug discov...
2025-07-22 20:36:15,718 - DEBUG - [main.py:10131] - 🤖   State: {'new_phase': 'smart_diagnostic_q5'}
2025-07-22 20:36:15,718 - INFO - [main.py:10157] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 20:36:15,718 - INFO - [main.py:10158] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] CURRENT PHASE DETERMINATION: AI=smart_diagnostic_q5, Session=smart_diagnostic_q4, Final=smart_diagnostic_q5
2025-07-22 20:36:16,000 - WARNING - [main.py:10260] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q4', new_phase='smart_diagnostic_q5'
2025-07-22 20:36:16,001 - INFO - [main.py:6365] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI state update validation passed: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-22 20:36:16,001 - WARNING - [main.py:10269] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 20:36:16,002 - WARNING - [main.py:10288] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔄 PHASE TRANSITION: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-22 20:36:16,002 - INFO - [main.py:10319] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 20:36:16,003 - INFO - [main.py:10324] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 20:36:16,003 - INFO - [phase_transition_integrity.py:315] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q4 → teaching_start
2025-07-22 20:36:16,003 - INFO - [phase_transition_integrity.py:278] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 📸 DATA SNAPSHOT CREATED: Session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase smart_diagnostic_q4
2025-07-22 20:36:16,004 - WARNING - [phase_transition_integrity.py:327] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 20:36:16,004 - WARNING - [phase_transition_integrity.py:328] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 20:36:16,005 - WARNING - [phase_transition_integrity.py:329] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   - assigned_level_for_teaching in state_updates: False
2025-07-22 20:36:16,005 - WARNING - [phase_transition_integrity.py:330] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 20:36:16,005 - WARNING - [phase_transition_integrity.py:332] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   - assigned_level_for_teaching value: None
2025-07-22 20:36:16,006 - WARNING - [phase_transition_integrity.py:333] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   - state_updates keys: ['new_phase']
2025-07-22 20:36:16,007 - WARNING - [phase_transition_integrity.py:334] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 20:36:16,007 - INFO - [phase_transition_integrity.py:153] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q4 → teaching_start
2025-07-22 20:36:16,008 - WARNING - [phase_transition_integrity.py:169] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ❌ INVALID TRANSITION: smart_diagnostic_q4 → teaching_start
2025-07-22 20:36:16,009 - ERROR - [phase_transition_integrity.py:374] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔧 TRANSITION RECOVERY: smart_diagnostic_q4 → smart_diagnostic_start
2025-07-22 20:36:16,009 - DEBUG - [phase_transition_integrity.py:832] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 20:36:16,009 - DEBUG - [phase_transition_integrity.py:865] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 📝 TRANSITION RECORDED: smart_diagnostic_q4 → smart_diagnostic_start (invalid)
2025-07-22 20:36:16,010 - INFO - [phase_transition_integrity.py:391] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = smart_diagnostic_start
2025-07-22 20:36:16,010 - WARNING - [main.py:10366] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔧 STATE MACHINE: Invalid transition corrected to smart_diagnostic_q5
2025-07-22 20:36:16,011 - WARNING - [main.py:10393] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔧 TRANSITION RECOVERED: Invalid transition: smart_diagnostic_q4 → teaching_start not allowed
2025-07-22 20:36:16,011 - WARNING - [main.py:10402] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 20:36:16,013 - WARNING - [main.py:10403] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   1. Input phase: 'smart_diagnostic_q4'
2025-07-22 20:36:16,014 - WARNING - [main.py:10404] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 20:36:16,015 - WARNING - [main.py:10405] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 20:36:16,015 - WARNING - [main.py:10406] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 20:36:16,016 - WARNING - [main.py:10407] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍   5. Final phase to save: 'smart_diagnostic_q5'
2025-07-22 20:36:16,016 - WARNING - [main.py:10410] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 💾 FINAL STATE APPLICATION:
2025-07-22 20:36:16,016 - WARNING - [main.py:10411] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 💾   - Current phase input: 'smart_diagnostic_q4'
2025-07-22 20:36:16,017 - WARNING - [main.py:10412] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 💾   - Validated state updates: 26 fields
2025-07-22 20:36:16,017 - WARNING - [main.py:10413] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 💾   - Final phase to save: 'smart_diagnostic_q5'
2025-07-22 20:36:16,017 - WARNING - [main.py:10414] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 💾   - Phase change: True
2025-07-22 20:36:16,018 - WARNING - [main.py:10415] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 💾   - Integrity applied: True
2025-07-22 20:36:16,018 - INFO - [main.py:6397] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 20:36:16,018 - INFO - [main.py:6398] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Phase transition: smart_diagnostic_q4 -> smart_diagnostic_start
2025-07-22 20:36:16,019 - INFO - [main.py:6399] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Current level: 2
2025-07-22 20:36:16,019 - INFO - [main.py:6400] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Question index: 0
2025-07-22 20:36:16,019 - INFO - [main.py:6401] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   First encounter: True
2025-07-22 20:36:16,019 - INFO - [main.py:6406] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Answers collected: 0
2025-07-22 20:36:16,019 - INFO - [main.py:6407] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Levels failed: 0
2025-07-22 20:36:16,020 - INFO - [main.py:6365] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI state update validation passed: smart_diagnostic_q4 → smart_diagnostic_start
2025-07-22 20:36:16,020 - INFO - [main.py:6411] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   State update valid: True
2025-07-22 20:36:16,020 - INFO - [main.py:6418] - [4bab24be-d5e0-4078-88eb-3d50da75bd53]   Diagnostic complete: False
2025-07-22 20:36:16,020 - WARNING - [main.py:10433] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 20:36:16,021 - INFO - [main.py:10442] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-22 20:36:16,021 - INFO - [main.py:10443] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 20:36:16,536 - WARNING - [main.py:10493] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 20:36:16,537 - WARNING - [main.py:10494] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅   - Phase: smart_diagnostic_q5
2025-07-22 20:36:16,538 - WARNING - [main.py:10495] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅   - Probing Level: 2
2025-07-22 20:36:16,538 - WARNING - [main.py:10496] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅   - Question Index: 0
2025-07-22 20:36:16,539 - WARNING - [main.py:10497] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅   - Diagnostic Complete: False
2025-07-22 20:36:16,539 - WARNING - [main.py:10504] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅   - Quiz Questions Saved: 0
2025-07-22 20:36:16,540 - WARNING - [main.py:10505] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅   - Quiz Answers Saved: 0
2025-07-22 20:36:16,540 - WARNING - [main.py:10506] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅   - Quiz Started: False
2025-07-22 20:36:16,541 - DEBUG - [main.py:10555] - 🔥 STATE SAVED - Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: smart_diagnostic_q5
2025-07-22 20:36:16,542 - DEBUG - [main.py:10556] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 20:36:17,344 - DEBUG - [main.py:10614] - ✅ SESSION UPDATED - ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: smart_diagnostic_q5
2025-07-22 20:36:17,344 - DEBUG - [main.py:10615] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q4 → smart_diagnostic_q5
2025-07-22 20:36:17,345 - INFO - [main.py:10621] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ Updated existing session document: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:17,346 - INFO - [main.py:18535] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 20:36:17,347 - DEBUG - [main.py:4943] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 20:36:17,347 - DEBUG - [main.py:10708] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] No final assessment data found in AI response
2025-07-22 20:36:17,347 - DEBUG - [main.py:10731] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] No lesson completion detected (Phase: smart_diagnostic_start, Complete: False)
2025-07-22 20:36:17,348 - DEBUG - [main.py:10755] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 20:36:17,348 - DEBUG - [main.py:10756] - 🔒   Current Phase: smart_diagnostic_q4
2025-07-22 20:36:17,349 - DEBUG - [main.py:10757] - 🔒   Final Phase: smart_diagnostic_start
2025-07-22 20:36:17,349 - DEBUG - [main.py:10758] - 🔒   Diagnostic Complete: False
2025-07-22 20:36:17,350 - DEBUG - [main.py:10759] - 🔒   Assigned Level: None
2025-07-22 20:36:17,350 - INFO - [main.py:10832] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 20:36:17,351 - INFO - [main.py:10866] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 20:36:17,352 - INFO - [main.py:10874] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 20:36:17,352 - INFO - [main.py:10879] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 20:36:17,352 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:36:17,353 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:36:17,353 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:36:17,353 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:36:17,353 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:36:17,354 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:36:17,354 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:36:17,354 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:36:17,354 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:36:17,355 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:36:17,355 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:36:17,355 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:36:17,355 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:36:17,356 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:36:17,356 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:36:17,356 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:36:17,356 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:36:17,357 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:36:17,357 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:36:17,357 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:36:17,357 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:36:17,358 - INFO - [main.py:10888] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 20:36:17,358 - INFO - [intelligent_guardrails.py:130] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:36:17,358 - INFO - [intelligent_guardrails.py:167] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAIL VIOLATION: scaffolding_progression - warning
2025-07-22 20:36:17,359 - INFO - [intelligent_guardrails.py:183] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=1
2025-07-22 20:36:17,359 - INFO - [main.py:10950] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=1
2025-07-22 20:36:17,360 - WARNING - [main.py:10954] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] 🚨 GUARDRAIL VIOLATION: scaffolding_progression - May be introducing complex concepts too early (Severity: GuardrailSeverity.WARNING)
2025-07-22 20:36:17,360 - INFO - [main.py:10978] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 20:36:17,361 - DEBUG - [main.py:11035] - 🎯 RESPONSE READY:
2025-07-22 20:36:17,361 - DEBUG - [main.py:11036] - 🎯   Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:17,362 - DEBUG - [main.py:11037] - 🎯   Phase: smart_diagnostic_q4 → smart_diagnostic_start
2025-07-22 20:36:17,363 - DEBUG - [main.py:11038] - 🎯   Content: You've got it exactly right, Andrea! You're thinki...
2025-07-22 20:36:17,364 - DEBUG - [main.py:11039] - 🎯   Request ID: 4bab24be-d5e0-4078-88eb-3d50da75bd53
2025-07-22 20:36:17,365 - INFO - [main.py:11045] - [4bab24be-d5e0-4078-88eb-3d50da75bd53] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 20:36:17,366 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,366 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,366 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,366 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:17,367 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:17,367 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:17,367 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:17,367 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,368 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:17,368 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,368 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,368 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,369 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,369 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,369 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:17,370 - WARNING - [main.py:888] - High response time detected: 5.78s for enhance_content_api
2025-07-22 20:36:50,125 - INFO - [main.py:7143] - Incoming request: {"request_id": "********-c489-4a4b-803c-3a82c333c82d", "timestamp": "2025-07-22T19:36:50.125009+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "I think AI can be like a super-fast scientist trying to find new medicines! It can look at millions of tiny things called molecules and see which ones might be good for fighting a specific sickness, like a detective looking for a special key to unlock a door.[1][2][3] It can also help scientists guess which molecules will work best by predicting how they'll behave in the body.[1][4] This way, they don't have to try out millions of different things in a lab, which takes a very, very long time. AI can help them find the best medicine much faster, so people can get better sooner!", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5", "chat_history": [{"role": "user", "content": "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", "timestamp": "2025-07-22T19:34:31.251Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! You've already grasped some really important ideas about how AI can be a \"super-smart helper\" in healthcare. You're right, it's all about computers learning and processing lots of information to help doctors.\n\nLet's move on to our next question!\n\n**Question 2 of 5 (Level 3 Concept Exploration):** When you hear \"AI in healthcare,\" what's the first specific thing that comes to your mind that AI might help with?", "timestamp": "2025-07-22T19:34:37.380Z"}, {"role": "user", "content": "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", "timestamp": "2025-07-22T19:35:04.917Z"}, {"role": "assistant", "content": "That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medical images like X-rays and scans. It's like having a super-powered magnifying glass for doctors, helping them detect even the smallest signs of a **disease**. This is a key part of **diagnosis**, which means figuring out what's wrong.\n\nYou're doing great! Let's try another question.\n\n**Question 3 of 5 (Level 4 Application Insight):** Besides looking at pictures, can you think of another way AI might help doctors when someone is feeling unwell?", "timestamp": "2025-07-22T19:35:11.242Z"}, {"role": "user", "content": "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", "timestamp": "2025-07-22T19:35:38.820Z"}, {"role": "assistant", "content": "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.\n\nYou're building a great understanding! Here's your next question:\n\n**Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", "timestamp": "2025-07-22T19:35:45.259Z"}, {"role": "user", "content": "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.  You're building a great understanding! Here's your next question:  **Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", "timestamp": "2025-07-22T19:36:11.198Z"}, {"role": "assistant", "content": "You've got it exactly right, Andrea! You're thinking ahead about how AI can be used in **drug discovery**. That's a very advanced concept, and you've explained it so well! AI can sift through massive amounts of scientific data to identify potential new medicines much faster than humans can. It can also help predict how a new drug might work in the body, which is incredibly helpful in developing treatments for diseases.\n\nWe're almost at the end of our diagnostic questions!\n\n**Question 5 of 5 (Level 6 Synthesis):** Putting it all together, how does AI's ability to help with diagnosis and potentially drug discovery benefit people who are sick?", "timestamp": "2025-07-22T19:36:17.387Z"}]}}
2025-07-22 20:36:50,126 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 20:36:50,126 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gj0qnEDDwAwV0F7rNkWh0AcRM_Vqch0hfz0_Gre-6PN9Ghpx_KAU3ISJU2J4mFlUyqTNjIYwn4CYbeHQcnCVC9TG8q0ADK-fDuwpAjd43B7b7hQvBLumyroLvpp-ck-bA7FeUD7-cawX6D5ltTIXZUOV-kLJ4yceZzYjPuKze7oeqPzjAiYvgX8J2FSG4MBaovb1BjlR-AoNq_jsjcJryDf3ndrLxmRY2_r86KIuvX3nbm29snqIUaWed6ukFgi35NpPRt1QnbaLdGL1kNzv-epB_8HoN48szPVY5NXOb2i0peCGEc2w2C_AFWqHIFviUy3v1mhg7ZLNsHKd5xFJNw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '5682', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 20:36:50,126 - INFO - [auth_decorator.py:70] - 🔒 Request ID: ********-c489-4a4b-803c-3a82c333c82d
2025-07-22 20:36:50,126 - INFO - [auth_decorator.py:74] - [********-c489-4a4b-803c-3a82c333c82d][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 20:36:50,127 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 20:36:50,127 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 20:36:50,127 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 20:36:50,127 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 20:36:50,127 - INFO - [auth_decorator.py:95] - [********-c489-4a4b-803c-3a82c333c82d][require_auth] Development mode detected - bypassing authentication
2025-07-22 20:36:50,128 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 20:36:50,128 - INFO - [auth_decorator.py:121] - [********-c489-4a4b-803c-3a82c333c82d][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 20:36:50,520 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:36:50,520 - INFO - [auth_decorator.py:164] - [********-c489-4a4b-803c-3a82c333c82d][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:36:50,520 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 20:36:50,521 - INFO - [main.py:7327] -
================================================================================
2025-07-22 20:36:50,522 - WARNING - [main.py:7328] - [********-c489-4a4b-803c-3a82c333c82d] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 20:36:50,522 - WARNING - [main.py:7329] - [********-c489-4a4b-803c-3a82c333c82d] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 20:36:50,523 - DEBUG - [main.py:7337] - [********-c489-4a4b-803c-3a82c333c82d] Console output error: I/O operation on closed file.
2025-07-22 20:36:50,525 - INFO - [main.py:8179] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"I think AI can be like a super-fast scientist trying to find new medicines! It can look at millions of tiny things called molecules and see which ones might be good for fighting a specific sickness, like a detective looking for a special key to unlock a door.[1][2][3] It can also help scientists guess which molecules will work best by predicting how they'll behave in the body.[1][4] This way, they don't have to try...
2025-07-22 20:36:50,526 - INFO - [main.py:8181] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "I think AI can be like a super-fast scientist trying to find new medicines! It can look at millions of tiny things called molecules and see which ones might be good for fighting a specific sickness, like a detective looking for a special key to unlock a door.[1][2][3] It can also help scientists guess which molecules will work best by predicting how they'll behave in the body.[1][4] This way, they don't have to try out millions of different things in a lab, which takes a very, very long time. AI can help them find the best medicine much faster, so people can get better sooner!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', 'chat_history': [{'role': 'user', 'content': "Hello again! I'm ready for my lesson! AI in healthcare is like having super-smart helpers for doctors and nurses. It uses computers that can learn and think, almost like a brain, to help people get better. It's really good at looking at lots of information very quickly, like spotting a tiny detail in a picture or remembering lots of facts about sicknesses. This helps doctors find out what's wrong with people when they're sick and helps them choose the best medicine to make them feel better.", 'timestamp': '2025-07-22T19:34:31.251Z'}, {'role': 'assistant', 'content': 'That\'s a fantastic start, Andrea! You\'ve already grasped some really important ideas about how AI can be a "super-smart helper" in healthcare. You\'re right, it\'s all about computers learning and processing lots of information to help doctors.\n\nLet\'s move on to our next question!\n\n**Question 2 of 5 (Level 3 Concept Exploration):** When you hear "AI in healthcare," what\'s the first specific thing that comes to your mind that AI might help with?', 'timestamp': '2025-07-22T19:34:37.380Z'}, {'role': 'user', 'content': "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", 'timestamp': '2025-07-22T19:35:04.917Z'}, {'role': 'assistant', 'content': "That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medical images like X-rays and scans. It's like having a super-powered magnifying glass for doctors, helping them detect even the smallest signs of a **disease**. This is a key part of **diagnosis**, which means figuring out what's wrong.\n\nYou're doing great! Let's try another question.\n\n**Question 3 of 5 (Level 4 Application Insight):** Besides looking at pictures, can you think of another way AI might help doctors when someone is feeling unwell?", 'timestamp': '2025-07-22T19:35:11.242Z'}, {'role': 'user', 'content': "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", 'timestamp': '2025-07-22T19:35:38.820Z'}, {'role': 'assistant', 'content': "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.\n\nYou're building a great understanding! Here's your next question:\n\n**Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", 'timestamp': '2025-07-22T19:35:45.259Z'}, {'role': 'user', 'content': "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.  You're building a great understanding! Here's your next question:  **Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", 'timestamp': '2025-07-22T19:36:11.198Z'}, {'role': 'assistant', 'content': "You've got it exactly right, Andrea! You're thinking ahead about how AI can be used in **drug discovery**. That's a very advanced concept, and you've explained it so well! AI can sift through massive amounts of scientific data to identify potential new medicines much faster than humans can. It can also help predict how a new drug might work in the body, which is incredibly helpful in developing treatments for diseases.\n\nWe're almost at the end of our diagnostic questions!\n\n**Question 5 of 5 (Level 6 Synthesis):** Putting it all together, how does AI's ability to help with diagnosis and potentially drug discovery benefit people who are sick?", 'timestamp': '2025-07-22T19:36:17.387Z'}]}
2025-07-22 20:36:50,527 - INFO - [main.py:8183] - [********-c489-4a4b-803c-3a82c333c82d] 🔍  - Session ID from payload: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:50,527 - INFO - [main.py:8184] - [********-c489-4a4b-803c-3a82c333c82d] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 20:36:50,527 - INFO - [main.py:8185] - [********-c489-4a4b-803c-3a82c333c82d] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 20:36:50,528 - DEBUG - [main.py:8223] - [********-c489-4a4b-803c-3a82c333c82d] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:36:50,529 - INFO - [main.py:8224] - [********-c489-4a4b-803c-3a82c333c82d] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:36:50,530 - INFO - [main.py:8264] - [********-c489-4a4b-803c-3a82c333c82d] Level not provided, determined from grade 'primary-5': 5
2025-07-22 20:36:50,810 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 20:36:50,810 - INFO - [main.py:8281] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 20:36:50,811 - INFO - [main.py:8282] - [********-c489-4a4b-803c-3a82c333c82d] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 20:36:50,811 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 20:36:51,326 - INFO - [main.py:8312] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 20:36:51,326 - INFO - [main.py:8375] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 20:36:51,326 - INFO - [main.py:8500] - [********-c489-4a4b-803c-3a82c333c82d] ✅ Successfully retrieved lesson from primary path
2025-07-22 20:36:51,327 - INFO - [main.py:8511] - [********-c489-4a4b-803c-3a82c333c82d] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 20:36:51,327 - INFO - [main.py:8550] - [********-c489-4a4b-803c-3a82c333c82d] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 20:36:51,327 - INFO - [main.py:4456] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 20:36:51,630 - INFO - [main.py:4522] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 20:36:51,630 - INFO - [main.py:4522] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 20:36:51,631 - INFO - [main.py:4522] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 20:36:51,631 - INFO - [main.py:4522] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 20:36:51,632 - INFO - [main.py:4522] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 20:36:51,632 - INFO - [main.py:4591] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 20:36:51,632 - DEBUG - [main.py:4605] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 20:36:51,633 - DEBUG - [main.py:4608] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 20:36:51,634 - DEBUG - [main.py:4609] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 20:36:51,634 - DEBUG - [main.py:4610] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 20:36:51,635 - INFO - [main.py:4614] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Calling Gemini API for module inference...
2025-07-22 20:36:52,109 - INFO - [main.py:4624] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Gemini API call completed in 0.47s. Raw response: 'ai_tools_and_applications'
2025-07-22 20:36:52,110 - DEBUG - [main.py:4646] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 20:36:52,110 - INFO - [main.py:4651] - [********-c489-4a4b-803c-3a82c333c82d] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 20:36:52,110 - INFO - [main.py:8584] - [********-c489-4a4b-803c-3a82c333c82d] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 20:36:52,110 - INFO - [main.py:8621] - [********-c489-4a4b-803c-3a82c333c82d] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 20:36:52,390 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 20:36:52,925 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 20:36:52,925 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:52,925 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 20:36:52,925 - DEBUG - [main.py:8677] - 🔍   - Current Phase: smart_diagnostic_q5
2025-07-22 20:36:52,926 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 2
2025-07-22 20:36:52,926 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 20:36:52,926 - WARNING - [main.py:8685] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 SESSION STATE DEBUG:
2025-07-22 20:36:52,927 - WARNING - [main.py:8686] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   - Session exists: True
2025-07-22 20:36:52,927 - WARNING - [main.py:8687] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   - Current phase: smart_diagnostic_q5
2025-07-22 20:36:52,927 - WARNING - [main.py:8688] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'new_phase', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'teaching_interactions', 'last_modified', 'latest_assessed_level_for_module', 'quiz_answers', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 20:36:52,928 - DEBUG - [main.py:8706] - [********-c489-4a4b-803c-3a82c333c82d] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 20:36:52,929 - DEBUG - [main.py:8707] - [********-c489-4a4b-803c-3a82c333c82d]   Retrieved Phase: 'smart_diagnostic_q5'
2025-07-22 20:36:52,930 - DEBUG - [main.py:8708] - [********-c489-4a4b-803c-3a82c333c82d]   Diagnostic Completed: False
2025-07-22 20:36:52,930 - DEBUG - [main.py:8709] - [********-c489-4a4b-803c-3a82c333c82d]   Assigned Level: None
2025-07-22 20:36:52,931 - WARNING - [main.py:8710] - [********-c489-4a4b-803c-3a82c333c82d] 🔒 STATE PROTECTION: phase='smart_diagnostic_q5', diagnostic_done=False, level=None
2025-07-22 20:36:52,931 - INFO - [main.py:8742] - [********-c489-4a4b-803c-3a82c333c82d] ✅ State protection not triggered (diagnostic=False, level=None)
2025-07-22 20:36:52,932 - INFO - [main.py:8743] - [********-c489-4a4b-803c-3a82c333c82d] State protection not triggered
2025-07-22 20:36:52,933 - INFO - [main.py:8791] - [********-c489-4a4b-803c-3a82c333c82d]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 20:36:52,933 - INFO - [main.py:8792] - [********-c489-4a4b-803c-3a82c333c82d]   assigned_level_for_teaching (session): None
2025-07-22 20:36:52,934 - INFO - [main.py:8793] - [********-c489-4a4b-803c-3a82c333c82d]   latest_assessed_level (profile): None
2025-07-22 20:36:52,934 - INFO - [main.py:8794] - [********-c489-4a4b-803c-3a82c333c82d]   teaching_level_for_returning_student: None
2025-07-22 20:36:52,934 - INFO - [main.py:8795] - [********-c489-4a4b-803c-3a82c333c82d]   has_completed_diagnostic_before: False
2025-07-22 20:36:52,935 - INFO - [main.py:8796] - [********-c489-4a4b-803c-3a82c333c82d]   is_first_encounter_for_module: True
2025-07-22 20:36:52,936 - WARNING - [main.py:8801] - [********-c489-4a4b-803c-3a82c333c82d] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
2025-07-22 20:36:52,936 - INFO - [main.py:8809] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 PHASE INVESTIGATION:
2025-07-22 20:36:52,937 - INFO - [main.py:8810] - [********-c489-4a4b-803c-3a82c333c82d]   Retrieved from Firestore: 'smart_diagnostic_q5'
2025-07-22 20:36:52,937 - INFO - [main.py:8811] - [********-c489-4a4b-803c-3a82c333c82d]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 20:36:52,937 - INFO - [main.py:8812] - [********-c489-4a4b-803c-3a82c333c82d]   Is first encounter: True
2025-07-22 20:36:52,937 - INFO - [main.py:8813] - [********-c489-4a4b-803c-3a82c333c82d]   Diagnostic completed: False
2025-07-22 20:36:52,938 - INFO - [main.py:8819] - [********-c489-4a4b-803c-3a82c333c82d] ✅ Using stored phase from Firestore: 'smart_diagnostic_q5'
2025-07-22 20:36:52,938 - INFO - [main.py:8833] - [********-c489-4a4b-803c-3a82c333c82d] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 20:36:52,938 - INFO - [main.py:8835] - [********-c489-4a4b-803c-3a82c333c82d] Final phase for AI logic: smart_diagnostic_q5
2025-07-22 20:36:52,938 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 20:36:52,938 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 20:36:52,939 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 20:36:52,939 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 20:36:52,940 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 20:36:52,940 - INFO - [main.py:8855] - [********-c489-4a4b-803c-3a82c333c82d] NEW SESSION: Forcing question_index to 0 (was: 0)
2025-07-22 20:36:52,940 - INFO - [main.py:6144] - [********-c489-4a4b-803c-3a82c333c82d] Diagnostic context validation passed
2025-07-22 20:36:52,940 - WARNING - [main.py:9007] - [********-c489-4a4b-803c-3a82c333c82d] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'smart_diagnostic_q5' for first encounter
2025-07-22 20:36:52,941 - INFO - [main.py:9030] - [********-c489-4a4b-803c-3a82c333c82d] Skipping diagnostic context enhancement for non-diagnostic phase: smart_diagnostic_q5
2025-07-22 20:36:52,941 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'smart_diagnostic_q5'
2025-07-22 20:36:52,941 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'smart_diagnostic_q5'
2025-07-22 20:36:52,941 - INFO - [main.py:9042] - [********-c489-4a4b-803c-3a82c333c82d] Robust context prepared successfully. Phase: smart_diagnostic_q5
2025-07-22 20:36:52,942 - DEBUG - [main.py:9043] - [********-c489-4a4b-803c-3a82c333c82d] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 20:36:52,942 - INFO - [main.py:9285] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 UNIFIED STATE MACHINE: Current phase = smart_diagnostic_q5
2025-07-22 20:36:52,943 - INFO - [main.py:9286] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 UNIFIED STATE MACHINE: Processing user query: I think AI can be like a super-fast scientist trying to find new medicines! It can look at millions ...
2025-07-22 20:36:52,943 - INFO - [main.py:9329] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 ROUTING: Smart Diagnostic phase 'smart_diagnostic_q5' - proceeding to main AI processing
2025-07-22 20:36:52,944 - INFO - [main.py:9349] - [********-c489-4a4b-803c-3a82c333c82d] 🤖 MAIN AI PROCESSING: No specific handler, calling main AI for phase 'smart_diagnostic_q5'
2025-07-22 20:36:52,944 - INFO - [main.py:11193] - [********-c489-4a4b-803c-3a82c333c82d] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:52,944 - DEBUG - [main.py:11202] - [********-c489-4a4b-803c-3a82c333c82d] Console output error: I/O operation on closed file.
2025-07-22 20:36:52,944 - INFO - [main.py:11235] - [********-c489-4a4b-803c-3a82c333c82d] 📤 Sending message to existing session: I think AI can be like a super-fast scientist tryi...
2025-07-22 20:36:52,945 - INFO - [main.py:5965] - [********-c489-4a4b-803c-3a82c333c82d] 💰 Sending message to existing session (NO API CALL)
2025-07-22 20:36:53,828 - INFO - [main.py:5970] - [********-c489-4a4b-803c-3a82c333c82d] ✅ Response received from session (NO API CALL COST)
2025-07-22 20:36:53,829 - INFO - [main.py:11242] - [********-c489-4a4b-803c-3a82c333c82d] 📥 Received response from session: Excellent! You've perfectly described how AI acts like a super-fast scientist in **drug discovery**....
2025-07-22 20:36:53,832 - INFO - [main.py:11333] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 SMART DIAGNOSTIC PHASE DETECTED: smart_diagnostic_q5
2025-07-22 20:36:53,833 - INFO - [main.py:11351] - [********-c489-4a4b-803c-3a82c333c82d] ✅ DIAGNOSTIC COMPLETION DETECTED
2025-07-22 20:36:53,837 - INFO - [main.py:11376] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 EXTRACTED TEACHING LEVEL: 6
2025-07-22 20:36:53,838 - INFO - [main.py:11388] - [********-c489-4a4b-803c-3a82c333c82d] 🚀 DIAGNOSTIC → TEACHING TRANSITION PREPARED
2025-07-22 20:36:53,838 - INFO - [main.py:9360] - [********-c489-4a4b-803c-3a82c333c82d] ✅ MAIN AI PROCESSING: Completed successfully
2025-07-22 20:36:53,838 - INFO - [main.py:9371] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 20:36:53,838 - INFO - [main.py:5710] - [********-c489-4a4b-803c-3a82c333c82d] 🔄 PHASE TRANSITION: smart_diagnostic_q5 → smart_diagnostic_q5
2025-07-22 20:36:53,839 - INFO - [phase_transition_integrity.py:153] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q5 → smart_diagnostic_q5
2025-07-22 20:36:53,839 - INFO - [phase_transition_integrity.py:220] - [********-c489-4a4b-803c-3a82c333c82d] ✅ TRANSITION VALIDATED: smart_diagnostic_q5 → smart_diagnostic_q5
2025-07-22 20:36:53,839 - INFO - [main.py:5718] - [********-c489-4a4b-803c-3a82c333c82d] ✅ VALID TRANSITION: smart_diagnostic_q5 → smart_diagnostic_q5
2025-07-22 20:36:53,839 - INFO - [main.py:9387] - [********-c489-4a4b-803c-3a82c333c82d] 🔒 UNIFIED PATHWAY ENFORCED: smart_diagnostic_q5 → smart_diagnostic_q5
2025-07-22 20:36:53,840 - INFO - [main.py:9388] - [********-c489-4a4b-803c-3a82c333c82d] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 20:36:54,668 - INFO - [main.py:9402] - [********-c489-4a4b-803c-3a82c333c82d] ✅ UNIFIED STATE MACHINE: Session state updated with phase smart_diagnostic_q5
2025-07-22 20:36:54,669 - INFO - [main.py:9412] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 20:36:54,669 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:36:54,669 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:36:54,670 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:36:54,670 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:36:54,670 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:36:54,670 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:36:54,671 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:36:54,671 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:36:54,671 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:36:54,672 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:36:54,672 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:36:54,673 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:36:54,673 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:36:54,674 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:36:54,674 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:36:54,675 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:36:54,675 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:36:54,675 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:36:54,675 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:36:54,676 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:36:54,676 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:36:54,676 - INFO - [intelligent_guardrails.py:130] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:36:54,678 - WARNING - [intelligent_guardrails.py:146] - [********-c489-4a4b-803c-3a82c333c82d] 🚨 QUIZ REQUEST BLOCKED: Teaching incomplete
2025-07-22 20:36:54,682 - INFO - [intelligent_guardrails.py:451] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ BLOCKING VIOLATIONS: Preventing quiz transition
2025-07-22 20:36:54,686 - INFO - [intelligent_guardrails.py:464] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ BACKEND INSTRUCTION (INTERNAL ONLY): Continue teaching until all completion criteria are met
2025-07-22 20:36:54,699 - INFO - [intelligent_guardrails.py:512] - [********-c489-4a4b-803c-3a82c333c82d] 📝 STUDENT-FRIENDLY CONTINUATION: Generated encouraging message instead of backend instruction
2025-07-22 20:36:54,700 - INFO - [intelligent_guardrails.py:183] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ GUARDRAILS RESULT: Valid=False, Violations=1
2025-07-22 20:36:54,701 - INFO - [intelligent_guardrails.py:675] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ GUARDRAILS APPLIED: Valid=False, Violations=1, Teaching Complete=False
2025-07-22 20:36:54,701 - INFO - [main.py:9514] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ GUARDRAIL VIOLATIONS: 1 found
2025-07-22 20:36:54,701 - INFO - [main.py:9519] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️   - BLOCKING: teaching_incomplete_quiz_block - Quiz transition blocked - teaching not complete
2025-07-22 20:36:54,701 - WARNING - [main.py:9525] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ BLOCKING VIOLATION: Preventing inappropriate progression
2025-07-22 20:36:54,702 - INFO - [main.py:9544] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ GUARDRAILS APPLIED: Valid=False, Enhanced=True
2025-07-22 20:36:54,702 - WARNING - [main.py:9593] - [********-c489-4a4b-803c-3a82c333c82d] 🤖 AI RESPONSE RECEIVED:
2025-07-22 20:36:54,702 - WARNING - [main.py:9594] - [********-c489-4a4b-803c-3a82c333c82d] 🤖   - Content length: 773 chars
2025-07-22 20:36:54,703 - WARNING - [main.py:9595] - [********-c489-4a4b-803c-3a82c333c82d] 🤖   - State updates: {'new_phase': 'teaching_start', 'diagnostic_completed_this_session': True, 'assigned_level_for_teaching': 6, 'current_session_working_level': 6, 'latest_assessed_level_for_module': 6, 'diagnostic_complete': True}
2025-07-22 20:36:54,703 - WARNING - [main.py:9596] - [********-c489-4a4b-803c-3a82c333c82d] 🤖   - Raw state block: None...
2025-07-22 20:36:54,703 - DEBUG - [main.py:10129] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 20:36:54,703 - DEBUG - [main.py:10130] - 🤖   Content: Excellent! You've perfectly described how AI acts like a super-fast scientist in **drug discovery**....
2025-07-22 20:36:54,704 - DEBUG - [main.py:10131] - 🤖   State: {'new_phase': 'teaching_start', 'diagnostic_completed_this_session': True, 'assigned_level_for_teaching': 6, 'current_session_working_level': 6, 'latest_assessed_level_for_module': 6, 'diagnostic_complete': True}
2025-07-22 20:36:54,704 - INFO - [main.py:10157] - [********-c489-4a4b-803c-3a82c333c82d] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 20:36:54,704 - INFO - [main.py:10158] - [********-c489-4a4b-803c-3a82c333c82d] CURRENT PHASE DETERMINATION: AI=teaching_start, Session=smart_diagnostic_q5, Final=teaching_start
2025-07-22 20:36:54,991 - WARNING - [main.py:10260] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 STATE UPDATE VALIDATION: current_phase='smart_diagnostic_q5', new_phase='teaching_start'
2025-07-22 20:36:54,992 - INFO - [main.py:6365] - [********-c489-4a4b-803c-3a82c333c82d] AI state update validation passed: smart_diagnostic_q5 → teaching_start
2025-07-22 20:36:54,992 - WARNING - [main.py:10269] - [********-c489-4a4b-803c-3a82c333c82d] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 20:36:54,992 - WARNING - [main.py:10288] - [********-c489-4a4b-803c-3a82c333c82d] 🔄 PHASE TRANSITION: smart_diagnostic_q5 → teaching_start
2025-07-22 20:36:54,993 - INFO - [main.py:10319] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching_start
2025-07-22 20:36:54,993 - INFO - [main.py:10324] - [********-c489-4a4b-803c-3a82c333c82d] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 20:36:54,994 - INFO - [phase_transition_integrity.py:315] - [********-c489-4a4b-803c-3a82c333c82d] 🔄 APPLYING TRANSITION WITH INTEGRITY: smart_diagnostic_q5 → teaching_start
2025-07-22 20:36:54,994 - INFO - [phase_transition_integrity.py:278] - [********-c489-4a4b-803c-3a82c333c82d] 📸 DATA SNAPSHOT CREATED: Session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase smart_diagnostic_q5
2025-07-22 20:36:54,995 - WARNING - [phase_transition_integrity.py:327] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 DEBUG MERGED DATA FOR TEACHING_START:
2025-07-22 20:36:54,995 - WARNING - [phase_transition_integrity.py:328] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   - assigned_level_for_teaching in session_data: True
2025-07-22 20:36:54,996 - WARNING - [phase_transition_integrity.py:329] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   - assigned_level_for_teaching in state_updates: True
2025-07-22 20:36:54,997 - WARNING - [phase_transition_integrity.py:330] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   - assigned_level_for_teaching in merged_data: True
2025-07-22 20:36:54,997 - WARNING - [phase_transition_integrity.py:332] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   - assigned_level_for_teaching value: 6
2025-07-22 20:36:54,998 - WARNING - [phase_transition_integrity.py:333] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   - state_updates keys: ['new_phase', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'current_session_working_level', 'latest_assessed_level_for_module', 'diagnostic_complete']
2025-07-22 20:36:54,998 - WARNING - [phase_transition_integrity.py:334] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   - merged_data keys (first 20): ['session_id', 'student_id', 'lesson_ref', 'current_phase', 'diagnostic_complete', 'assigned_level_for_teaching', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_complete', 'quiz_questions_generated', 'quiz_answers', 'concepts_covered', 'learning_objectives_met', 'lesson_start_time', 'grade', 'subject', 'performance_data', 'new_phase']
2025-07-22 20:36:54,998 - INFO - [phase_transition_integrity.py:153] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 PHASE TRANSITION VALIDATION: smart_diagnostic_q5 → teaching_start
2025-07-22 20:36:54,999 - INFO - [phase_transition_integrity.py:220] - [********-c489-4a4b-803c-3a82c333c82d] ✅ TRANSITION VALIDATED: smart_diagnostic_q5 → teaching_start
2025-07-22 20:36:54,999 - INFO - [phase_transition_integrity.py:345] - [********-c489-4a4b-803c-3a82c333c82d] ✅ TRANSITION APPLIED: smart_diagnostic_q5 → teaching_start
2025-07-22 20:36:55,000 - DEBUG - [phase_transition_integrity.py:832] - [********-c489-4a4b-803c-3a82c333c82d] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 20:36:55,001 - DEBUG - [phase_transition_integrity.py:865] - [********-c489-4a4b-803c-3a82c333c82d] 📝 TRANSITION RECORDED: smart_diagnostic_q5 → teaching_start (valid)
2025-07-22 20:36:55,001 - INFO - [phase_transition_integrity.py:391] - [********-c489-4a4b-803c-3a82c333c82d] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching_start
2025-07-22 20:36:55,002 - WARNING - [main.py:10366] - [********-c489-4a4b-803c-3a82c333c82d] 🔧 STATE MACHINE: Invalid transition corrected to teaching
2025-07-22 20:36:55,002 - INFO - [main.py:10397] - [********-c489-4a4b-803c-3a82c333c82d] ✅ TRANSITION VALIDATED: smart_diagnostic_q5 → teaching
2025-07-22 20:36:55,002 - WARNING - [main.py:10402] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 20:36:55,003 - WARNING - [main.py:10403] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   1. Input phase: 'smart_diagnostic_q5'
2025-07-22 20:36:55,003 - WARNING - [main.py:10404] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 20:36:55,004 - WARNING - [main.py:10405] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   3. Proposed phase: 'teaching_start'
2025-07-22 20:36:55,004 - WARNING - [main.py:10406] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   4. Integrity validation result: 'valid'
2025-07-22 20:36:55,004 - WARNING - [main.py:10407] - [********-c489-4a4b-803c-3a82c333c82d] 🔍   5. Final phase to save: 'teaching'
2025-07-22 20:36:55,005 - WARNING - [main.py:10410] - [********-c489-4a4b-803c-3a82c333c82d] 💾 FINAL STATE APPLICATION:
2025-07-22 20:36:55,005 - WARNING - [main.py:10411] - [********-c489-4a4b-803c-3a82c333c82d] 💾   - Current phase input: 'smart_diagnostic_q5'
2025-07-22 20:36:55,005 - WARNING - [main.py:10412] - [********-c489-4a4b-803c-3a82c333c82d] 💾   - Validated state updates: 27 fields
2025-07-22 20:36:55,006 - WARNING - [main.py:10413] - [********-c489-4a4b-803c-3a82c333c82d] 💾   - Final phase to save: 'teaching'
2025-07-22 20:36:55,006 - WARNING - [main.py:10414] - [********-c489-4a4b-803c-3a82c333c82d] 💾   - Phase change: True
2025-07-22 20:36:55,006 - WARNING - [main.py:10415] - [********-c489-4a4b-803c-3a82c333c82d] 💾   - Integrity applied: True
2025-07-22 20:36:55,007 - INFO - [main.py:6397] - [********-c489-4a4b-803c-3a82c333c82d] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 20:36:55,007 - INFO - [main.py:6398] - [********-c489-4a4b-803c-3a82c333c82d]   Phase transition: smart_diagnostic_q5 -> teaching_start
2025-07-22 20:36:55,007 - INFO - [main.py:6399] - [********-c489-4a4b-803c-3a82c333c82d]   Current level: 2
2025-07-22 20:36:55,007 - INFO - [main.py:6400] - [********-c489-4a4b-803c-3a82c333c82d]   Question index: 0
2025-07-22 20:36:55,008 - INFO - [main.py:6401] - [********-c489-4a4b-803c-3a82c333c82d]   First encounter: True
2025-07-22 20:36:55,008 - INFO - [main.py:6406] - [********-c489-4a4b-803c-3a82c333c82d]   Answers collected: 0
2025-07-22 20:36:55,008 - INFO - [main.py:6407] - [********-c489-4a4b-803c-3a82c333c82d]   Levels failed: 0
2025-07-22 20:36:55,008 - INFO - [main.py:6365] - [********-c489-4a4b-803c-3a82c333c82d] AI state update validation passed: smart_diagnostic_q5 → teaching_start
2025-07-22 20:36:55,009 - INFO - [main.py:6411] - [********-c489-4a4b-803c-3a82c333c82d]   State update valid: True
2025-07-22 20:36:55,009 - INFO - [main.py:6418] - [********-c489-4a4b-803c-3a82c333c82d]   Diagnostic complete: True
2025-07-22 20:36:55,009 - INFO - [main.py:6420] - [********-c489-4a4b-803c-3a82c333c82d]   Assigned level: 6
2025-07-22 20:36:55,010 - WARNING - [main.py:10428] - [********-c489-4a4b-803c-3a82c333c82d] 🔧 PROBING LEVEL SYNC: Set probing level to 6 to match assigned teaching level
2025-07-22 20:36:55,010 - WARNING - [main.py:10433] - [********-c489-4a4b-803c-3a82c333c82d] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 20:36:55,011 - INFO - [main.py:10442] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-22 20:36:55,011 - INFO - [main.py:10443] - [********-c489-4a4b-803c-3a82c333c82d] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 20:36:55,575 - WARNING - [main.py:10493] - [********-c489-4a4b-803c-3a82c333c82d] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 20:36:55,575 - WARNING - [main.py:10494] - [********-c489-4a4b-803c-3a82c333c82d] ✅   - Phase: teaching
2025-07-22 20:36:55,576 - WARNING - [main.py:10495] - [********-c489-4a4b-803c-3a82c333c82d] ✅   - Probing Level: 6
2025-07-22 20:36:55,576 - WARNING - [main.py:10496] - [********-c489-4a4b-803c-3a82c333c82d] ✅   - Question Index: 0
2025-07-22 20:36:55,576 - WARNING - [main.py:10497] - [********-c489-4a4b-803c-3a82c333c82d] ✅   - Diagnostic Complete: True
2025-07-22 20:36:55,576 - WARNING - [main.py:10504] - [********-c489-4a4b-803c-3a82c333c82d] ✅   - Quiz Questions Saved: 0
2025-07-22 20:36:55,576 - WARNING - [main.py:10505] - [********-c489-4a4b-803c-3a82c333c82d] ✅   - Quiz Answers Saved: 0
2025-07-22 20:36:55,577 - WARNING - [main.py:10506] - [********-c489-4a4b-803c-3a82c333c82d] ✅   - Quiz Started: False
2025-07-22 20:36:55,577 - DEBUG - [main.py:10555] - 🔥 STATE SAVED - Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: teaching
2025-07-22 20:36:55,577 - DEBUG - [main.py:10556] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 20:36:56,404 - DEBUG - [main.py:10614] - ✅ SESSION UPDATED - ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: teaching
2025-07-22 20:36:56,405 - DEBUG - [main.py:10615] - ✅ INTERACTION LOGGED - Phase: smart_diagnostic_q5 → teaching
2025-07-22 20:36:56,406 - INFO - [main.py:10621] - [********-c489-4a4b-803c-3a82c333c82d] ✅ Updated existing session document: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:56,406 - INFO - [main.py:18535] - [********-c489-4a4b-803c-3a82c333c82d] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 20:36:56,406 - DEBUG - [main.py:4943] - [********-c489-4a4b-803c-3a82c333c82d] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 20:36:56,406 - DEBUG - [main.py:10708] - [********-c489-4a4b-803c-3a82c333c82d] No final assessment data found in AI response
2025-07-22 20:36:56,407 - DEBUG - [main.py:10731] - [********-c489-4a4b-803c-3a82c333c82d] No lesson completion detected (Phase: teaching_start, Complete: False)
2025-07-22 20:36:56,407 - DEBUG - [main.py:10755] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 20:36:56,408 - DEBUG - [main.py:10756] - 🔒   Current Phase: smart_diagnostic_q5
2025-07-22 20:36:56,409 - DEBUG - [main.py:10757] - 🔒   Final Phase: teaching_start
2025-07-22 20:36:56,410 - DEBUG - [main.py:10758] - 🔒   Diagnostic Complete: True
2025-07-22 20:36:56,410 - DEBUG - [main.py:10759] - 🔒   Assigned Level: 6
2025-07-22 20:36:56,411 - INFO - [main.py:10832] - [********-c489-4a4b-803c-3a82c333c82d] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 20:36:56,411 - INFO - [main.py:10866] - [********-c489-4a4b-803c-3a82c333c82d] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 20:36:56,411 - INFO - [main.py:10874] - [********-c489-4a4b-803c-3a82c333c82d] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 20:36:56,412 - INFO - [main.py:10879] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 20:36:56,413 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:36:56,413 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:36:56,414 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:36:56,414 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:36:56,414 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:36:56,414 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:36:56,415 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:36:56,416 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:36:56,416 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:36:56,417 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:36:56,417 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:36:56,417 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:36:56,417 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:36:56,418 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:36:56,418 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:36:56,418 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:36:56,418 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:36:56,419 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:36:56,419 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:36:56,419 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:36:56,419 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:36:56,420 - INFO - [main.py:10888] - [********-c489-4a4b-803c-3a82c333c82d] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 20:36:56,420 - INFO - [intelligent_guardrails.py:130] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:36:56,420 - WARNING - [intelligent_guardrails.py:146] - [********-c489-4a4b-803c-3a82c333c82d] 🚨 QUIZ REQUEST BLOCKED: Teaching incomplete
2025-07-22 20:36:56,421 - INFO - [intelligent_guardrails.py:451] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ BLOCKING VIOLATIONS: Preventing quiz transition
2025-07-22 20:36:56,421 - INFO - [intelligent_guardrails.py:464] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ BACKEND INSTRUCTION (INTERNAL ONLY): Continue teaching until all completion criteria are met
2025-07-22 20:36:56,421 - INFO - [intelligent_guardrails.py:512] - [********-c489-4a4b-803c-3a82c333c82d] 📝 STUDENT-FRIENDLY CONTINUATION: Generated encouraging message instead of backend instruction
2025-07-22 20:36:56,421 - INFO - [intelligent_guardrails.py:183] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ GUARDRAILS RESULT: Valid=False, Violations=1
2025-07-22 20:36:56,422 - INFO - [main.py:10950] - [********-c489-4a4b-803c-3a82c333c82d] 🛡️ GUARDRAILS VALIDATION: Valid=False, Violations=1
2025-07-22 20:36:56,422 - WARNING - [main.py:10954] - [********-c489-4a4b-803c-3a82c333c82d] 🚨 GUARDRAIL VIOLATION: teaching_incomplete_quiz_block - Quiz transition blocked - teaching not complete (Severity: GuardrailSeverity.BLOCKING)
2025-07-22 20:36:56,422 - INFO - [main.py:10975] - [********-c489-4a4b-803c-3a82c333c82d] ✨ GUARDRAILS ENHANCED RESPONSE: Applied pedagogical improvements
2025-07-22 20:36:56,423 - INFO - [main.py:10978] - [********-c489-4a4b-803c-3a82c333c82d] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 20:36:56,423 - DEBUG - [main.py:11035] - 🎯 RESPONSE READY:
2025-07-22 20:36:56,423 - DEBUG - [main.py:11036] - 🎯   Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:36:56,424 - DEBUG - [main.py:11037] - 🎯   Phase: smart_diagnostic_q5 → teaching_start
2025-07-22 20:36:56,424 - DEBUG - [main.py:11038] - 🎯   Content: Excellent! You've perfectly described how AI acts ...
2025-07-22 20:36:56,424 - DEBUG - [main.py:11039] - 🎯   Request ID: ********-c489-4a4b-803c-3a82c333c82d
2025-07-22 20:36:56,425 - INFO - [main.py:11045] - [********-c489-4a4b-803c-3a82c333c82d] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 20:36:56,425 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,425 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,425 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,426 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:56,426 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:56,426 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:56,426 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:56,426 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,426 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:36:56,427 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,427 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,427 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,427 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,427 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,427 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:36:56,428 - WARNING - [main.py:888] - High response time detected: 5.91s for enhance_content_api
2025-07-22 20:37:15,725 - INFO - [main.py:7143] - Incoming request: {"request_id": "89fecb33-d699-496a-8fca-1ca49858f4f8", "timestamp": "2025-07-22T19:37:15.725086+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "lets begin", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5", "chat_history": [{"role": "user", "content": "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", "timestamp": "2025-07-22T19:35:04.917Z"}, {"role": "assistant", "content": "That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medical images like X-rays and scans. It's like having a super-powered magnifying glass for doctors, helping them detect even the smallest signs of a **disease**. This is a key part of **diagnosis**, which means figuring out what's wrong.\n\nYou're doing great! Let's try another question.\n\n**Question 3 of 5 (Level 4 Application Insight):** Besides looking at pictures, can you think of another way AI might help doctors when someone is feeling unwell?", "timestamp": "2025-07-22T19:35:11.242Z"}, {"role": "user", "content": "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", "timestamp": "2025-07-22T19:35:38.820Z"}, {"role": "assistant", "content": "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.\n\nYou're building a great understanding! Here's your next question:\n\n**Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", "timestamp": "2025-07-22T19:35:45.259Z"}, {"role": "user", "content": "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.  You're building a great understanding! Here's your next question:  **Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", "timestamp": "2025-07-22T19:36:11.198Z"}, {"role": "assistant", "content": "You've got it exactly right, Andrea! You're thinking ahead about how AI can be used in **drug discovery**. That's a very advanced concept, and you've explained it so well! AI can sift through massive amounts of scientific data to identify potential new medicines much faster than humans can. It can also help predict how a new drug might work in the body, which is incredibly helpful in developing treatments for diseases.\n\nWe're almost at the end of our diagnostic questions!\n\n**Question 5 of 5 (Level 6 Synthesis):** Putting it all together, how does AI's ability to help with diagnosis and potentially drug discovery benefit people who are sick?", "timestamp": "2025-07-22T19:36:17.387Z"}, {"role": "user", "content": "I think AI can be like a super-fast scientist trying to find new medicines! It can look at millions of tiny things called molecules and see which ones might be good for fighting a specific sickness, like a detective looking for a special key to unlock a door.[1][2][3] It can also help scientists guess which molecules will work best by predicting how they'll behave in the body.[1][4] This way, they don't have to try out millions of different things in a lab, which takes a very, very long time. AI can help them find the best medicine much faster, so people can get better sooner!", "timestamp": "2025-07-22T19:36:49.990Z"}, {"role": "assistant", "content": "Excellent! You've perfectly described how AI acts like a super-fast scientist in **drug discovery**. You're right, it can examine countless molecules, like a detective searching for a key, and predict which ones are most likely to be effective against a **disease**. This speeds up the process immensely, meaning new treatments can reach people much quicker.\n\nExcellent! You've completed all 5 questions of the Smart Diagnostic assessment! \ud83c\udf89\n**Diagnostic Results:** \u2705 Questions Completed: 5/5 \ud83d\udcca Recommended Teaching Level: 6\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on AI in Healthcare! \ud83d\ude80\n\nGreat questions, Andrea! Let's explore more about AI in Healthcare to make sure you really understand it.\n\nGreat questions, Student! Let's explore more about AI in Healthcare to make sure you really understand it.", "timestamp": "2025-07-22T19:36:56.456Z"}]}}
2025-07-22 20:37:15,726 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 20:37:15,726 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gj0qnEDDwAwV0F7rNkWh0AcRM_Vqch0hfz0_Gre-6PN9Ghpx_KAU3ISJU2J4mFlUyqTNjIYwn4CYbeHQcnCVC9TG8q0ADK-fDuwpAjd43B7b7hQvBLumyroLvpp-ck-bA7FeUD7-cawX6D5ltTIXZUOV-kLJ4yceZzYjPuKze7oeqPzjAiYvgX8J2FSG4MBaovb1BjlR-AoNq_jsjcJryDf3ndrLxmRY2_r86KIuvX3nbm29snqIUaWed6ukFgi35NpPRt1QnbaLdGL1kNzv-epB_8HoN48szPVY5NXOb2i0peCGEc2w2C_AFWqHIFviUy3v1mhg7ZLNsHKd5xFJNw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '5643', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 20:37:15,726 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 89fecb33-d699-496a-8fca-1ca49858f4f8
2025-07-22 20:37:15,727 - INFO - [auth_decorator.py:74] - [89fecb33-d699-496a-8fca-1ca49858f4f8][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 20:37:15,727 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 20:37:15,727 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 20:37:15,728 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 20:37:15,728 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 20:37:15,728 - INFO - [auth_decorator.py:95] - [89fecb33-d699-496a-8fca-1ca49858f4f8][require_auth] Development mode detected - bypassing authentication
2025-07-22 20:37:15,728 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 20:37:15,729 - INFO - [auth_decorator.py:121] - [89fecb33-d699-496a-8fca-1ca49858f4f8][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 20:37:16,016 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:37:16,017 - INFO - [auth_decorator.py:164] - [89fecb33-d699-496a-8fca-1ca49858f4f8][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:37:16,018 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 20:37:16,022 - INFO - [main.py:7327] -
================================================================================
2025-07-22 20:37:16,022 - WARNING - [main.py:7328] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 20:37:16,023 - WARNING - [main.py:7329] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 20:37:16,024 - DEBUG - [main.py:7337] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Console output error: I/O operation on closed file.
2025-07-22 20:37:16,026 - INFO - [main.py:8179] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"lets begin","country":"Nigeria","curriculum":"National Curriculum","grade":"primary-5","subject":"Artificial Intelligence","session_id":"fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5","chat_history":[{"role":"user","content":"The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like ...
2025-07-22 20:37:16,027 - INFO - [main.py:8181] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': 'lets begin', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', 'chat_history': [{'role': 'user', 'content': "The first thing that pops into my head is AI helping doctors look at pictures, like X-rays or scans of insides. It's like AI has super-eyes that can spot tiny things that might be wrong, like a little boo-boo hidden inside, that a regular doctor might miss. It helps them figure out what's making someone sick really quickly!", 'timestamp': '2025-07-22T19:35:04.917Z'}, {'role': 'assistant', 'content': "That's a brilliant example, Andrea! You're absolutely right, AI is incredibly good at analyzing medical images like X-rays and scans. It's like having a super-powered magnifying glass for doctors, helping them detect even the smallest signs of a **disease**. This is a key part of **diagnosis**, which means figuring out what's wrong.\n\nYou're doing great! Let's try another question.\n\n**Question 3 of 5 (Level 4 Application Insight):** Besides looking at pictures, can you think of another way AI might help doctors when someone is feeling unwell?", 'timestamp': '2025-07-22T19:35:11.242Z'}, {'role': 'user', 'content': "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", 'timestamp': '2025-07-22T19:35:38.820Z'}, {'role': 'assistant', 'content': "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.\n\nYou're building a great understanding! Here's your next question:\n\n**Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", 'timestamp': '2025-07-22T19:35:45.259Z'}, {'role': 'user', 'content': "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.  You're building a great understanding! Here's your next question:  **Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", 'timestamp': '2025-07-22T19:36:11.198Z'}, {'role': 'assistant', 'content': "You've got it exactly right, Andrea! You're thinking ahead about how AI can be used in **drug discovery**. That's a very advanced concept, and you've explained it so well! AI can sift through massive amounts of scientific data to identify potential new medicines much faster than humans can. It can also help predict how a new drug might work in the body, which is incredibly helpful in developing treatments for diseases.\n\nWe're almost at the end of our diagnostic questions!\n\n**Question 5 of 5 (Level 6 Synthesis):** Putting it all together, how does AI's ability to help with diagnosis and potentially drug discovery benefit people who are sick?", 'timestamp': '2025-07-22T19:36:17.387Z'}, {'role': 'user', 'content': "I think AI can be like a super-fast scientist trying to find new medicines! It can look at millions of tiny things called molecules and see which ones might be good for fighting a specific sickness, like a detective looking for a special key to unlock a door.[1][2][3] It can also help scientists guess which molecules will work best by predicting how they'll behave in the body.[1][4] This way, they don't have to try out millions of different things in a lab, which takes a very, very long time. AI can help them find the best medicine much faster, so people can get better sooner!", 'timestamp': '2025-07-22T19:36:49.990Z'}, {'role': 'assistant', 'content': "Excellent! You've perfectly described how AI acts like a super-fast scientist in **drug discovery**. You're right, it can examine countless molecules, like a detective searching for a key, and predict which ones are most likely to be effective against a **disease**. This speeds up the process immensely, meaning new treatments can reach people much quicker.\n\nExcellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉\n**Diagnostic Results:** ✅ Questions Completed: 5/5 📊 Recommended Teaching Level: 6\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on AI in Healthcare! 🚀\n\nGreat questions, Andrea! Let's explore more about AI in Healthcare to make sure you really understand it.\n\nGreat questions, Student! Let's explore more about AI in Healthcare to make sure you really understand it.", 'timestamp': '2025-07-22T19:36:56.456Z'}]}
2025-07-22 20:37:16,028 - INFO - [main.py:8183] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍  - Session ID from payload: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:37:16,028 - INFO - [main.py:8184] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 20:37:16,029 - INFO - [main.py:8185] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 20:37:16,029 - DEBUG - [main.py:8223] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:37:16,030 - INFO - [main.py:8224] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:37:16,031 - INFO - [main.py:8264] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Level not provided, determined from grade 'primary-5': 5
2025-07-22 20:37:16,360 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 20:37:16,361 - INFO - [main.py:8281] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 20:37:16,361 - INFO - [main.py:8282] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 20:37:16,362 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 20:37:16,824 - INFO - [main.py:8312] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 20:37:16,825 - INFO - [main.py:8375] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 20:37:16,825 - INFO - [main.py:8500] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ Successfully retrieved lesson from primary path
2025-07-22 20:37:16,825 - INFO - [main.py:8511] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 20:37:16,826 - INFO - [main.py:8550] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 20:37:16,826 - INFO - [main.py:4456] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 20:37:17,166 - INFO - [main.py:4522] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 20:37:17,166 - INFO - [main.py:4522] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 20:37:17,166 - INFO - [main.py:4522] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 20:37:17,166 - INFO - [main.py:4522] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 20:37:17,167 - INFO - [main.py:4522] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 20:37:17,167 - INFO - [main.py:4591] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 20:37:17,167 - DEBUG - [main.py:4605] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 20:37:17,167 - DEBUG - [main.py:4608] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 20:37:17,168 - DEBUG - [main.py:4609] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 20:37:17,168 - DEBUG - [main.py:4610] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 20:37:17,168 - INFO - [main.py:4614] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Calling Gemini API for module inference...
2025-07-22 20:37:17,704 - INFO - [main.py:4624] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Gemini API call completed in 0.54s. Raw response: 'ai_tools_and_applications'
2025-07-22 20:37:17,705 - DEBUG - [main.py:4646] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 20:37:17,706 - INFO - [main.py:4651] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 20:37:17,706 - INFO - [main.py:8584] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 20:37:17,707 - INFO - [main.py:8621] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 20:37:17,972 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 20:37:18,499 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 20:37:18,500 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:37:18,501 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 20:37:18,501 - DEBUG - [main.py:8677] - 🔍   - Current Phase: teaching
2025-07-22 20:37:18,502 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 6
2025-07-22 20:37:18,502 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 20:37:18,503 - WARNING - [main.py:8685] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 SESSION STATE DEBUG:
2025-07-22 20:37:18,503 - WARNING - [main.py:8686] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍   - Session exists: True
2025-07-22 20:37:18,503 - WARNING - [main.py:8687] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍   - Current phase: teaching
2025-07-22 20:37:18,504 - WARNING - [main.py:8688] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'new_phase', 'quiz_performance', 'current_probing_level_number', 'is_first_encounter_for_module', 'current_phase', 'current_quiz_question', 'teaching_interactions', 'last_modified', 'latest_assessed_level_for_module', 'diagnostic_complete', 'quiz_answers', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 20:37:18,505 - DEBUG - [main.py:8706] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 20:37:18,505 - DEBUG - [main.py:8707] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Retrieved Phase: 'teaching'
2025-07-22 20:37:18,506 - DEBUG - [main.py:8708] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Diagnostic Completed: True
2025-07-22 20:37:18,507 - DEBUG - [main.py:8709] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Assigned Level: 6
2025-07-22 20:37:18,507 - WARNING - [main.py:8710] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔒 STATE PROTECTION: phase='teaching', diagnostic_done=True, level=6
2025-07-22 20:37:18,508 - INFO - [main.py:8739] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ State is valid - no correction needed
2025-07-22 20:37:18,509 - INFO - [main.py:8740] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ State validation passed: teaching
2025-07-22 20:37:18,510 - WARNING - [main.py:8789] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ⚠️ No latest assessed level in profile, using session state: 6
2025-07-22 20:37:18,510 - INFO - [main.py:8791] - [89fecb33-d699-496a-8fca-1ca49858f4f8]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 20:37:18,511 - INFO - [main.py:8792] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   assigned_level_for_teaching (session): 6
2025-07-22 20:37:18,512 - INFO - [main.py:8793] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   latest_assessed_level (profile): None
2025-07-22 20:37:18,512 - INFO - [main.py:8794] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   teaching_level_for_returning_student: 6
2025-07-22 20:37:18,513 - INFO - [main.py:8795] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   has_completed_diagnostic_before: True
2025-07-22 20:37:18,514 - INFO - [main.py:8796] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   is_first_encounter_for_module: False
2025-07-22 20:37:18,514 - INFO - [main.py:8806] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ RETURNING STUDENT: Using existing diagnostic state - diagnostic_completed=True
2025-07-22 20:37:18,515 - INFO - [main.py:8809] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 PHASE INVESTIGATION:
2025-07-22 20:37:18,515 - INFO - [main.py:8810] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Retrieved from Firestore: 'teaching'
2025-07-22 20:37:18,516 - INFO - [main.py:8811] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 20:37:18,516 - INFO - [main.py:8812] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Is first encounter: False
2025-07-22 20:37:18,517 - INFO - [main.py:8813] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Diagnostic completed: True
2025-07-22 20:37:18,517 - WARNING - [main.py:8821] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ⚠️ Invalid phase format: 'teaching', using default
2025-07-22 20:37:18,518 - INFO - [main.py:8833] - [89fecb33-d699-496a-8fca-1ca49858f4f8] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 20:37:18,518 - INFO - [main.py:8835] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Final phase for AI logic: smart_diagnostic_start
2025-07-22 20:37:18,519 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 20:37:18,519 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 20:37:18,520 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 20:37:18,520 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 20:37:18,521 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 20:37:18,521 - INFO - [main.py:6144] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Diagnostic context validation passed
2025-07-22 20:37:18,522 - INFO - [main.py:9020] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Phase determination: smart_diagnostic_start -> teaching
2025-07-22 20:37:18,522 - INFO - [main.py:9030] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Skipping diagnostic context enhancement for non-diagnostic phase: teaching
2025-07-22 20:37:18,523 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'teaching'
2025-07-22 20:37:18,523 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'teaching'
2025-07-22 20:37:18,524 - INFO - [main.py:9042] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Robust context prepared successfully. Phase: teaching
2025-07-22 20:37:18,524 - DEBUG - [main.py:9043] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 20:37:18,525 - INFO - [main.py:9285] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯 UNIFIED STATE MACHINE: Current phase = teaching
2025-07-22 20:37:18,526 - INFO - [main.py:9286] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯 UNIFIED STATE MACHINE: Processing user query: lets begin...
2025-07-22 20:37:18,526 - INFO - [main.py:9297] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯 ROUTING: Teaching phase handler
2025-07-22 20:37:18,528 - INFO - [main.py:7513] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📚 TEACHING PHASE HANDLER: Processing teaching content
2025-07-22 20:37:18,528 - INFO - [main.py:7545] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🤖 AI INSTRUCTOR: Continuing teaching phase
2025-07-22 20:37:18,529 - INFO - [main.py:11193] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 💰 COST-OPTIMIZED: Using chat session approach for session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:37:18,530 - DEBUG - [main.py:11202] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Console output error: I/O operation on closed file.
2025-07-22 20:37:18,530 - INFO - [main.py:11235] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📤 Sending message to existing session: lets begin...
2025-07-22 20:37:18,531 - INFO - [main.py:5965] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 💰 Sending message to existing session (NO API CALL)
2025-07-22 20:37:19,915 - INFO - [main.py:5970] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ Response received from session (NO API CALL COST)
2025-07-22 20:37:19,918 - INFO - [main.py:11242] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📥 Received response from session: Now let's begin our exciting lesson on AI in Healthcare! 🚀

Welcome, Andrea! We're going to dive dee...
2025-07-22 20:37:19,919 - INFO - [main.py:11437] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📋 LOGIC-BASED STATE EXTRACTION: No text patterns, relying on structured data only
2025-07-22 20:37:19,919 - INFO - [main.py:7573] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📊 TEACHING PROGRESS: 1 interactions
2025-07-22 20:37:19,919 - INFO - [main.py:7574] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📊 OBJECTIVES COVERAGE: 0.0%
2025-07-22 20:37:19,920 - INFO - [main.py:9371] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯 UNIFIED STATE MACHINE: Applying state updates and validating transitions
2025-07-22 20:37:19,920 - INFO - [main.py:5710] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔄 PHASE TRANSITION: teaching → teaching
2025-07-22 20:37:19,921 - INFO - [phase_transition_integrity.py:153] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 PHASE TRANSITION VALIDATION: teaching → teaching
2025-07-22 20:37:19,922 - WARNING - [phase_transition_integrity.py:191] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔧 DATA RECOVERY APPLIED: Recovered assigned_level_for_teaching as 5
2025-07-22 20:37:19,922 - ERROR - [phase_transition_integrity.py:704] - 🔍 STATE CONSISTENCY ERRORS DETECTED:
2025-07-22 20:37:19,923 - ERROR - [phase_transition_integrity.py:706] - 🔍   1. Cannot enter teaching phase without completing diagnostic
2025-07-22 20:37:19,923 - ERROR - [phase_transition_integrity.py:707] - 🔍 SESSION DATA DEBUG:
2025-07-22 20:37:19,924 - ERROR - [phase_transition_integrity.py:708] - 🔍   - diagnostic_complete: False
2025-07-22 20:37:19,924 - ERROR - [phase_transition_integrity.py:709] - 🔍   - assigned_level_for_teaching: None
2025-07-22 20:37:19,924 - ERROR - [phase_transition_integrity.py:710] - 🔍   - teaching_complete: False
2025-07-22 20:37:19,925 - ERROR - [phase_transition_integrity.py:711] - 🔍   - teaching_interactions: 1
2025-07-22 20:37:19,925 - ERROR - [phase_transition_integrity.py:712] - 🔍   - from_phase: teaching, to_phase: teaching
2025-07-22 20:37:19,925 - ERROR - [phase_transition_integrity.py:211] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ❌ STATE INCONSISTENCY: State consistency check: 1 errors found
2025-07-22 20:37:19,927 - ERROR - [main.py:5733] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ❌ INVALID TRANSITION: teaching → teaching, using quiz_initiate
2025-07-22 20:37:19,927 - INFO - [main.py:9387] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔒 UNIFIED PATHWAY ENFORCED: teaching → quiz_initiate
2025-07-22 20:37:19,927 - INFO - [main.py:9388] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔒 Alternative paths are not permitted in the unified lesson flow
2025-07-22 20:37:20,741 - INFO - [main.py:9402] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ UNIFIED STATE MACHINE: Session state updated with phase quiz_initiate
2025-07-22 20:37:20,741 - INFO - [main.py:9412] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ APPLYING INTELLIGENT GUARDRAILS
2025-07-22 20:37:20,742 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:37:20,742 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:37:20,742 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:37:20,742 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:37:20,743 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:37:20,743 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:37:20,743 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:37:20,743 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:37:20,744 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:37:20,744 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:37:20,744 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:37:20,744 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:37:20,745 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:37:20,745 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:37:20,746 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:37:20,746 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:37:20,747 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:37:20,747 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:37:20,748 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:37:20,748 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:37:20,749 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:37:20,749 - INFO - [intelligent_guardrails.py:130] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:37:20,750 - INFO - [intelligent_guardrails.py:183] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:37:20,750 - INFO - [intelligent_guardrails.py:675] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ GUARDRAILS APPLIED: Valid=True, Violations=0, Teaching Complete=False
2025-07-22 20:37:20,750 - INFO - [main.py:9544] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ GUARDRAILS APPLIED: Valid=True, Enhanced=True
2025-07-22 20:37:20,751 - WARNING - [main.py:9593] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🤖 AI RESPONSE RECEIVED:
2025-07-22 20:37:20,751 - WARNING - [main.py:9594] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🤖   - Content length: 1547 chars
2025-07-22 20:37:20,751 - WARNING - [main.py:9595] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🤖   - State updates: {'new_phase': 'teaching', 'teaching_interactions': 1, 'unified_pathway_enforced': True}
2025-07-22 20:37:20,751 - WARNING - [main.py:9596] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🤖   - Raw state block: None...
2025-07-22 20:37:20,752 - INFO - [main.py:9604] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎓 APPLYING TEACHING PHASE ENHANCEMENT
2025-07-22 20:37:20,758 - WARNING - [teaching_phase_enhancement.py:445] - No active teaching session found for fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:37:20,759 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:37:20,759 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:37:20,759 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:37:20,759 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:37:20,760 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:37:20,760 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:37:20,760 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:37:20,760 - INFO - [main.py:9695] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯 ADAPTIVE REQUIREMENTS: 10 interactions required
2025-07-22 20:37:20,760 - INFO - [main.py:9696] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯   Based on: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:37:20,760 - ERROR - [main.py:10039] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ❌ Teaching phase analysis error: No active teaching session
2025-07-22 20:37:20,761 - DEBUG - [main.py:10129] - 🤖 AI RESPONSE PROCESSED:
2025-07-22 20:37:20,761 - DEBUG - [main.py:10130] - 🤖   Content: Now let's begin our exciting lesson on AI in Healthcare! 🚀

Welcome, Andrea! We're going to dive dee...
2025-07-22 20:37:20,761 - DEBUG - [main.py:10131] - 🤖   State: {'new_phase': 'teaching', 'teaching_interactions': 1, 'unified_pathway_enforced': True}
2025-07-22 20:37:20,761 - INFO - [main.py:10157] - [89fecb33-d699-496a-8fca-1ca49858f4f8] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
2025-07-22 20:37:20,762 - INFO - [main.py:10158] - [89fecb33-d699-496a-8fca-1ca49858f4f8] CURRENT PHASE DETERMINATION: AI=teaching, Session=teaching, Final=teaching
2025-07-22 20:37:21,031 - WARNING - [main.py:10260] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 STATE UPDATE VALIDATION: current_phase='teaching', new_phase='teaching'
2025-07-22 20:37:21,032 - INFO - [main.py:6365] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI state update validation passed: teaching → teaching
2025-07-22 20:37:21,033 - WARNING - [main.py:10269] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 20:37:21,033 - WARNING - [main.py:10290] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   PHASE MAINTAINED: teaching
2025-07-22 20:37:21,034 - INFO - [main.py:10319] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: teaching
2025-07-22 20:37:21,034 - INFO - [main.py:10324] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 20:37:21,034 - INFO - [phase_transition_integrity.py:315] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔄 APPLYING TRANSITION WITH INTEGRITY: teaching → teaching
2025-07-22 20:37:21,035 - INFO - [phase_transition_integrity.py:278] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📸 DATA SNAPSHOT CREATED: Session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase teaching
2025-07-22 20:37:21,036 - INFO - [phase_transition_integrity.py:153] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 PHASE TRANSITION VALIDATION: teaching → teaching
2025-07-22 20:37:21,047 - INFO - [phase_transition_integrity.py:220] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ TRANSITION VALIDATED: teaching → teaching
2025-07-22 20:37:21,048 - INFO - [phase_transition_integrity.py:345] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ TRANSITION APPLIED: teaching → teaching
2025-07-22 20:37:21,048 - DEBUG - [phase_transition_integrity.py:832] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 20:37:21,048 - DEBUG - [phase_transition_integrity.py:865] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 📝 TRANSITION RECORDED: teaching → teaching (valid)
2025-07-22 20:37:21,048 - INFO - [phase_transition_integrity.py:391] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching
2025-07-22 20:37:21,049 - WARNING - [main.py:10366] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔧 STATE MACHINE: Invalid transition corrected to quiz_initiate
2025-07-22 20:37:21,049 - INFO - [main.py:10397] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ TRANSITION VALIDATED: teaching → quiz_initiate
2025-07-22 20:37:21,050 - WARNING - [main.py:10402] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 20:37:21,050 - WARNING - [main.py:10403] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍   1. Input phase: 'teaching'
2025-07-22 20:37:21,051 - WARNING - [main.py:10404] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 20:37:21,051 - WARNING - [main.py:10405] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍   3. Proposed phase: 'teaching'
2025-07-22 20:37:21,051 - WARNING - [main.py:10406] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍   4. Integrity validation result: 'valid'
2025-07-22 20:37:21,052 - WARNING - [main.py:10407] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍   5. Final phase to save: 'quiz_initiate'
2025-07-22 20:37:21,052 - WARNING - [main.py:10410] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 💾 FINAL STATE APPLICATION:
2025-07-22 20:37:21,053 - WARNING - [main.py:10411] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 💾   - Current phase input: 'teaching'
2025-07-22 20:37:21,053 - WARNING - [main.py:10412] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 💾   - Validated state updates: 25 fields
2025-07-22 20:37:21,053 - WARNING - [main.py:10413] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 💾   - Final phase to save: 'quiz_initiate'
2025-07-22 20:37:21,054 - WARNING - [main.py:10414] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 💾   - Phase change: True
2025-07-22 20:37:21,054 - WARNING - [main.py:10415] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 💾   - Integrity applied: True
2025-07-22 20:37:21,054 - INFO - [main.py:6397] - [89fecb33-d699-496a-8fca-1ca49858f4f8] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 20:37:21,055 - INFO - [main.py:6398] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Phase transition: teaching -> teaching
2025-07-22 20:37:21,055 - INFO - [main.py:6399] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Current level: 6
2025-07-22 20:37:21,055 - INFO - [main.py:6400] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Question index: 0
2025-07-22 20:37:21,055 - INFO - [main.py:6401] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   First encounter: False
2025-07-22 20:37:21,055 - INFO - [main.py:6406] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Answers collected: 0
2025-07-22 20:37:21,056 - INFO - [main.py:6407] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Levels failed: 0
2025-07-22 20:37:21,056 - INFO - [main.py:6365] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI state update validation passed: teaching → teaching
2025-07-22 20:37:21,056 - INFO - [main.py:6411] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   State update valid: True
2025-07-22 20:37:21,056 - INFO - [main.py:6418] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Diagnostic complete: False
2025-07-22 20:37:21,057 - INFO - [main.py:6420] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   Assigned level: 6
2025-07-22 20:37:21,057 - WARNING - [main.py:10433] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 20:37:21,058 - INFO - [main.py:10442] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 DEBUG state_updates_from_ai teaching_interactions: 1
2025-07-22 20:37:21,058 - INFO - [main.py:10443] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 20:37:21,059 - WARNING - [main.py:10446] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ INTERACTION COUNT PROTECTION: AI tried to set 1, backend preserving 0
2025-07-22 20:37:21,059 - WARNING - [main.py:10447] - [89fecb33-d699-496a-8fca-1ca49858f4f8]   This prevents teaching interaction count resets and infinite loops
2025-07-22 20:37:21,575 - WARNING - [main.py:10493] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 20:37:21,575 - WARNING - [main.py:10494] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅   - Phase: quiz_initiate
2025-07-22 20:37:21,576 - WARNING - [main.py:10495] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅   - Probing Level: 6
2025-07-22 20:37:21,576 - WARNING - [main.py:10496] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅   - Question Index: 0
2025-07-22 20:37:21,577 - WARNING - [main.py:10497] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅   - Diagnostic Complete: True
2025-07-22 20:37:21,577 - WARNING - [main.py:10504] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅   - Quiz Questions Saved: 0
2025-07-22 20:37:21,577 - WARNING - [main.py:10505] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅   - Quiz Answers Saved: 0
2025-07-22 20:37:21,578 - WARNING - [main.py:10506] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅   - Quiz Started: False
2025-07-22 20:37:21,578 - DEBUG - [main.py:10555] - 🔥 STATE SAVED - Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: quiz_initiate
2025-07-22 20:37:21,578 - DEBUG - [main.py:10556] - 🔥 QUIZ DATA - Questions: 0, Answers: 0
2025-07-22 20:37:22,424 - DEBUG - [main.py:10614] - ✅ SESSION UPDATED - ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: quiz_initiate
2025-07-22 20:37:22,425 - DEBUG - [main.py:10615] - ✅ INTERACTION LOGGED - Phase: teaching → quiz_initiate
2025-07-22 20:37:22,425 - INFO - [main.py:10621] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ Updated existing session document: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:37:22,425 - INFO - [main.py:18535] - [89fecb33-d699-496a-8fca-1ca49858f4f8] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 20:37:22,426 - DEBUG - [main.py:4943] - [89fecb33-d699-496a-8fca-1ca49858f4f8] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 20:37:22,426 - DEBUG - [main.py:10708] - [89fecb33-d699-496a-8fca-1ca49858f4f8] No final assessment data found in AI response
2025-07-22 20:37:22,427 - DEBUG - [main.py:10731] - [89fecb33-d699-496a-8fca-1ca49858f4f8] No lesson completion detected (Phase: teaching, Complete: False)
2025-07-22 20:37:22,427 - WARNING - [main.py:10750] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🔧 STATE CORRECTION: Forcing 'diagnostic_completed_this_session' to True because phase is 'teaching'.
2025-07-22 20:37:22,428 - DEBUG - [main.py:10755] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 20:37:22,429 - DEBUG - [main.py:10756] - 🔒   Current Phase: teaching
2025-07-22 20:37:22,430 - DEBUG - [main.py:10757] - 🔒   Final Phase: teaching
2025-07-22 20:37:22,430 - DEBUG - [main.py:10758] - 🔒   Diagnostic Complete: True
2025-07-22 20:37:22,431 - DEBUG - [main.py:10759] - 🔒   Assigned Level: 6
2025-07-22 20:37:22,432 - INFO - [main.py:10832] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 20:37:22,432 - INFO - [main.py:10866] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 20:37:22,433 - INFO - [main.py:10874] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 20:37:22,433 - INFO - [main.py:10879] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 20:37:22,434 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:37:22,434 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:37:22,435 - INFO - [teaching_rules.py:168] -    Teaching Level: 5 → Multiplier: 1.0
2025-07-22 20:37:22,435 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:37:22,435 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 10
2025-07-22 20:37:22,436 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.75
2025-07-22 20:37:22,436 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:37:22,436 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:37:22,437 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:37:22,437 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:37:22,437 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:37:22,437 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:37:22,438 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 5 (×1.0)
2025-07-22 20:37:22,438 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:37:22,438 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.0 × 1.0 = 10
2025-07-22 20:37:22,439 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:37:22,439 - INFO - [teaching_rules.py:405] -       Interactions: 0/10 (❌)
2025-07-22 20:37:22,439 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:37:22,439 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.75 (❌)
2025-07-22 20:37:22,440 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:37:22,440 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:37:22,440 - INFO - [main.py:10888] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 20:37:22,440 - INFO - [intelligent_guardrails.py:130] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:37:22,442 - INFO - [intelligent_guardrails.py:183] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=0
2025-07-22 20:37:22,442 - INFO - [main.py:10950] - [89fecb33-d699-496a-8fca-1ca49858f4f8] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=0
2025-07-22 20:37:22,443 - INFO - [main.py:10978] - [89fecb33-d699-496a-8fca-1ca49858f4f8] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 20:37:22,443 - DEBUG - [main.py:11035] - 🎯 RESPONSE READY:
2025-07-22 20:37:22,443 - DEBUG - [main.py:11036] - 🎯   Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:37:22,444 - DEBUG - [main.py:11037] - 🎯   Phase: teaching → teaching
2025-07-22 20:37:22,444 - DEBUG - [main.py:11038] - 🎯   Content: Now let's begin our exciting lesson on AI in Healt...
2025-07-22 20:37:22,444 - DEBUG - [main.py:11039] - 🎯   Request ID: 89fecb33-d699-496a-8fca-1ca49858f4f8
2025-07-22 20:37:22,445 - INFO - [main.py:11045] - [89fecb33-d699-496a-8fca-1ca49858f4f8] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 20:37:22,445 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,445 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,446 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,447 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:37:22,447 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:37:22,447 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:37:22,448 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:37:22,448 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,448 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:37:22,448 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,448 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,449 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,449 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,449 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,449 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:37:22,450 - WARNING - [main.py:888] - High response time detected: 6.43s for enhance_content_api
2025-07-22 20:38:35,583 - INFO - [main.py:7143] - Incoming request: {"request_id": "fc051ccf-fdc8-47b6-8878-9213d4491d5e", "timestamp": "2025-07-22T19:38:35.579295+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.10.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-089", "content_to_enhance": "Yes, that sounds super helpful! It's like having a doctor with super-sight who can also remember everything about every sickness ever! It makes me feel like people will get better much faster.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "primary-5", "subject": "Artificial Intelligence", "session_id": "fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5", "chat_history": [{"role": "user", "content": "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", "timestamp": "2025-07-22T19:35:38.820Z"}, {"role": "assistant", "content": "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.\n\nYou're building a great understanding! Here's your next question:\n\n**Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", "timestamp": "2025-07-22T19:35:45.259Z"}, {"role": "user", "content": "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.  You're building a great understanding! Here's your next question:  **Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", "timestamp": "2025-07-22T19:36:11.198Z"}, {"role": "assistant", "content": "You've got it exactly right, Andrea! You're thinking ahead about how AI can be used in **drug discovery**. That's a very advanced concept, and you've explained it so well! AI can sift through massive amounts of scientific data to identify potential new medicines much faster than humans can. It can also help predict how a new drug might work in the body, which is incredibly helpful in developing treatments for diseases.\n\nWe're almost at the end of our diagnostic questions!\n\n**Question 5 of 5 (Level 6 Synthesis):** Putting it all together, how does AI's ability to help with diagnosis and potentially drug discovery benefit people who are sick?", "timestamp": "2025-07-22T19:36:17.387Z"}, {"role": "user", "content": "I think AI can be like a super-fast scientist trying to find new medicines! It can look at millions of tiny things called molecules and see which ones might be good for fighting a specific sickness, like a detective looking for a special key to unlock a door.[1][2][3] It can also help scientists guess which molecules will work best by predicting how they'll behave in the body.[1][4] This way, they don't have to try out millions of different things in a lab, which takes a very, very long time. AI can help them find the best medicine much faster, so people can get better sooner!", "timestamp": "2025-07-22T19:36:49.990Z"}, {"role": "assistant", "content": "Excellent! You've perfectly described how AI acts like a super-fast scientist in **drug discovery**. You're right, it can examine countless molecules, like a detective searching for a key, and predict which ones are most likely to be effective against a **disease**. This speeds up the process immensely, meaning new treatments can reach people much quicker.\n\nExcellent! You've completed all 5 questions of the Smart Diagnostic assessment! \ud83c\udf89\n**Diagnostic Results:** \u2705 Questions Completed: 5/5 \ud83d\udcca Recommended Teaching Level: 6\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on AI in Healthcare! \ud83d\ude80\n\nGreat questions, Andrea! Let's explore more about AI in Healthcare to make sure you really understand it.\n\nGreat questions, Student! Let's explore more about AI in Healthcare to make sure you really understand it.", "timestamp": "2025-07-22T19:36:56.456Z"}, {"role": "user", "content": "lets begin", "timestamp": "2025-07-22T19:37:15.546Z"}, {"role": "assistant", "content": "Now let's begin our exciting lesson on AI in Healthcare! \ud83d\ude80\n\nWelcome, Andrea! We're going to dive deeper into how Artificial Intelligence, or AI, is like a superhero in the world of medicine. Remember how you mentioned AI being a \"super-smart helper\"? That's exactly right!\n\n**Introduction to Healthcare and AI:**\n\nThink of **Healthcare** as everything that keeps us healthy \u2013 doctors, nurses, hospitals, and even the medicines we take. **AI in Healthcare** means using smart computer programs that can learn and make decisions, just like you learn in school, to help doctors and patients.\n\n**AI in Disease Diagnosis:**\n\nOne of the most amazing ways AI helps is with **diagnosis**. This is how doctors figure out what **disease** a person has.\n\n*   **Super-powered Eyes for Doctors:** You were spot on when you talked about AI looking at X-rays and scans! AI programs can be trained to look at thousands of these images and spot tiny details that might signal a problem, like a very early sign of a disease. This helps doctors make a **diagnosis** faster and more accurately.\n*   **Listening to Your Body:** AI can also analyze symptoms you tell a doctor, like a cough or a fever, and compare them to a giant database of diseases. This helps the doctor narrow down the possibilities.\n\nSo, AI helps doctors by being an amazing assistant that can see, analyze, and remember a lot of information very quickly, leading to faster and more accurate diagnoses!\n\nWhat do you think about AI helping doctors find diseases so quickly? Does that sound helpful?", "timestamp": "2025-07-22T19:37:22.481Z"}]}}
2025-07-22 20:38:35,585 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/enhance-content
2025-07-22 20:38:35,585 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'Accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjZkZTQwZjA0ODgxYzZhMDE2MTFlYjI4NGE0Yzk1YTI1MWU5MTEyNTAiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gj0qnEDDwAwV0F7rNkWh0AcRM_Vqch0hfz0_Gre-6PN9Ghpx_KAU3ISJU2J4mFlUyqTNjIYwn4CYbeHQcnCVC9TG8q0ADK-fDuwpAjd43B7b7hQvBLumyroLvpp-ck-bA7FeUD7-cawX6D5ltTIXZUOV-kLJ4yceZzYjPuKze7oeqPzjAiYvgX8J2FSG4MBaovb1BjlR-AoNq_jsjcJryDf3ndrLxmRY2_r86KIuvX3nbm29snqIUaWed6ukFgi35NpPRt1QnbaLdGL1kNzv-epB_8HoN48szPVY5NXOb2i0peCGEc2w2C_AFWqHIFviUy3v1mhg7ZLNsHKd5xFJNw', 'User-Agent': 'axios/1.10.0', 'Content-Length': '6530', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
2025-07-22 20:38:35,586 - INFO - [auth_decorator.py:70] - 🔒 Request ID: fc051ccf-fdc8-47b6-8878-9213d4491d5e
2025-07-22 20:38:35,586 - INFO - [auth_decorator.py:74] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e][require_auth] Decorator invoked for path: /api/enhance-content
2025-07-22 20:38:35,586 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-22 20:38:35,586 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-22 20:38:35,586 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-22 20:38:35,587 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-22 20:38:35,587 - INFO - [auth_decorator.py:95] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e][require_auth] Development mode detected - bypassing authentication
2025-07-22 20:38:35,587 - INFO - [auth_decorator.py:118] - 🔒 DEVELOPMENT: Found student_id in request: andrea_ugono_33305
2025-07-22 20:38:35,587 - INFO - [auth_decorator.py:121] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e][require_auth] Using student_id from request: andrea_ugono_33305
2025-07-22 20:38:35,884 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:38:35,884 - INFO - [auth_decorator.py:164] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e][require_auth] Development auth: uid=andrea_ugono_33305, name=Andrea Ugono
2025-07-22 20:38:35,885 - DEBUG - [proactor_events.py:631] - Using proactor: IocpProactor
2025-07-22 20:38:35,885 - INFO - [main.py:7327] -
================================================================================
2025-07-22 20:38:35,886 - WARNING - [main.py:7328] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
2025-07-22 20:38:35,886 - WARNING - [main.py:7329] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🚀🚀🚀 ENHANCE-CONTENT START - UNIFIED STATE MACHINE ACTIVE 🚀🚀🚀
2025-07-22 20:38:35,886 - DEBUG - [main.py:7337] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Console output error: I/O operation on closed file.
2025-07-22 20:38:35,887 - INFO - [main.py:8179] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-089","content_to_enhance":"Yes, that sounds super helpful! It's like having a doctor with super-sight who can also remember everything about every sickness ever! It makes me feel like people will get better much faster.","country":"Nigeria","curriculum":"National Curriculum","grade":"primary-5","subject":"Artificial Intelligence","session_id":"fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5","chat_history":[{"role":"user","content":"I know! AI...
2025-07-22 20:38:35,889 - INFO - [main.py:8181] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-089', 'content_to_enhance': "Yes, that sounds super helpful! It's like having a doctor with super-sight who can also remember everything about every sickness ever! It makes me feel like people will get better much faster.", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'subject': 'Artificial Intelligence', 'session_id': 'fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', 'chat_history': [{'role': 'user', 'content': "I know! AI can also be like a super-organiser for all the information about someone who is sick. It can read all their past visits to the doctor, their allergies, and even what medicines they are taking. Then, it can put it all together in a way that's easy for the doctor to see. This helps the doctor understand everything about the person really quickly, so they can make the best decision about what to do next, rather than spending lots of time looking through piles of paper or computer files!", 'timestamp': '2025-07-22T19:35:38.820Z'}, {'role': 'assistant', 'content': "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.\n\nYou're building a great understanding! Here's your next question:\n\n**Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", 'timestamp': '2025-07-22T19:35:45.259Z'}, {'role': 'user', 'content': "That's an excellent point, Andrea! You've hit on another crucial aspect: AI's ability to manage and make sense of vast amounts of patient data. It's like a super-efficient personal assistant for doctors, organizing all the important health information to help them make quicker and more informed decisions. This really speeds up the process and makes sure nothing important is overlooked.  You're building a great understanding! Here's your next question:  **Question 4 of 5 (Level 5 Future Focus):** We've talked about AI helping doctors figure out what's wrong. What do you think AI could do to help create new medicines to fight diseases?", 'timestamp': '2025-07-22T19:36:11.198Z'}, {'role': 'assistant', 'content': "You've got it exactly right, Andrea! You're thinking ahead about how AI can be used in **drug discovery**. That's a very advanced concept, and you've explained it so well! AI can sift through massive amounts of scientific data to identify potential new medicines much faster than humans can. It can also help predict how a new drug might work in the body, which is incredibly helpful in developing treatments for diseases.\n\nWe're almost at the end of our diagnostic questions!\n\n**Question 5 of 5 (Level 6 Synthesis):** Putting it all together, how does AI's ability to help with diagnosis and potentially drug discovery benefit people who are sick?", 'timestamp': '2025-07-22T19:36:17.387Z'}, {'role': 'user', 'content': "I think AI can be like a super-fast scientist trying to find new medicines! It can look at millions of tiny things called molecules and see which ones might be good for fighting a specific sickness, like a detective looking for a special key to unlock a door.[1][2][3] It can also help scientists guess which molecules will work best by predicting how they'll behave in the body.[1][4] This way, they don't have to try out millions of different things in a lab, which takes a very, very long time. AI can help them find the best medicine much faster, so people can get better sooner!", 'timestamp': '2025-07-22T19:36:49.990Z'}, {'role': 'assistant', 'content': "Excellent! You've perfectly described how AI acts like a super-fast scientist in **drug discovery**. You're right, it can examine countless molecules, like a detective searching for a key, and predict which ones are most likely to be effective against a **disease**. This speeds up the process immensely, meaning new treatments can reach people much quicker.\n\nExcellent! You've completed all 5 questions of the Smart Diagnostic assessment! 🎉\n**Diagnostic Results:** ✅ Questions Completed: 5/5 📊 Recommended Teaching Level: 6\nBased on your responses, I've determined that we should start teaching at Level 6. Now let's begin our exciting lesson on AI in Healthcare! 🚀\n\nGreat questions, Andrea! Let's explore more about AI in Healthcare to make sure you really understand it.\n\nGreat questions, Student! Let's explore more about AI in Healthcare to make sure you really understand it.", 'timestamp': '2025-07-22T19:36:56.456Z'}, {'role': 'user', 'content': 'lets begin', 'timestamp': '2025-07-22T19:37:15.546Z'}, {'role': 'assistant', 'content': 'Now let\'s begin our exciting lesson on AI in Healthcare! 🚀\n\nWelcome, Andrea! We\'re going to dive deeper into how Artificial Intelligence, or AI, is like a superhero in the world of medicine. Remember how you mentioned AI being a "super-smart helper"? That\'s exactly right!\n\n**Introduction to Healthcare and AI:**\n\nThink of **Healthcare** as everything that keeps us healthy – doctors, nurses, hospitals, and even the medicines we take. **AI in Healthcare** means using smart computer programs that can learn and make decisions, just like you learn in school, to help doctors and patients.\n\n**AI in Disease Diagnosis:**\n\nOne of the most amazing ways AI helps is with **diagnosis**. This is how doctors figure out what **disease** a person has.\n\n*   **Super-powered Eyes for Doctors:** You were spot on when you talked about AI looking at X-rays and scans! AI programs can be trained to look at thousands of these images and spot tiny details that might signal a problem, like a very early sign of a disease. This helps doctors make a **diagnosis** faster and more accurately.\n*   **Listening to Your Body:** AI can also analyze symptoms you tell a doctor, like a cough or a fever, and compare them to a giant database of diseases. This helps the doctor narrow down the possibilities.\n\nSo, AI helps doctors by being an amazing assistant that can see, analyze, and remember a lot of information very quickly, leading to faster and more accurate diagnoses!\n\nWhat do you think about AI helping doctors find diseases so quickly? Does that sound helpful?', 'timestamp': '2025-07-22T19:37:22.481Z'}]}
2025-07-22 20:38:35,889 - INFO - [main.py:8183] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍  - Session ID from payload: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:38:35,889 - INFO - [main.py:8184] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍  - Student ID from payload: andrea_ugono_33305
2025-07-22 20:38:35,889 - INFO - [main.py:8185] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍  - Lesson Ref from payload: P5-AI-089
2025-07-22 20:38:35,890 - DEBUG - [main.py:8223] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:38:35,890 - INFO - [main.py:8224] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Parsed Params: student_id='andrea_ugono_33305', session_id='fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5', lesson_ref='P5-AI-089'
2025-07-22 20:38:35,890 - INFO - [main.py:8264] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Level not provided, determined from grade 'primary-5': 5
2025-07-22 20:38:37,425 - INFO - [main.py:6664] - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
2025-07-22 20:38:37,427 - INFO - [main.py:8281] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LESSON RETRIEVAL: Attempting to fetch P5-AI-089
2025-07-22 20:38:37,427 - INFO - [main.py:8282] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 📍 Parameters: country='Nigeria', curriculum='National Curriculum', grade='primary-5', level='5', subject='Artificial Intelligence'
2025-07-22 20:38:37,428 - DEBUG - [main.py:859] - Cache hit for fetch_lesson_data
2025-07-22 20:38:37,730 - INFO - [main.py:8312] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 Smart Diagnostic in progress, skipping lesson package
2025-07-22 20:38:37,731 - INFO - [main.py:8375] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 Smart Diagnostic must be completed before lesson package delivery
2025-07-22 20:38:37,731 - INFO - [main.py:8500] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ Successfully retrieved lesson from primary path
2025-07-22 20:38:37,732 - INFO - [main.py:8511] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ All required fields present after lesson content parsing and mapping
2025-07-22 20:38:37,732 - INFO - [main.py:8550] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
2025-07-22 20:38:37,732 - INFO - [main.py:4456] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI in Healthcare'.
2025-07-22 20:38:38,609 - INFO - [main.py:4522] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
2025-07-22 20:38:38,609 - INFO - [main.py:4522] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
2025-07-22 20:38:38,609 - INFO - [main.py:4522] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
2025-07-22 20:38:38,610 - INFO - [main.py:4522] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
2025-07-22 20:38:38,610 - INFO - [main.py:4522] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
2025-07-22 20:38:38,610 - INFO - [main.py:4591] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
2025-07-22 20:38:38,611 - DEBUG - [main.py:4605] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
2025-07-22 20:38:38,611 - DEBUG - [main.py:4608] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference Prompt (first 500 chars):
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction...
2025-07-22 20:38:38,612 - DEBUG - [main.py:4609] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI in Healthcare. Topic: AI in Healthcare. Learning Objectives: Understand how AI is used in disease diagnosis.; Explore the role of AI in drug discovery.. Key Concepts: used; disease; diagnosis; Explore; role; drug; discovery; Introduction; Healthcare; AI in Healthcare. Introduction: ...
2025-07-22 20:38:38,612 - DEBUG - [main.py:4610] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
2025-07-22 20:38:38,613 - INFO - [main.py:4614] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Calling Gemini API for module inference...
2025-07-22 20:38:39,015 - INFO - [main.py:4624] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Gemini API call completed in 0.40s. Raw response: 'ai_tools_and_applications'
2025-07-22 20:38:39,015 - DEBUG - [main.py:4646] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Cleaned slug: 'ai_tools_and_applications'
2025-07-22 20:38:39,015 - INFO - [main.py:4651] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
2025-07-22 20:38:39,015 - INFO - [main.py:8584] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Successfully inferred module ID via AI: ai_tools_and_applications
2025-07-22 20:38:39,016 - INFO - [main.py:8621] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
2025-07-22 20:38:39,314 - INFO - [main.py:4171] - No prior student performance document found for Topic: artificial_intelligence_primary-5_artificial_intelligence_ai_tools_and_applications
2025-07-22 20:38:40,200 - DEBUG - [main.py:8674] - 🔍 SESSION STATE RETRIEVAL:
2025-07-22 20:38:40,200 - DEBUG - [main.py:8675] - 🔍   - Session ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:38:40,200 - DEBUG - [main.py:8676] - 🔍   - Document Exists: True
2025-07-22 20:38:40,201 - DEBUG - [main.py:8677] - 🔍   - Current Phase: quiz_initiate
2025-07-22 20:38:40,201 - DEBUG - [main.py:8678] - 🔍   - Probing Level: 6
2025-07-22 20:38:40,201 - DEBUG - [main.py:8679] - 🔍   - Question Index: 0
2025-07-22 20:38:40,201 - WARNING - [main.py:8685] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 SESSION STATE DEBUG:
2025-07-22 20:38:40,202 - WARNING - [main.py:8686] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   - Session exists: True
2025-07-22 20:38:40,202 - WARNING - [main.py:8687] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   - Current phase: quiz_initiate
2025-07-22 20:38:40,202 - WARNING - [main.py:8688] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   - State data keys: ['created_at', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_probing_level_number', 'quiz_performance', 'current_phase', 'is_first_encounter_for_module', 'new_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'diagnostic_complete', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'unified_pathway_enforced', 'teaching_complete', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'lessonRef', 'current_question_index', 'quiz_interactions', 'current_session_working_level', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
2025-07-22 20:38:40,202 - DEBUG - [main.py:8706] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
2025-07-22 20:38:40,202 - DEBUG - [main.py:8707] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Retrieved Phase: 'quiz_initiate'
2025-07-22 20:38:40,203 - DEBUG - [main.py:8708] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Diagnostic Completed: True
2025-07-22 20:38:40,203 - DEBUG - [main.py:8709] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Assigned Level: 6
2025-07-22 20:38:40,204 - WARNING - [main.py:8710] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔒 STATE PROTECTION: phase='quiz_initiate', diagnostic_done=True, level=6
2025-07-22 20:38:40,204 - INFO - [main.py:8739] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ State is valid - no correction needed
2025-07-22 20:38:40,204 - INFO - [main.py:8740] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ State validation passed: quiz_initiate
2025-07-22 20:38:40,205 - INFO - [main.py:8772] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 ADVANCED PHASE DETECTED: 'quiz_initiate' - treating as returning student
2025-07-22 20:38:40,205 - WARNING - [main.py:8789] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ⚠️ No latest assessed level in profile, using session state: 6
2025-07-22 20:38:40,206 - INFO - [main.py:8791] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]  🔍 FIRST ENCOUNTER LOGIC:
2025-07-22 20:38:40,206 - INFO - [main.py:8792] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   assigned_level_for_teaching (session): 6
2025-07-22 20:38:40,206 - INFO - [main.py:8793] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   latest_assessed_level (profile): None
2025-07-22 20:38:40,207 - INFO - [main.py:8794] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   teaching_level_for_returning_student: 6
2025-07-22 20:38:40,207 - INFO - [main.py:8795] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   has_completed_diagnostic_before: True
2025-07-22 20:38:40,208 - INFO - [main.py:8796] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   is_first_encounter_for_module: False
2025-07-22 20:38:40,208 - INFO - [main.py:8806] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ RETURNING STUDENT: Using existing diagnostic state - diagnostic_completed=True
2025-07-22 20:38:40,208 - INFO - [main.py:8809] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 PHASE INVESTIGATION:
2025-07-22 20:38:40,208 - INFO - [main.py:8810] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Retrieved from Firestore: 'quiz_initiate'
2025-07-22 20:38:40,209 - INFO - [main.py:8811] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'smart_diagnostic_start'
2025-07-22 20:38:40,209 - INFO - [main.py:8812] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Is first encounter: False
2025-07-22 20:38:40,209 - INFO - [main.py:8813] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Diagnostic completed: True
2025-07-22 20:38:40,209 - INFO - [main.py:8819] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ Using stored phase from Firestore: 'quiz_initiate'
2025-07-22 20:38:40,210 - INFO - [main.py:8833] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
2025-07-22 20:38:40,210 - INFO - [main.py:8835] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Final phase for AI logic: quiz_initiate
2025-07-22 20:38:40,210 - INFO - [main.py:4117] - 🎯 SMART_DIAGNOSTIC: get_initial_probing_level called with grade='primary-5' - RETURNING Q1 FOUNDATION LEVEL
2025-07-22 20:38:40,210 - INFO - [main.py:3833] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='primary-5'
2025-07-22 20:38:40,211 - INFO - [main.py:3842] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-22 20:38:40,211 - INFO - [main.py:3905] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-22 20:38:40,211 - INFO - [main.py:4125] - 🎯 SMART_DIAGNOSTIC: Q1 foundation level = 2 for grade primary-5
2025-07-22 20:38:40,212 - INFO - [main.py:6144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Diagnostic context validation passed
2025-07-22 20:38:40,212 - INFO - [main.py:9013] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 PRESERVING ADVANCED PHASE: 'quiz_initiate' - no override for advanced phases
2025-07-22 20:38:40,212 - INFO - [main.py:9030] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Skipping diagnostic context enhancement for non-diagnostic phase: quiz_initiate
2025-07-22 20:38:40,212 - DEBUG - [main.py:9039] - 🧪 DEBUG PHASE: current_phase_for_ai = 'quiz_initiate'
2025-07-22 20:38:40,213 - DEBUG - [main.py:9040] - 🧪 DEBUG PHASE: determined_phase = 'quiz_initiate'
2025-07-22 20:38:40,213 - INFO - [main.py:9042] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Robust context prepared successfully. Phase: quiz_initiate
2025-07-22 20:38:40,214 - DEBUG - [main.py:9043] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'objectives_covered', 'total_objectives', 'objectives_coverage_percentage', 'content_depth_score', 'teaching_time_minutes', 'teaching_start_time', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai', 'current_phase', 'current_lesson_phase']
2025-07-22 20:38:40,214 - INFO - [main.py:9056] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ENHANCED: Handling quiz_initiate phase...
2025-07-22 20:38:40,215 - INFO - [main.py:9060] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] QUIZ_INITIATE: Automatically proceeding to quiz questions (no confirmation required)
2025-07-22 20:38:40,215 - INFO - [main.py:9064] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ENHANCED: Student is ready for curriculum-aligned quiz. Generating questions...
2025-07-22 20:38:40,216 - INFO - [teaching_level_consistency.py:44] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 TEACHING_LEVEL: Determining teaching level from context
2025-07-22 20:38:40,218 - INFO - [teaching_level_consistency.py:60] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 TEACHING_LEVEL: Using Explicitly assigned teaching level = 6
2025-07-22 20:38:40,219 - INFO - [teaching_level_consistency.py:102] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔄 TEACHING_LEVEL: Propagating level 6 throughout context
2025-07-22 20:38:40,219 - DEBUG - [teaching_level_consistency.py:118] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔄 TEACHING_LEVEL: Set assigned_level_for_teaching = 6
2025-07-22 20:38:40,219 - DEBUG - [teaching_level_consistency.py:118] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔄 TEACHING_LEVEL: Set current_session_working_level = 6
2025-07-22 20:38:40,220 - DEBUG - [teaching_level_consistency.py:118] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔄 TEACHING_LEVEL: Set teaching_level = 6
2025-07-22 20:38:40,220 - DEBUG - [teaching_level_consistency.py:118] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔄 TEACHING_LEVEL: Set quiz_generation_level = 6
2025-07-22 20:38:40,220 - DEBUG - [teaching_level_consistency.py:118] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔄 TEACHING_LEVEL: Set content_generation_level = 6
2025-07-22 20:38:40,221 - INFO - [main.py:9072] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 CONSISTENT TEACHING LEVEL: Using level 6 from Explicitly assigned teaching level
2025-07-22 20:38:40,221 - INFO - [main.py:9073] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 Teaching level propagated throughout lesson context
2025-07-22 20:38:40,223 - INFO - [main.py:9096] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ENHANCED: Generating curriculum-aligned quiz questions using lesson content: 136 chars
2025-07-22 20:38:40,225 - INFO - [main.py:22894] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 CURRICULUM-ALIGNED: Generating quiz questions from lesson content (136 chars)
2025-07-22 20:38:40,226 - INFO - [main.py:22895] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 DYNAMIC DIFFICULTY: Adapting quiz complexity to teaching level 6/10
2025-07-22 20:38:42,437 - INFO - [main.py:22992] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ AI RESPONSE RECEIVED: 2770 characters
2025-07-22 20:38:42,437 - INFO - [main.py:23007] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 RAW AI RESPONSE: ```json
[
  {
    "question_number": 1,
    "question_type": "multiple_choice",
    "question": "What is the main topic of this lesson?",
    "options": [
      "A) AI in Education",
      "B) AI in H...
2025-07-22 20:38:42,439 - ERROR - [main.py:23065] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] JSON parsing failed: Expecting ',' delimiter: line 63 column 34 (char 1598)
2025-07-22 20:38:42,439 - ERROR - [main.py:23066] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Failed JSON: [
  {
    "question_number": 1,
    "question_type": "multiple_choice",
    "question": "What is the main topic of this lesson?",
    "options": [
      "A) AI in Education",
      "B) AI in Healthcare",
      "C) AI in Transportation",
      "D) AI in Entertainment"
    ],
    "answer": "B",
    "c...
2025-07-22 20:38:44,524 - INFO - [main.py:22992] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ AI RESPONSE RECEIVED: 2549 characters
2025-07-22 20:38:44,524 - INFO - [main.py:23007] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 RAW AI RESPONSE: ```json
[
  {
    "question_number": 1,
    "question_type": "multiple_choice",
    "question": "What is the main topic of this lesson?",
    "options": [
      "A) AI in Space Exploration",
      "B)...
2025-07-22 20:38:44,525 - INFO - [main.py:23024] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ JSON PARSED: Found 10 questions
2025-07-22 20:38:44,526 - INFO - [main.py:23061] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ QUIZ GENERATED: 10 questions at level 6
2025-07-22 20:38:44,526 - INFO - [main.py:9118] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ENHANCED: Quiz validation score: 0.00
2025-07-22 20:38:44,527 - INFO - [teaching_level_consistency.py:44] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 TEACHING_LEVEL: Determining teaching level from context
2025-07-22 20:38:44,527 - INFO - [teaching_level_consistency.py:60] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 TEACHING_LEVEL: Using Explicitly assigned teaching level = 6
2025-07-22 20:38:44,528 - INFO - [teaching_level_consistency.py:215] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 QUIZ_VALIDATION: Validating 10 questions for level 6
2025-07-22 20:38:44,528 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_1 content for level 6
2025-07-22 20:38:44,530 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_1 consistency = True, score = 1.00
2025-07-22 20:38:44,531 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_2 content for level 6
2025-07-22 20:38:44,531 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_2 consistency = True, score = 1.00
2025-07-22 20:38:44,531 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_3 content for level 6
2025-07-22 20:38:44,531 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_3 consistency = True, score = 1.00
2025-07-22 20:38:44,532 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_4 content for level 6
2025-07-22 20:38:44,532 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_4 consistency = True, score = 1.00
2025-07-22 20:38:44,532 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_5 content for level 6
2025-07-22 20:38:44,533 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_5 consistency = True, score = 1.00
2025-07-22 20:38:44,533 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_6 content for level 6
2025-07-22 20:38:44,534 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_6 consistency = True, score = 1.00
2025-07-22 20:38:44,534 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_7 content for level 6
2025-07-22 20:38:44,534 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_7 consistency = True, score = 1.00
2025-07-22 20:38:44,534 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_8 content for level 6
2025-07-22 20:38:44,534 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_8 consistency = True, score = 1.00
2025-07-22 20:38:44,535 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_9 content for level 6
2025-07-22 20:38:44,535 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_9 consistency = True, score = 1.00
2025-07-22 20:38:44,535 - INFO - [teaching_level_consistency.py:144] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: Validating quiz_question_10 content for level 6
2025-07-22 20:38:44,535 - INFO - [teaching_level_consistency.py:171] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 LEVEL_VALIDATION: quiz_question_10 consistency = True, score = 1.00
2025-07-22 20:38:44,535 - INFO - [teaching_level_consistency.py:251] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 QUIZ_VALIDATION: Alignment = True, aligned = 10/10
2025-07-22 20:38:44,536 - INFO - [main.py:9129] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ENHANCED: Quiz questions properly aligned with teaching level 6
2025-07-22 20:38:44,536 - WARNING - [main.py:9132] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ENHANCED: Quiz questions not sufficiently relevant (score: 0.00). Regenerating...
2025-07-22 20:38:44,537 - INFO - [main.py:9160] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ENHANCED: Transitioning to 'quiz_questions' phase.
2025-07-22 20:38:44,537 - DEBUG - [main.py:9161] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 ENHANCED QUIZ: Generated 10 questions, starting quiz
2025-07-22 20:38:44,537 - INFO - [main.py:9169] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ENHANCED: Quiz initiate handled, skipping normal AI processing
2025-07-22 20:38:44,537 - INFO - [main.py:9247] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ENHANCED: Quiz phase handled, skipping AI processing
2025-07-22 20:38:44,538 - DEBUG - [main.py:9248] - 🔥 QUIZ PHASE SKIPPED AI PROCESSING:
2025-07-22 20:38:44,538 - DEBUG - [main.py:9249] - 🔥   Phase: quiz_initiate
2025-07-22 20:38:44,538 - DEBUG - [main.py:9250] - 🔥   Enhanced content available: True
2025-07-22 20:38:44,538 - DEBUG - [main.py:9251] - 🔥   State updates: {'new_phase': 'quiz_questions', 'quiz_questions_generated': [{'question': 'What is the main topic of this lesson?', 'type': 'Multiple Choice', 'options': ['A) AI in Space Exploration', 'B) AI in Healthcare', 'C) AI in Education', 'D) AI in Finance'], 'correct_answer': 'B', 'concept': 'AI in Healthcare'}, {'question': 'Which of these is a way AI can be used in healthcare?', 'type': 'Multiple Choice', 'options': ['A) Helping patients learn to play music', 'B) Finding new ways to treat diseases', 'C) Designing video games for doctors', 'D) Predicting the weather for hospitals'], 'correct_answer': 'B', 'concept': 'used'}, {'question': "What is AI's role in helping doctors find out what is wrong with someone?", 'type': 'Multiple Choice', 'options': ['A) To make patients laugh', 'B) To help with diagnosis', 'C) To clean hospital rooms', 'D) To deliver medicine'], 'correct_answer': 'B', 'concept': 'diagnosis'}, {'question': 'AI can help in the process of finding new medicines, which is called:', 'type': 'Multiple Choice', 'options': ['A) Disease prediction', 'B) Patient care', 'C) Drug discovery', 'D) Medical imaging'], 'correct_answer': 'C', 'concept': 'drug discovery'}, {'question': 'AI can help doctors to ______ new treatments for diseases.', 'type': 'Fill in the Blank', 'options': [], 'correct_answer': '', 'concept': 'used'}, {'question': 'The lesson is an __________ to how AI is changing the world of __________.', 'type': 'Fill in the Blank', 'options': [], 'correct_answer': '', 'concept': 'Introduction, Healthcare'}, {'question': 'AI can help doctors make a __________ by looking at patient information.', 'type': 'Fill in the Blank', 'options': [], 'correct_answer': '', 'concept': 'diagnosis'}, {'question': 'What is one important role AI plays in healthcare?', 'type': 'Short Answer', 'options': [], 'correct_answer': '', 'concept': 'role'}, {'question': 'Besides diagnosis, name another area where AI is used in healthcare.', 'type': 'Short Answer', 'options': [], 'correct_answer': '', 'concept': 'used'}, {'question': 'What does AI help to discover in healthcare?', 'type': 'Short Answer', 'options': [], 'correct_answer': '', 'concept': 'drug discovery'}], 'quiz_started': True, 'current_quiz_question': 0, 'quiz_answers': []}
2025-07-22 20:38:44,538 - INFO - [main.py:9259] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 UNIFIED STATE MACHINE STATUS:
2025-07-22 20:38:44,539 - INFO - [main.py:9260] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   Current phase: quiz_initiate
2025-07-22 20:38:44,539 - INFO - [main.py:9261] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   Teaching interactions: 0
2025-07-22 20:38:44,539 - INFO - [main.py:9262] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   Quiz interactions: 0
2025-07-22 20:38:44,539 - INFO - [main.py:9263] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   Teaching complete: False
2025-07-22 20:38:44,539 - INFO - [main.py:9264] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   Quiz complete: False
2025-07-22 20:38:44,539 - INFO - [main.py:9265] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   Objectives coverage: 0.0%
2025-07-22 20:38:44,540 - WARNING - [main.py:10260] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 STATE UPDATE VALIDATION: current_phase='quiz_initiate', new_phase='quiz_questions'
2025-07-22 20:38:44,540 - INFO - [main.py:6365] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI state update validation passed: quiz_initiate → quiz_questions
2025-07-22 20:38:44,540 - WARNING - [main.py:10269] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ STATE UPDATE VALIDATION PASSED
2025-07-22 20:38:44,540 - WARNING - [main.py:10288] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔄 PHASE TRANSITION: quiz_initiate → quiz_questions
2025-07-22 20:38:44,540 - INFO - [main.py:10319] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 STATE MACHINE: Proposed phase mapped to unified pathway: quiz_initiate
2025-07-22 20:38:44,541 - INFO - [main.py:10324] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔄 APPLYING PHASE TRANSITION INTEGRITY SYSTEM
2025-07-22 20:38:44,541 - INFO - [phase_transition_integrity.py:315] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔄 APPLYING TRANSITION WITH INTEGRITY: quiz_initiate → quiz_initiate
2025-07-22 20:38:44,541 - INFO - [phase_transition_integrity.py:278] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 📸 DATA SNAPSHOT CREATED: Session fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase quiz_initiate
2025-07-22 20:38:44,541 - INFO - [phase_transition_integrity.py:153] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 PHASE TRANSITION VALIDATION: quiz_initiate → quiz_initiate
2025-07-22 20:38:44,541 - ERROR - [phase_transition_integrity.py:704] - 🔍 STATE CONSISTENCY ERRORS DETECTED:
2025-07-22 20:38:44,541 - ERROR - [phase_transition_integrity.py:706] - 🔍   1. Cannot enter quiz phase without sufficient teaching
2025-07-22 20:38:44,542 - ERROR - [phase_transition_integrity.py:707] - 🔍 SESSION DATA DEBUG:
2025-07-22 20:38:44,542 - ERROR - [phase_transition_integrity.py:708] - 🔍   - diagnostic_complete: True
2025-07-22 20:38:44,542 - ERROR - [phase_transition_integrity.py:709] - 🔍   - assigned_level_for_teaching: 6
2025-07-22 20:38:44,542 - ERROR - [phase_transition_integrity.py:710] - 🔍   - teaching_complete: False
2025-07-22 20:38:44,542 - ERROR - [phase_transition_integrity.py:711] - 🔍   - teaching_interactions: 0
2025-07-22 20:38:44,542 - ERROR - [phase_transition_integrity.py:712] - 🔍   - from_phase: quiz_initiate, to_phase: quiz_initiate
2025-07-22 20:38:44,542 - ERROR - [phase_transition_integrity.py:211] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ❌ STATE INCONSISTENCY: State consistency check: 1 errors found
2025-07-22 20:38:44,543 - ERROR - [phase_transition_integrity.py:374] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔧 TRANSITION RECOVERY: quiz_initiate → teaching_start
2025-07-22 20:38:44,543 - DEBUG - [phase_transition_integrity.py:832] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔒 CRITICAL DATA PRESERVED: 18 fields checked
2025-07-22 20:38:44,543 - DEBUG - [phase_transition_integrity.py:865] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 📝 TRANSITION RECORDED: quiz_initiate → teaching_start (invalid)
2025-07-22 20:38:44,543 - INFO - [phase_transition_integrity.py:391] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = teaching_start
2025-07-22 20:38:44,544 - WARNING - [main.py:10366] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔧 STATE MACHINE: Invalid transition corrected to quiz_questions
2025-07-22 20:38:44,544 - WARNING - [main.py:10393] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔧 TRANSITION RECOVERED: State inconsistency cannot be resolved: State consistency check: 1 errors found
2025-07-22 20:38:44,544 - WARNING - [main.py:10402] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 COMPLETE PHASE FLOW DEBUG:
2025-07-22 20:38:44,545 - WARNING - [main.py:10403] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   1. Input phase: 'quiz_initiate'
2025-07-22 20:38:44,545 - WARNING - [main.py:10404] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   2. Python calculated next phase: 'NOT_FOUND'
2025-07-22 20:38:44,545 - WARNING - [main.py:10405] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   3. Proposed phase: 'quiz_initiate'
2025-07-22 20:38:44,545 - WARNING - [main.py:10406] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   4. Integrity validation result: 'invalid'
2025-07-22 20:38:44,545 - WARNING - [main.py:10407] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍   5. Final phase to save: 'quiz_questions'
2025-07-22 20:38:44,546 - WARNING - [main.py:10410] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 💾 FINAL STATE APPLICATION:
2025-07-22 20:38:44,546 - WARNING - [main.py:10411] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 💾   - Current phase input: 'quiz_initiate'
2025-07-22 20:38:44,546 - WARNING - [main.py:10412] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 💾   - Validated state updates: 28 fields
2025-07-22 20:38:44,547 - WARNING - [main.py:10413] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 💾   - Final phase to save: 'quiz_questions'
2025-07-22 20:38:44,547 - WARNING - [main.py:10414] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 💾   - Phase change: True
2025-07-22 20:38:44,547 - WARNING - [main.py:10415] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 💾   - Integrity applied: True
2025-07-22 20:38:44,548 - INFO - [main.py:6397] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] DIAGNOSTIC_FLOW_METRICS:
2025-07-22 20:38:44,548 - INFO - [main.py:6398] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Phase transition: quiz_initiate -> teaching_start
2025-07-22 20:38:44,548 - INFO - [main.py:6399] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Current level: 6
2025-07-22 20:38:44,548 - INFO - [main.py:6400] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Question index: 0
2025-07-22 20:38:44,548 - INFO - [main.py:6401] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   First encounter: False
2025-07-22 20:38:44,549 - INFO - [main.py:6406] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Answers collected: 0
2025-07-22 20:38:44,549 - INFO - [main.py:6407] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Levels failed: 0
2025-07-22 20:38:44,549 - INFO - [main.py:6365] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI state update validation passed: quiz_initiate → teaching_start
2025-07-22 20:38:44,549 - INFO - [main.py:6411] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   State update valid: True
2025-07-22 20:38:44,549 - INFO - [main.py:6418] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Diagnostic complete: False
2025-07-22 20:38:44,549 - INFO - [main.py:6420] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e]   Assigned level: 6
2025-07-22 20:38:44,549 - WARNING - [main.py:10433] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
2025-07-22 20:38:44,550 - INFO - [main.py:10442] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 DEBUG state_updates_from_ai teaching_interactions: 0
2025-07-22 20:38:44,550 - INFO - [main.py:10443] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔍 DEBUG backend teaching_interactions: 0
2025-07-22 20:38:44,885 - WARNING - [main.py:10493] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ SESSION STATE SAVED TO FIRESTORE:
2025-07-22 20:38:44,885 - WARNING - [main.py:10494] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅   - Phase: quiz_questions
2025-07-22 20:38:44,886 - WARNING - [main.py:10495] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅   - Probing Level: 6
2025-07-22 20:38:44,886 - WARNING - [main.py:10496] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅   - Question Index: 0
2025-07-22 20:38:44,886 - WARNING - [main.py:10497] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅   - Diagnostic Complete: True
2025-07-22 20:38:44,886 - WARNING - [main.py:10504] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅   - Quiz Questions Saved: 10
2025-07-22 20:38:44,887 - WARNING - [main.py:10505] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅   - Quiz Answers Saved: 0
2025-07-22 20:38:44,887 - WARNING - [main.py:10506] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅   - Quiz Started: True
2025-07-22 20:38:44,887 - DEBUG - [main.py:10555] - 🔥 STATE SAVED - Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: quiz_questions
2025-07-22 20:38:44,888 - DEBUG - [main.py:10556] - 🔥 QUIZ DATA - Questions: 10, Answers: 0
2025-07-22 20:38:46,097 - DEBUG - [main.py:10614] - ✅ SESSION UPDATED - ID: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5, Phase: quiz_questions
2025-07-22 20:38:46,097 - DEBUG - [main.py:10615] - ✅ INTERACTION LOGGED - Phase: quiz_initiate → quiz_questions
2025-07-22 20:38:46,098 - INFO - [main.py:10621] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ Updated existing session document: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:38:46,098 - INFO - [main.py:18535] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
2025-07-22 20:38:46,098 - DEBUG - [main.py:4943] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] FINAL_ASSESSMENT_BLOCK not found in AI response.
2025-07-22 20:38:46,099 - DEBUG - [main.py:10708] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] No final assessment data found in AI response
2025-07-22 20:38:46,099 - DEBUG - [main.py:10731] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] No lesson completion detected (Phase: teaching_start, Complete: False)
2025-07-22 20:38:46,099 - WARNING - [main.py:10750] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🔧 STATE CORRECTION: Forcing 'diagnostic_completed_this_session' to True because phase is 'teaching_start'.
2025-07-22 20:38:46,100 - DEBUG - [main.py:10755] - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
2025-07-22 20:38:46,100 - DEBUG - [main.py:10756] - 🔒   Current Phase: quiz_initiate
2025-07-22 20:38:46,101 - DEBUG - [main.py:10757] - 🔒   Final Phase: teaching_start
2025-07-22 20:38:46,101 - DEBUG - [main.py:10758] - 🔒   Diagnostic Complete: True
2025-07-22 20:38:46,102 - DEBUG - [main.py:10759] - 🔒   Assigned Level: 6
2025-07-22 20:38:46,103 - INFO - [main.py:10832] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ POST-PROCESSING VALIDATION: All validations passed
2025-07-22 20:38:46,103 - INFO - [main.py:10866] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ POST-PROCESSING: Response format standardized successfully
2025-07-22 20:38:46,104 - INFO - [main.py:10874] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎯 POST-PROCESSING COMPLETE: Validation=True, Errors=0
2025-07-22 20:38:46,104 - INFO - [main.py:10879] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🛡️ APPLYING INTELLIGENT GUARDRAILS VALIDATION
2025-07-22 20:38:46,104 - INFO - [teaching_rules.py:166] - 🎯 ADAPTIVE REQUIREMENTS CALCULATED:
2025-07-22 20:38:46,105 - INFO - [teaching_rules.py:167] -    Grade: primary-5 → Multiplier: 1.0
2025-07-22 20:38:46,105 - INFO - [teaching_rules.py:168] -    Teaching Level: 6 → Multiplier: 1.1
2025-07-22 20:38:46,105 - INFO - [teaching_rules.py:169] -    Lesson Complexity: moderate → Multiplier: 1.0
2025-07-22 20:38:46,105 - INFO - [teaching_rules.py:170] -    Adaptive Min Interactions: 11
2025-07-22 20:38:46,106 - INFO - [teaching_rules.py:171] -    Adaptive Content Depth: 0.77
2025-07-22 20:38:46,106 - INFO - [teaching_rules.py:172] -    Adaptive Teaching Time: 12 min
2025-07-22 20:38:46,106 - INFO - [teaching_rules.py:374] - 🎯 TEACHING INCOMPLETE: 0.0% coverage, 0 interactions
2025-07-22 20:38:46,107 - INFO - [teaching_rules.py:375] - 🎯 Continue teaching to improve coverage and understanding
2025-07-22 20:38:46,107 - INFO - [teaching_rules.py:398] - 🎓 ENHANCED TEACHING VALIDATION RESULTS:
2025-07-22 20:38:46,107 - INFO - [teaching_rules.py:399] -    📊 ADAPTIVE REQUIREMENTS:
2025-07-22 20:38:46,107 - INFO - [teaching_rules.py:400] -       Grade: primary-5 (×1.0)
2025-07-22 20:38:46,108 - INFO - [teaching_rules.py:401] -       Teaching Level: Level 6 (×1.1)
2025-07-22 20:38:46,108 - INFO - [teaching_rules.py:402] -       Lesson Complexity: moderate (×1.0)
2025-07-22 20:38:46,108 - INFO - [teaching_rules.py:403] -       Calculation: 10 × 1.0 × 1.1 × 1.0 = 11
2025-07-22 20:38:46,109 - INFO - [teaching_rules.py:404] -    📈 CURRENT PROGRESS:
2025-07-22 20:38:46,109 - INFO - [teaching_rules.py:405] -       Interactions: 0/11 (❌)
2025-07-22 20:38:46,109 - INFO - [teaching_rules.py:406] -       Objectives: 0.0%/100% (PRIMARY) (❌)
2025-07-22 20:38:46,109 - INFO - [teaching_rules.py:407] -       Content Depth: 0.00/0.77 (❌)
2025-07-22 20:38:46,110 - INFO - [teaching_rules.py:408] -       Teaching Time: 0.0/12 min (❌)
2025-07-22 20:38:46,110 - INFO - [teaching_rules.py:409] -    🎯 RESULT: ❌ INCOMPLETE - teaching_incomplete_need_more_coverage
2025-07-22 20:38:46,110 - INFO - [main.py:10888] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🎓 TEACHING COMPLETION STATUS: False (teaching_incomplete_need_more_coverage)
2025-07-22 20:38:46,111 - INFO - [intelligent_guardrails.py:130] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🛡️ GUARDRAILS: Validating AI response (Teaching Complete: False)
2025-07-22 20:38:46,111 - INFO - [intelligent_guardrails.py:167] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🛡️ GUARDRAIL VIOLATION: cognitive_level_matching - critical
2025-07-22 20:38:46,111 - INFO - [intelligent_guardrails.py:473] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🛡️ CRITICAL VIOLATIONS: Adjusting content complexity
2025-07-22 20:38:46,112 - INFO - [intelligent_guardrails.py:183] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🛡️ GUARDRAILS RESULT: Valid=True, Violations=1
2025-07-22 20:38:46,112 - INFO - [main.py:10950] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🛡️ GUARDRAILS VALIDATION: Valid=True, Violations=1
2025-07-22 20:38:46,112 - WARNING - [main.py:10954] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] 🚨 GUARDRAIL VIOLATION: cognitive_level_matching - Content too complex for level 5 (Severity: GuardrailSeverity.CRITICAL)
2025-07-22 20:38:46,113 - INFO - [main.py:10975] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✨ GUARDRAILS ENHANCED RESPONSE: Applied pedagogical improvements
2025-07-22 20:38:46,114 - INFO - [main.py:10978] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] ✅ INTELLIGENT GUARDRAILS APPLIED SUCCESSFULLY
2025-07-22 20:38:46,114 - DEBUG - [main.py:11035] - 🎯 RESPONSE READY:
2025-07-22 20:38:46,115 - DEBUG - [main.py:11036] - 🎯   Session: fallback-2434947d-06e0-4e1f-8cde-dac3960d59c5
2025-07-22 20:38:46,115 - DEBUG - [main.py:11037] - 🎯   Phase: quiz_initiate → teaching_start
2025-07-22 20:38:46,116 - DEBUG - [main.py:11038] - 🎯   Content: Let me adjust that explanation. Simplify language ...
2025-07-22 20:38:46,116 - DEBUG - [main.py:11039] - 🎯   Request ID: fc051ccf-fdc8-47b6-8878-9213d4491d5e
2025-07-22 20:38:46,116 - INFO - [main.py:11045] - [fc051ccf-fdc8-47b6-8878-9213d4491d5e] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
2025-07-22 20:38:46,117 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,117 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,118 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,118 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,118 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,118 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,119 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,119 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,119 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,119 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,120 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,120 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,120 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,120 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,120 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,120 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,120 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,120 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,121 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,121 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,121 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,121 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,121 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,121 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,121 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,121 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,122 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,122 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,122 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing list
2025-07-22 20:38:46,122 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,122 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,122 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,122 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,122 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,123 - DEBUG - [main.py:23685] - 🧹 SANITIZE_DEBUG: Processing dict
2025-07-22 20:38:46,123 - WARNING - [main.py:888] - High response time detected: 10.24s for enhance_content_api
