#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Unified Pathway Implementation

This script tests the unified lesson pathway implementation to ensure that:
1. All alternative code paths have been removed
2. Only the unified pathway is supported
3. Phase transitions are properly validated
4. Legacy phase names are correctly normalized
"""

import os
import sys
import logging
import unittest
import json
from typing import Dict, Any, List, Tuple, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the modules we want to test
try:
    from lesson_manager.main import LessonPhase
    from lesson_manager.phase_transition_integrity import phase_transition_manager, PhaseTransitionResult
except ImportError as e:
    logger.error(f"Error importing modules: {e}")
    logger.error("Make sure you're running this script from the backend/cloud_function directory")
    sys.exit(1)

class TestUnifiedPathway(unittest.TestCase):
    """Test the unified lesson pathway implementation."""
    
    def setUp(self):
        """Set up the test case."""
        self.unified_phases = [
            'teaching_start', 'teaching', 'quiz_initiate', 
            'quiz_questions', 'quiz_results', 'final_report_inprogress', 'complete'
        ]
        
        self.valid_transitions = {
            'teaching_start': ['teaching', 'quiz_initiate'],
            'teaching': ['quiz_initiate'],
            'quiz_initiate': ['quiz_questions'],
            'quiz_questions': ['quiz_questions', 'quiz_results'],
            'quiz_results': ['final_report_inprogress'],
            'final_report_inprogress': ['complete'],
            'complete': []
        }
        
        self.legacy_phases = [
            'diagnostic_start_probe', 'diagnostic_probing_L1_ask_q1',
            'smart_diagnostic_start', 'smart_diagnostic_q1',
            'quiz', 'quiz_start', 'quiz_complete',
            'conclusion_summary', 'completed'
        ]
    
    def test_lesson_phase_constants(self):
        """Test that the LessonPhase class has the correct constants."""
        self.assertEqual(LessonPhase.TEACHING_START, 'teaching_start')
        self.assertEqual(LessonPhase.TEACHING, 'teaching')
        self.assertEqual(LessonPhase.QUIZ_INITIATE, 'quiz_initiate')
        self.assertEqual(LessonPhase.QUIZ_QUESTIONS, 'quiz_questions')
        self.assertEqual(LessonPhase.QUIZ_RESULTS, 'quiz_results')
        self.assertEqual(LessonPhase.FINAL_REPORT_INPROGRESS, 'final_report_inprogress')
        self.assertEqual(LessonPhase.COMPLETE, 'complete')
    
    def test_valid_transitions(self):
        """Test that the valid transitions match the unified pathway."""
        for from_phase, to_phases in self.valid_transitions.items():
            for to_phase in to_phases:
                self.assertTrue(
                    LessonPhase.validate_transition(from_phase, to_phase),
                    f"Transition from {from_phase} to {to_phase} should be valid"
                )
    
    def test_invalid_transitions(self):
        """Test that invalid transitions are rejected."""
        invalid_transitions = [
            ('teaching', 'teaching_start'),  # Backward transition
            ('quiz_initiate', 'teaching'),   # Backward transition
            ('quiz_questions', 'teaching'),  # Backward transition
            ('quiz_results', 'quiz_initiate'),  # Backward transition
            ('final_report_inprogress', 'quiz_results'),  # Backward transition
            ('complete', 'final_report_inprogress'),  # Backward transition
            ('teaching', 'quiz_results'),  # Skip phase
            ('teaching_start', 'quiz_questions'),  # Skip phase
            ('quiz_initiate', 'quiz_results'),  # Skip phase
            ('quiz_initiate', 'final_report_inprogress'),  # Skip phase
            ('quiz_questions', 'complete'),  # Skip phase
        ]
        
        for from_phase, to_phase in invalid_transitions:
            self.assertFalse(
                LessonPhase.validate_transition(from_phase, to_phase),
                f"Transition from {from_phase} to {to_phase} should be invalid"
            )
    
    def test_normalize_phase_name(self):
        """Test that legacy phase names are correctly normalized."""
        # Test diagnostic phases
        for phase in ['diagnostic_start_probe', 'smart_diagnostic_start', 'smart_diagnostic_q1']:
            normalized = LessonPhase._normalize_phase_name(phase)
            self.assertEqual(normalized, LessonPhase.TEACHING_START)
        
        # Test teaching phases
        for phase in ['teaching_level_5', 'teaching_start_level_3']:
            normalized = LessonPhase._normalize_phase_name(phase)
            self.assertEqual(normalized, LessonPhase.TEACHING)
        
        # Test quiz phases
        self.assertEqual(LessonPhase._normalize_phase_name('quiz'), LessonPhase.QUIZ_INITIATE)
        self.assertEqual(LessonPhase._normalize_phase_name('quiz_start'), LessonPhase.QUIZ_INITIATE)
        self.assertEqual(LessonPhase._normalize_phase_name('quiz_complete'), LessonPhase.QUIZ_RESULTS)
        
        # Test completion phases
        self.assertEqual(LessonPhase._normalize_phase_name('conclusion_summary'), LessonPhase.COMPLETE)
        self.assertEqual(LessonPhase._normalize_phase_name('completed'), LessonPhase.COMPLETE)
    
    def test_get_next_phase(self):
        """Test that the get_next_phase method returns the correct next phase."""
        self.assertEqual(LessonPhase.get_next_phase(None), LessonPhase.TEACHING_START)
        self.assertEqual(LessonPhase.get_next_phase(LessonPhase.TEACHING_START), LessonPhase.TEACHING)
        self.assertEqual(LessonPhase.get_next_phase(LessonPhase.TEACHING), LessonPhase.QUIZ_INITIATE)
        self.assertEqual(LessonPhase.get_next_phase(LessonPhase.QUIZ_INITIATE), LessonPhase.QUIZ_QUESTIONS)
        
        # Test quiz_questions with quiz_complete=False
        self.assertEqual(
            LessonPhase.get_next_phase(LessonPhase.QUIZ_QUESTIONS, {'quiz_complete': False}),
            LessonPhase.QUIZ_QUESTIONS
        )
        
        # Test quiz_questions with quiz_complete=True
        self.assertEqual(
            LessonPhase.get_next_phase(LessonPhase.QUIZ_QUESTIONS, {'quiz_complete': True}),
            LessonPhase.QUIZ_RESULTS
        )
        
        # Test quiz_results with system_trigger_received=False
        self.assertEqual(
            LessonPhase.get_next_phase(LessonPhase.QUIZ_RESULTS, {'system_trigger_received': False}),
            LessonPhase.QUIZ_RESULTS
        )
        
        # Test quiz_results with system_trigger_received=True
        self.assertEqual(
            LessonPhase.get_next_phase(LessonPhase.QUIZ_RESULTS, {'system_trigger_received': True}),
            LessonPhase.FINAL_REPORT_INPROGRESS
        )
        
        self.assertEqual(LessonPhase.get_next_phase(LessonPhase.FINAL_REPORT_INPROGRESS), LessonPhase.COMPLETE)
        self.assertEqual(LessonPhase.get_next_phase(LessonPhase.COMPLETE), LessonPhase.COMPLETE)
    
    def test_phase_transition_integrity(self):
        """Test the phase transition integrity manager."""
        # Test valid transition
        result = phase_transition_manager.validate_phase_transition(
            'teaching', 'quiz_initiate', {}, 'test-request-id'
        )
        self.assertEqual(result.result, PhaseTransitionResult.VALID)
        
        # Test invalid transition
        result = phase_transition_manager.validate_phase_transition(
            'teaching', 'quiz_results', {}, 'test-request-id'
        )
        self.assertNotEqual(result.result, PhaseTransitionResult.VALID)
        
        # Test backward transition
        result = phase_transition_manager.validate_phase_transition(
            'quiz_initiate', 'teaching', {}, 'test-request-id'
        )
        self.assertEqual(result.result, PhaseTransitionResult.BLOCKED)
        
        # Test same phase transition
        result = phase_transition_manager.validate_phase_transition(
            'teaching', 'teaching', {}, 'test-request-id'
        )
        self.assertEqual(result.result, PhaseTransitionResult.VALID)

def main():
    """Run the tests."""
    unittest.main()

if __name__ == "__main__":
    main()