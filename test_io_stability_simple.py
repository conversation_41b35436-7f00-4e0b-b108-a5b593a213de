#!/usr/bin/env python3
"""
Simple test script for I/O Operation Stability Fix in ImmediateConsoleHandler.
"""

import sys
import logging
import io
import os
from unittest.mock import patch

# Import the ImmediateConsoleHandler from main.py
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager'))

# We need to import the class after setting up the path
try:
    from main import ImmediateConsoleHandler
    print("✓ Successfully imported ImmediateConsoleHandler from main.py")
except ImportError as e:
    print(f"✗ Failed to import ImmediateConsoleHandler: {e}")
    sys.exit(1)

def test_handler():
    """Test the ImmediateConsoleHandler with various scenarios."""
    # Create a handler instance
    handler = ImmediateConsoleHandler()
    
    # Create a formatter
    formatter = logging.Formatter('%(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    
    # Create a log record
    record = logging.LogRecord(
        name="test", level=logging.INFO, pathname="", lineno=0,
        msg="Test message", args=(), exc_info=None
    )
    
    # Test 1: Normal operation
    print("\nTest 1: Normal operation")
    try:
        handler.emit(record)
        print("✓ Normal operation works")
    except Exception as e:
        print(f"✗ Normal operation failed: {e}")
    
    # Test 2: With mocked closed stdout
    print("\nTest 2: With mocked closed stdout")
    with patch('sys.stdout') as mock_stdout:
        mock_stdout.closed = True
        try:
            handler.emit(record)
            print("✓ Handled closed stdout gracefully")
        except Exception as e:
            print(f"✗ Failed with closed stdout: {e}")
    
    # Test 3: With mocked closed stderr
    print("\nTest 3: With mocked closed stderr")
    with patch('sys.stderr') as mock_stderr:
        mock_stderr.closed = True
        try:
            handler.emit(record)
            print("✓ Handled closed stderr gracefully")
        except Exception as e:
            print(f"✗ Failed with closed stderr: {e}")
    
    # Test 4: With both streams closed
    print("\nTest 4: With both streams closed")
    with patch('sys.stdout') as mock_stdout, patch('sys.stderr') as mock_stderr:
        mock_stdout.closed = True
        mock_stderr.closed = True
        try:
            handler.emit(record)
            print("✓ Handled both streams closed gracefully")
        except Exception as e:
            print(f"✗ Failed with both streams closed: {e}")
    
    # Test 5: With None streams
    print("\nTest 5: With None streams")
    with patch('sys.stdout', None), patch('sys.stderr', None):
        try:
            handler.emit(record)
            print("✓ Handled None streams gracefully")
        except Exception as e:
            print(f"✗ Failed with None streams: {e}")
    
    # Test 6: With streams lacking 'closed' attribute
    print("\nTest 6: With streams lacking 'closed' attribute")
    with patch('sys.stdout') as mock_stdout, patch('sys.stderr') as mock_stderr:
        # Remove 'closed' attribute
        if hasattr(mock_stdout, 'closed'):
            delattr(mock_stdout, 'closed')
        if hasattr(mock_stderr, 'closed'):
            delattr(mock_stderr, 'closed')
        try:
            handler.emit(record)
            print("✓ Handled streams without 'closed' attribute gracefully")
        except Exception as e:
            print(f"✗ Failed with streams without 'closed' attribute: {e}")
    
    # Test 7: With Unicode message
    print("\nTest 7: With Unicode message")
    unicode_record = logging.LogRecord(
        name="test", level=logging.INFO, pathname="", lineno=0,
        msg="Test message with Unicode: 🚀 ✅ → 📊", args=(), exc_info=None
    )
    try:
        handler.emit(unicode_record)
        print("✓ Handled Unicode message gracefully")
    except Exception as e:
        print(f"✗ Failed with Unicode message: {e}")
    
    # Test 8: With formatting exception
    print("\nTest 8: With formatting exception")
    bad_record = logging.LogRecord(
        name="test", level=logging.INFO, pathname="", lineno=0,
        msg="Test %s %d", args=("string", "not_a_number"), exc_info=None
    )
    try:
        handler.emit(bad_record)
        print("✓ Handled formatting exception gracefully")
    except Exception as e:
        print(f"✗ Failed with formatting exception: {e}")

if __name__ == "__main__":
    test_handler()
    print("\nAll tests completed.")