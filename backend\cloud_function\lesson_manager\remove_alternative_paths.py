#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Remove Alternative Lesson Flow Paths

This script implements task 6.1 from the unified lesson flow refactor spec:
- Identify and eliminate legacy lesson flow paths
- Remove or refactor code that supports non-unified pathways
- Update phase handlers to only support the unified pathway sequence

The unified lesson pathway is defined as:
teaching_start/teaching → quiz_initiate → quiz_questions → quiz_results → final_report_inprogress → complete
"""

import os
import re
import sys
import logging
from typing import List, Dict, Any, Tuple, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Constants
MAIN_PY_PATH = "backend/cloud_function/lesson_manager/main.py"
PHASE_TRANSITION_INTEGRITY_PATH = "backend/cloud_function/lesson_manager/phase_transition_integrity.py"
TEACHING_RULES_PATH = "backend/cloud_function/lesson_manager/teaching_rules.py"

# Unified pathway phases
UNIFIED_PHASES = [
    'teaching_start', 'teaching', 'quiz_initiate', 
    'quiz_questions', 'quiz_results', 'final_report_inprogress', 'complete'
]

# Valid transitions in the unified pathway
UNIFIED_TRANSITIONS = {
    'teaching_start': ['teaching', 'quiz_initiate'],
    'teaching': ['quiz_initiate'],
    'quiz_initiate': ['quiz_questions'],
    'quiz_questions': ['quiz_questions', 'quiz_results'],
    'quiz_results': ['final_report_inprogress'],
    'final_report_inprogress': ['complete'],
    'complete': []
}

def read_file(file_path: str) -> str:
    """Read a file and return its contents."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return ""

def write_file(file_path: str, content: str) -> bool:
    """Write content to a file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        logger.error(f"Error writing to file {file_path}: {e}")
        return False

def update_normalize_phase_name(content: str) -> str:
    """Update the _normalize_phase_name function to strictly enforce the unified pathway."""
    
    # Define the new implementation
    new_normalize_phase_name = """
    @classmethod
    def _normalize_phase_name(cls, phase):
        """
        Normalize all phase names to match unified pathway.
        
        Args:
            phase: The phase name to normalize
            
        Returns:
            str: The normalized phase name that strictly follows the unified pathway
        """
        if not phase:
            return cls.TEACHING_START
        
        # Unified pathway phases - pass through unchanged
        unified_phases = [
            cls.TEACHING_START, cls.TEACHING, cls.QUIZ_INITIATE, 
            cls.QUIZ_QUESTIONS, cls.QUIZ_RESULTS, cls.FINAL_REPORT_INPROGRESS, 
            cls.COMPLETE
        ]
        
        if phase in unified_phases:
            return phase
        
        # Map all diagnostic phases to teaching_start
        if 'diagnostic' in phase.lower():
            return cls.TEACHING_START
        
        # Map all teaching phases to teaching
        if 'teaching' in phase.lower() and phase != cls.TEACHING_START:
            return cls.TEACHING
        
        # Map all quiz phases
        if 'quiz' in phase.lower():
            if 'initiate' in phase.lower() or 'start' in phase.lower():
                return cls.QUIZ_INITIATE
            elif 'result' in phase.lower() or 'complete' in phase.lower():
                return cls.QUIZ_RESULTS
            else:
                return cls.QUIZ_QUESTIONS
        
        # Map all completion phases
        if 'complete' in phase.lower() or 'conclusion' in phase.lower() or 'final' in phase.lower():
            return cls.COMPLETE
        
        # Default to teaching for unknown phases
        logger.warning(f"Unknown phase '{phase}' normalized to {cls.TEACHING}")
        return cls.TEACHING
    """
    
    # Find the current implementation
    pattern = r'@classmethod\s+def _normalize_phase_name\(cls, phase\):.*?def'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # Replace the current implementation with the new one
        start, end = match.span()
        # Keep the last 'def' which belongs to the next function
        updated_content = content[:start] + new_normalize_phase_name + '\n    def' + content[end:]
        return updated_content
    else:
        logger.warning("Could not find _normalize_phase_name function")
        return content

def update_valid_transitions(content: str) -> str:
    """Update the VALID_TRANSITIONS dictionary to enforce the unified pathway."""
    
    # Define the new implementation
    new_valid_transitions = """
        # Valid phase transitions - enforces unified pathway
        VALID_TRANSITIONS = {
            TEACHING_START: [TEACHING, QUIZ_INITIATE],
            TEACHING: [QUIZ_INITIATE],
            QUIZ_INITIATE: [QUIZ_QUESTIONS],
            QUIZ_QUESTIONS: [QUIZ_QUESTIONS, QUIZ_RESULTS],  # Allow staying in quiz_questions for multiple questions
            QUIZ_RESULTS: [FINAL_REPORT_INPROGRESS],
            FINAL_REPORT_INPROGRESS: [COMPLETE],
            COMPLETE: []  # Terminal state
        }
    """
    
    # Find the current implementation
    pattern = r'# Valid phase transitions.*?VALID_TRANSITIONS = \{.*?\}'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # Replace the current implementation with the new one
        start, end = match.span()
        updated_content = content[:start] + new_valid_transitions + content[end:]
        return updated_content
    else:
        logger.warning("Could not find VALID_TRANSITIONS dictionary")
        return content

def update_phase_transition_integrity(content: str) -> str:
    """Update the VALID_TRANSITIONS dictionary in phase_transition_integrity.py."""
    
    # Define the new implementation
    new_valid_transitions = """
    # Valid phase transition mappings - aligned with unified pathway
    VALID_TRANSITIONS = {
        # Teaching phases
        'teaching_start': ['teaching', 'quiz_initiate'],
        'teaching': ['quiz_initiate'],
        
        # Quiz phases
        'quiz_initiate': ['quiz_questions'],
        'quiz_questions': ['quiz_questions', 'quiz_results'],  # Allow staying in quiz_questions
        'quiz_results': ['final_report_inprogress'],
        
        # Completion phases
        'final_report_inprogress': ['complete'],
        'complete': []  # Terminal state
    }
    """
    
    # Find the current implementation
    pattern = r'# Valid phase transition mappings.*?VALID_TRANSITIONS = \{.*?\}'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # Replace the current implementation with the new one
        start, end = match.span()
        updated_content = content[:start] + new_valid_transitions + content[end:]
        return updated_content
    else:
        logger.warning("Could not find VALID_TRANSITIONS dictionary in phase_transition_integrity.py")
        return content

def update_is_valid_phase_transition(content: str) -> str:
    """Remove the _is_valid_phase_transition method and replace it with a call to LessonPhase.validate_transition."""
    
    # Define the new implementation
    new_is_valid_phase_transition = """
    def _is_valid_phase_transition(self, current_phase, new_phase):
        """Validate if phase transition is allowed using the unified pathway"""
        # Use the LessonPhase class to validate transitions
        return LessonPhase.validate_transition(current_phase, new_phase)
    """
    
    # Find the current implementation
    pattern = r'def _is_valid_phase_transition\(self, current_phase, new_phase\):.*?return new_phase in valid_transitions\.get\(current_phase, \[\]\)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # Replace the current implementation with the new one
        start, end = match.span()
        updated_content = content[:start] + new_is_valid_phase_transition + content[end:]
        return updated_content
    else:
        logger.warning("Could not find _is_valid_phase_transition method")
        return content

def update_alternative_paths(content: str) -> str:
    """Remove alternative lesson retrieval paths."""
    
    # Define the new implementation
    new_lesson_retrieval = """
        # Standardized lesson retrieval path
        doc_path = f"lessonRef/{lesson_id_clean}"
        lesson_doc = db.document(doc_path).get()

        if not lesson_doc.exists:
            logger.warning(f"Lesson not found at {doc_path}, using dynamic content generation")
            # Generate dynamic content instead of trying alternative paths
    """
    
    # Find the current implementation
    pattern = r'# Alternative paths to try if primary fails.*?alternative_paths = \[.*?\]'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # Replace the current implementation with the new one
        start, end = match.span()
        updated_content = content[:start] + new_lesson_retrieval + content[end:]
        
        # Also remove the code that tries alternative paths
        pattern = r'# Try alternative paths if primary fails.*?if lesson_doc and lesson_doc\.exists:'
        match = re.search(pattern, updated_content, re.DOTALL)
        
        if match:
            start, end = match.span()
            updated_content = updated_content[:start] + "        # Use only the primary path\n        if lesson_doc and lesson_doc.exists:" + updated_content[end:]
        
        return updated_content
    else:
        logger.warning("Could not find alternative paths code")
        return content

def update_phase_handlers(content: str) -> str:
    """Update phase handlers to support only the unified pathway sequence."""
    
    # Define the new implementation
    new_phase_handlers = """
                # Route to appropriate phase handler based on unified pathway
                if current_phase in [LessonPhase.TEACHING_START, LessonPhase.TEACHING]:
                    logger.info(f"[{request_id}] 🎯 ROUTING: Teaching phase handler")
                    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_teaching_phase(
                        user_query, chat_history, context_for_enhance, session_state_data, request_id
                    )
                elif current_phase == LessonPhase.QUIZ_INITIATE:
                    logger.info(f"[{request_id}] 🎯 ROUTING: Quiz initiate phase handler")
                    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_quiz_initiate_phase(
                        user_query, chat_history, context_for_enhance, session_state_data, request_id
                    )
                elif current_phase == LessonPhase.QUIZ_QUESTIONS:
                    logger.info(f"[{request_id}] 🎯 ROUTING: Quiz questions phase handler")
                    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_quiz_questions_phase(
                        user_query, chat_history, context_for_enhance, session_state_data, request_id
                    )
                elif current_phase == LessonPhase.QUIZ_RESULTS:
                    logger.info(f"[{request_id}] 🎯 ROUTING: Quiz results phase handler")
                    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_quiz_results_phase(
                        user_query, chat_history, context_for_enhance, session_state_data, request_id
                    )
                elif current_phase == LessonPhase.FINAL_REPORT_INPROGRESS:
                    logger.info(f"[{request_id}] 🎯 ROUTING: Final report phase handler")
                    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_final_report_phase(
                        user_query, chat_history, context_for_enhance, session_state_data, request_id
                    )
                elif current_phase == LessonPhase.COMPLETE:
                    logger.info(f"[{request_id}] 🎯 ROUTING: Complete phase handler")
                    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_complete_phase(
                        user_query, chat_history, context_for_enhance, session_state_data, request_id
                    )
                else:
                    # Fallback to teaching phase for unknown phases - enforce unified pathway
                    logger.warning(f"[{request_id}] ⚠️ Unknown phase '{current_phase}', enforcing unified pathway")
                    logger.warning(f"[{request_id}] ⚠️ Defaulting to teaching_start phase")
                    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_teaching_phase(
                        user_query, chat_history, context_for_enhance, session_state_data, request_id
                    )
                    
                    # Force phase update to teaching_start
                    state_updates_from_ai['current_phase'] = LessonPhase.TEACHING_START
                    state_updates_from_ai['unified_pathway_enforced'] = True
    """
    
    # Find the current implementation
    pattern = r'# Route to appropriate phase handler based on unified pathway.*?state_updates_from_ai\[\'unified_pathway_enforced\'\] = True'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # Replace the current implementation with the new one
        start, end = match.span()
        updated_content = content[:start] + new_phase_handlers + content[end:]
        return updated_content
    else:
        logger.warning("Could not find phase handlers code")
        return content

def update_enforce_unified_pathway(content: str) -> str:
    """Update the code that enforces the unified pathway."""
    
    # Define the new implementation
    new_enforce_unified_pathway = """
                # Validate the transition against the unified pathway
                if not LessonPhase.validate_transition(current_phase, new_phase):
                    # Invalid transition - force to valid next phase in the unified pathway
                    valid_next_phase = LessonPhase.get_next_phase(current_phase, state_updates_from_ai)
                    logger.warning(f"[{request_id}] 🔧 ENFORCING UNIFIED PATHWAY: Correcting {new_phase} → {valid_next_phase}")
                    state_updates_from_ai['current_phase'] = valid_next_phase
                    new_phase = valid_next_phase
                    
                    # Log enforcement of unified pathway
                    logger.info(f"[{request_id}] 🔒 UNIFIED PATHWAY ENFORCED: {current_phase} → {valid_next_phase}")
                    logger.info(f"[{request_id}] 🔒 Alternative paths are not permitted in the unified lesson flow")
    """
    
    # Find the current implementation
    pattern = r'# Validate the transition against the unified pathway.*?logger\.info\(f"\[\{request_id\}\] 🔒 Alternative paths are not permitted in the unified lesson flow"\)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # Replace the current implementation with the new one
        start, end = match.span()
        updated_content = content[:start] + new_enforce_unified_pathway + content[end:]
        return updated_content
    else:
        logger.warning("Could not find enforce unified pathway code")
        return content

def main():
    """Main function to remove alternative lesson flow paths."""
    logger.info("Starting removal of alternative lesson flow paths")
    
    # Read the main.py file
    main_py_content = read_file(MAIN_PY_PATH)
    if not main_py_content:
        logger.error(f"Could not read {MAIN_PY_PATH}")
        return
    
    # Update the _normalize_phase_name function
    logger.info("Updating _normalize_phase_name function")
    main_py_content = update_normalize_phase_name(main_py_content)
    
    # Update the VALID_TRANSITIONS dictionary
    logger.info("Updating VALID_TRANSITIONS dictionary")
    main_py_content = update_valid_transitions(main_py_content)
    
    # Update the _is_valid_phase_transition method
    logger.info("Updating _is_valid_phase_transition method")
    main_py_content = update_is_valid_phase_transition(main_py_content)
    
    # Update alternative lesson retrieval paths
    logger.info("Updating alternative lesson retrieval paths")
    main_py_content = update_alternative_paths(main_py_content)
    
    # Update phase handlers
    logger.info("Updating phase handlers")
    main_py_content = update_phase_handlers(main_py_content)
    
    # Update enforce unified pathway code
    logger.info("Updating enforce unified pathway code")
    main_py_content = update_enforce_unified_pathway(main_py_content)
    
    # Write the updated main.py file
    logger.info(f"Writing updated {MAIN_PY_PATH}")
    if not write_file(MAIN_PY_PATH, main_py_content):
        logger.error(f"Could not write to {MAIN_PY_PATH}")
        return
    
    # Read the phase_transition_integrity.py file
    phase_transition_integrity_content = read_file(PHASE_TRANSITION_INTEGRITY_PATH)
    if not phase_transition_integrity_content:
        logger.error(f"Could not read {PHASE_TRANSITION_INTEGRITY_PATH}")
        return
    
    # Update the VALID_TRANSITIONS dictionary in phase_transition_integrity.py
    logger.info("Updating VALID_TRANSITIONS dictionary in phase_transition_integrity.py")
    phase_transition_integrity_content = update_phase_transition_integrity(phase_transition_integrity_content)
    
    # Write the updated phase_transition_integrity.py file
    logger.info(f"Writing updated {PHASE_TRANSITION_INTEGRITY_PATH}")
    if not write_file(PHASE_TRANSITION_INTEGRITY_PATH, phase_transition_integrity_content):
        logger.error(f"Could not write to {PHASE_TRANSITION_INTEGRITY_PATH}")
        return
    
    logger.info("Successfully removed alternative lesson flow paths")

if __name__ == "__main__":
    main()