# -*- coding: utf-8 -*-
"""
Test Teaching Phase Handler with AI Handoff Detection

This module tests the teaching phase handler implementation for the unified lesson flow.
It verifies that the handler correctly manages teaching phases and detects AI handoff points.
"""

import unittest
import logging
import json
from datetime import datetime, timezone
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the modules to test
from teaching_phase_handler import TeachingPhaseHandler
from teaching_phase_integration import handle_teaching_phase, apply_teaching_guardrails, detect_ai_handoff

class TestTeachingPhaseHandler(unittest.TestCase):
    """Test cases for the teaching phase handler"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.handler = TeachingPhaseHandler()
        self.request_id = "test-request-123"
        
        # Sample session data
        self.session_data = {
            'session_id': 'test-session-123',
            'student_id': 'test-student-123',
            'lesson_ref': 'P5_MAT_180',
            'assigned_level_for_teaching': 5,
            'teaching_interactions': 10,
            'content_depth_score': 0.7,
            'teaching_complete': False,
            'current_phase': 'teaching',
            'objectives_tracking': {
                'completion_percentage': 90.0,
                'covered_objectives': 9,
                'total_objectives': 10
            }
        }
        
        # Sample context
        self.context = {
            'topic': 'Fractions',
            'grade': 'Primary 5',
            'teaching_level': 5,
            'learning_objectives': [
                'Understand the concept of fractions',
                'Add and subtract fractions with like denominators',
                'Compare fractions with different denominators'
            ]
        }
    
    def test_teaching_start_handler(self):
        """Test the teaching_start phase handler"""
        # Test initial teaching_start
        next_phase, state_updates = self.handler.handle_teaching_start(
            {}, "Let's start learning", self.context, self.request_id
        )
        
        # Verify transition to teaching phase
        self.assertEqual(next_phase, 'teaching')
        self.assertEqual(state_updates['current_phase'], 'teaching')
        self.assertIn('teaching_start_time', state_updates)
        self.assertEqual(state_updates['teaching_interactions'], 0)
        
        # Test with existing teaching_start_time
        next_phase, state_updates = self.handler.handle_teaching_start(
            {'teaching_start_time': '2025-07-22T10:00:00Z'},
            "Let's continue learning",
            self.context,
            self.request_id
        )
        
        # Verify transition to teaching phase
        self.assertEqual(next_phase, 'teaching')
        self.assertEqual(state_updates['current_phase'], 'teaching')
    
    def test_teaching_handler_no_handoff(self):
        """Test the teaching phase handler without handoff"""
        ai_response = "Fractions represent parts of a whole. For example, 1/4 means one part out of four equal parts."
        
        next_phase, state_updates = self.handler.handle_teaching(
            self.session_data,
            "Tell me about fractions",
            ai_response,
            self.context,
            self.request_id
        )
        
        # Verify staying in teaching phase
        self.assertEqual(next_phase, 'teaching')
        self.assertEqual(state_updates['current_phase'], 'teaching')
        self.assertEqual(state_updates['teaching_interactions'], 11)  # Incremented
        self.assertIn('objectives_tracking', state_updates)
    
    def test_teaching_handler_with_handoff(self):
        """Test the teaching phase handler with handoff detection"""
        # Set teaching as complete
        session_data = self.session_data.copy()
        session_data['teaching_complete'] = True
        
        # AI response with handoff indicator
        ai_response = "Now that we've covered fractions thoroughly, let's test your understanding with a quiz."
        
        next_phase, state_updates = self.handler.handle_teaching(
            session_data,
            "I understand fractions now",
            ai_response,
            self.context,
            self.request_id
        )
        
        # Verify transition to quiz_initiate phase
        self.assertEqual(next_phase, 'quiz_initiate')
        self.assertTrue(state_updates['handoff_detected'])
        self.assertIn('handoff_timestamp', state_updates)
    
    def test_handoff_detection(self):
        """Test handoff indicator detection"""
        # Test with handoff indicator
        ai_response_with_handoff = "Great job! Now let's start the quiz to test your understanding."
        self.assertTrue(self.handler._detect_handoff_indicators(ai_response_with_handoff))
        
        # Test with quiz pattern
        ai_response_with_quiz = "Question 1: What is 1/4 + 2/4?"
        self.assertTrue(self.handler._detect_handoff_indicators(ai_response_with_quiz))
        
        # Test without handoff indicator
        ai_response_without_handoff = "Let me explain more about fractions. A fraction has a numerator and denominator."
        self.assertFalse(self.handler._detect_handoff_indicators(ai_response_without_handoff))
    
    def test_integration_function(self):
        """Test the integration function"""
        # Test teaching_start phase
        next_phase, state_updates = handle_teaching_phase(
            'teaching_start',
            {},
            "Let's start learning",
            "",
            self.context,
            self.request_id
        )
        
        # Verify transition to teaching phase
        self.assertEqual(next_phase, 'teaching')
        
        # Test teaching phase with handoff
        session_data = self.session_data.copy()
        session_data['teaching_complete'] = True
        ai_response = "Now let's begin the quiz to test your knowledge."
        
        next_phase, state_updates = handle_teaching_phase(
            'teaching',
            session_data,
            "I'm ready",
            ai_response,
            self.context,
            self.request_id
        )
        
        # Verify transition to quiz_initiate phase
        self.assertEqual(next_phase, 'quiz_initiate')

if __name__ == '__main__':
    unittest.main()