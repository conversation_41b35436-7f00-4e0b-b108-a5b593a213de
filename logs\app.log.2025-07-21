2025-07-21 08:22:59,827 - INFO - [main.py:71] - ============================================================
2025-07-21 08:22:59,833 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-21 08:22:59,834 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-21 08:22:59,835 - INFO - [main.py:74] - Platform: win32
2025-07-21 08:22:59,835 - INFO - [main.py:75] - ============================================================
2025-07-21 08:23:03,718 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-21 08:23:03,786 - INFO - [teaching_rules.py:46] - 🎓 Teaching Rules Engine initialized with comprehensive validation
2025-07-21 08:23:04,299 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-21 08:23:04,299 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-21 08:23:04,300 - INFO - [main.py:634] - ================================================================================
2025-07-21 08:23:04,301 - INFO - [main.py:635] - LESSON MANAGER BACKEND STARTING UP
2025-07-21 08:23:04,301 - INFO - [main.py:636] - ================================================================================
2025-07-21 08:23:04,301 - INFO - [main.py:637] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-21 08:23:04,302 - INFO - [main.py:638] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-21 08:23:04,302 - INFO - [main.py:639] - Log level: DEBUG
2025-07-21 08:23:04,302 - INFO - [main.py:640] - ================================================================================
2025-07-21 08:23:04,303 - INFO - [main.py:642] - Logging configuration complete with immediate console output
2025-07-21 08:23:04,303 - INFO - [main.py:643] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-21 08:23:04,306 - INFO - [main.py:1340] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-21 08:23:04,306 - INFO - [main.py:1347] - [OK] Enhanced state and auth managers initialized successfully
2025-07-21 08:23:04,309 - INFO - [main.py:1579] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-21 08:23:04,324 - INFO - [main.py:1608] - Phase transition fixes imported successfully
2025-07-21 08:23:04,327 - INFO - [main.py:5039] - Successfully imported utils functions
2025-07-21 08:23:04,329 - INFO - [main.py:5047] - Successfully imported extract_ai_state functions
2025-07-21 08:23:04,340 - INFO - [main.py:5497] - FLASK: Using unified Firebase initialization approach...
2025-07-21 08:23:04,344 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-21 08:23:04,344 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-21 08:23:04,344 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-21 08:23:04,344 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-21 08:23:04,428 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-21 08:23:04,428 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-21 08:23:05,067 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-21 08:23:05,623 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-21 08:23:06,463 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-21 08:23:06,464 - INFO - [main.py:5505] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-21 08:23:06,465 - INFO - [main.py:5595] - Gemini API will be initialized on first use (lazy loading).
2025-07-21 08:23:06,477 - INFO - [main.py:19582] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-21 08:23:06,478 - INFO - [main.py:19625] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-21 08:23:06,490 - INFO - [main.py:2067] - Successfully imported timetable_generator functions
2025-07-21 08:23:06,507 - INFO - [main.py:25618] - Starting Lesson Manager Service...
2025-07-21 08:23:06,541 - INFO - [main.py:25624] - ENHANCED: Debug mode enabled for better console logging
2025-07-21 08:23:06,541 - INFO - [main.py:25626] - Flask server starting on host 0.0.0.0, port 5000
2025-07-21 08:23:06,541 - INFO - [main.py:25627] - Debug mode: ON
2025-07-21 08:23:06,542 - INFO - [main.py:25630] - ================================================================================
2025-07-21 08:23:06,542 - INFO - [main.py:25631] - LESSON MANAGER BACKEND READY TO START
2025-07-21 08:23:06,542 - INFO - [main.py:25632] - ================================================================================
2025-07-21 08:23:06,543 - INFO - [main.py:25633] - Server: 0.0.0.0:5000
2025-07-21 08:23:06,543 - INFO - [main.py:25634] - Debug mode: ON
2025-07-21 08:23:06,543 - INFO - [main.py:25635] - Firebase initialized: False
2025-07-21 08:23:06,544 - INFO - [main.py:25636] - Database available: True
2025-07-21 08:23:06,544 - INFO - [main.py:25637] - Health check: http://localhost:5000/api/health
2025-07-21 08:23:06,544 - INFO - [main.py:25638] - Enhance content: http://localhost:5000/api/enhance-content
2025-07-21 08:23:06,544 - INFO - [main.py:25639] - ================================================================================
2025-07-21 08:23:06,545 - INFO - [main.py:25644] - CONSOLE: Starting Flask development server...
2025-07-21 08:23:06,638 - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-21 08:23:06,639 - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-07-21 08:33:28,380 - INFO - [main.py:71] - ============================================================
2025-07-21 08:33:28,381 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-21 08:33:28,381 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-21 08:33:28,381 - INFO - [main.py:74] - Platform: win32
2025-07-21 08:33:28,381 - INFO - [main.py:75] - ============================================================
2025-07-21 08:33:30,225 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-21 08:33:30,252 - INFO - [teaching_rules.py:46] - 🎓 Teaching Rules Engine initialized with comprehensive validation
2025-07-21 08:33:30,406 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-21 08:33:30,406 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-21 08:33:30,408 - INFO - [main.py:634] - ================================================================================
2025-07-21 08:33:30,408 - INFO - [main.py:635] - LESSON MANAGER BACKEND STARTING UP
2025-07-21 08:33:30,408 - INFO - [main.py:636] - ================================================================================
2025-07-21 08:33:30,408 - INFO - [main.py:637] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-21 08:33:30,409 - INFO - [main.py:638] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-21 08:33:30,409 - INFO - [main.py:639] - Log level: DEBUG
2025-07-21 08:33:30,409 - INFO - [main.py:640] - ================================================================================
2025-07-21 08:33:30,409 - INFO - [main.py:642] - Logging configuration complete with immediate console output
2025-07-21 08:33:30,409 - INFO - [main.py:643] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-21 08:33:30,411 - INFO - [main.py:1340] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-21 08:33:30,411 - INFO - [main.py:1347] - [OK] Enhanced state and auth managers initialized successfully
2025-07-21 08:33:30,414 - INFO - [main.py:1579] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-21 08:33:30,420 - INFO - [main.py:1608] - Phase transition fixes imported successfully
2025-07-21 08:33:30,423 - INFO - [main.py:5039] - Successfully imported utils functions
2025-07-21 08:33:30,426 - INFO - [main.py:5047] - Successfully imported extract_ai_state functions
2025-07-21 08:33:30,432 - INFO - [main.py:5497] - FLASK: Using unified Firebase initialization approach...
2025-07-21 08:33:30,434 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-21 08:33:30,434 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-21 08:33:30,434 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-21 08:33:30,435 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-21 08:33:30,480 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-21 08:33:30,480 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-21 08:33:30,863 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-21 08:33:31,336 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-21 08:33:32,115 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-21 08:33:32,116 - INFO - [main.py:5505] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-21 08:33:32,117 - INFO - [main.py:5595] - Gemini API will be initialized on first use (lazy loading).
2025-07-21 08:33:32,132 - INFO - [main.py:19582] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-21 08:33:32,133 - INFO - [main.py:19625] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-21 08:33:32,143 - INFO - [main.py:2067] - Successfully imported timetable_generator functions
2025-07-21 08:33:32,148 - INFO - [main.py:25618] - Starting Lesson Manager Service...
2025-07-21 08:33:32,149 - INFO - [main.py:25624] - ENHANCED: Debug mode enabled for better console logging
2025-07-21 08:33:32,149 - INFO - [main.py:25626] - Flask server starting on host 0.0.0.0, port 5000
2025-07-21 08:33:32,149 - INFO - [main.py:25627] - Debug mode: ON
2025-07-21 08:33:32,150 - INFO - [main.py:25630] - ================================================================================
2025-07-21 08:33:32,150 - INFO - [main.py:25631] - LESSON MANAGER BACKEND READY TO START
2025-07-21 08:33:32,150 - INFO - [main.py:25632] - ================================================================================
2025-07-21 08:33:32,151 - INFO - [main.py:25633] - Server: 0.0.0.0:5000
2025-07-21 08:33:32,151 - INFO - [main.py:25634] - Debug mode: ON
2025-07-21 08:33:32,151 - INFO - [main.py:25635] - Firebase initialized: False
2025-07-21 08:33:32,152 - INFO - [main.py:25636] - Database available: True
2025-07-21 08:33:32,152 - INFO - [main.py:25637] - Health check: http://localhost:5000/api/health
2025-07-21 08:33:32,152 - INFO - [main.py:25638] - Enhance content: http://localhost:5000/api/enhance-content
2025-07-21 08:33:32,153 - INFO - [main.py:25639] - ================================================================================
2025-07-21 08:33:32,153 - INFO - [main.py:25644] - CONSOLE: Starting Flask development server...
2025-07-21 08:33:32,194 - INFO - [_internal.py:97] - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-21 08:33:32,195 - INFO - [_internal.py:97] - [33mPress CTRL+C to quit[0m
2025-07-21 10:29:14,733 - INFO - [main.py:7115] - Incoming request: {"request_id": "471f2e73-6abf-4da9-87b9-89c1c109d881", "timestamp": "2025-07-21T09:29:14.725597+00:00", "method": "GET", "path": "/api/simple-test", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown"}
2025-07-21 10:29:14,760 - INFO - [main.py:19579] - 🧪 SIMPLE TEST ENDPOINT CALLED
2025-07-21 10:29:14,813 - INFO - [_internal.py:97] - 127.0.0.1 - - [21/Jul/2025 10:29:14] "GET /api/simple-test HTTP/1.1" 200 -
2025-07-21 10:29:16,880 - INFO - [main.py:7115] - Incoming request: {"request_id": "00a40a21-4c37-40f9-8212-9ef330415f6e", "timestamp": "2025-07-21T09:29:16.877213+00:00", "method": "POST", "path": "/api/auth/student-login", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "password": "[REDACTED]"}}
2025-07-21 10:29:16,886 - INFO - [_internal.py:97] - 127.0.0.1 - - [21/Jul/2025 10:29:16] "[33mPOST /api/auth/student-login HTTP/1.1[0m" 404 -
2025-07-21 11:08:36,836 - INFO - [main.py:7115] - Incoming request: {"request_id": "f8f85d8e-7d29-4aa3-884e-68435a1b2636", "timestamp": "2025-07-21T10:08:36.834615+00:00", "method": "GET", "path": "/api/health", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown"}
2025-07-21 11:08:36,854 - INFO - [main.py:3618] - 🎯 SMART_DIAGNOSTIC: get_smart_diagnostic_config called with grade='PRIMARY-5'
2025-07-21 11:08:36,857 - INFO - [main.py:3627] - 🎯 SMART_DIAGNOSTIC: Normalized grade = 'PRIMARY-5'
2025-07-21 11:08:36,863 - INFO - [main.py:3690] - 🎯 SMART_DIAGNOSTIC: Configuration = {'grade': 'PRIMARY-5', 'q1_level': 1.5, 'q2_level': 5, 'q3_level': 7, 'expected_teaching_level': 5, 'realistic_range': {'low': 1, 'high': 7}, 'nigeria_optimized': True, 'universal_foundation_check': True}
2025-07-21 11:08:36,904 - INFO - [_internal.py:97] - 127.0.0.1 - - [21/Jul/2025 11:08:36] "GET /api/health HTTP/1.1" 200 -
2025-07-21 11:08:38,950 - INFO - [main.py:7115] - Incoming request: {"request_id": "6258b954-0e03-40f2-b03b-575d19c851f5", "timestamp": "2025-07-21T10:08:38.949692+00:00", "method": "POST", "path": "/api/start-lesson", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "test_student_enhanced_1753092514", "lesson_id": "P5_MAT_180", "grade": "primary_5", "subject": "mathematics"}}
2025-07-21 11:08:38,954 - INFO - [_internal.py:97] - 127.0.0.1 - - [21/Jul/2025 11:08:38] "[33mPOST /api/start-lesson HTTP/1.1[0m" 404 -
2025-07-21 11:10:16,549 - INFO - [main.py:7115] - Incoming request: {"request_id": "05101dda-8748-47ce-907c-40ff9dfaad03", "timestamp": "2025-07-21T10:10:16.549263+00:00", "method": "POST", "path": "/api/lesson-content", "ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652", "user_id": "anonymous", "role": "unknown", "body": {"test": "data"}}
2025-07-21 11:10:16,553 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/lesson-content
2025-07-21 11:10:16,555 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'User-Agent': 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652', 'Content-Type': 'application/json', 'Host': 'localhost:5000', 'Content-Length': '16', 'Connection': 'Keep-Alive'}
2025-07-21 11:10:16,555 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 05101dda-8748-47ce-907c-40ff9dfaad03
2025-07-21 11:10:16,556 - INFO - [auth_decorator.py:74] - [05101dda-8748-47ce-907c-40ff9dfaad03][require_auth] Decorator invoked for path: /api/lesson-content
2025-07-21 11:10:16,558 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-21 11:10:16,560 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-21 11:10:16,561 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-21 11:10:16,562 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-21 11:10:16,563 - INFO - [auth_decorator.py:95] - [05101dda-8748-47ce-907c-40ff9dfaad03][require_auth] Development mode detected - bypassing authentication
2025-07-21 11:10:16,566 - INFO - [auth_decorator.py:137] - 🔒 DEVELOPMENT: Generated dynamic test student_id: test_student_1753092616
2025-07-21 11:10:16,567 - INFO - [auth_decorator.py:140] - [05101dda-8748-47ce-907c-40ff9dfaad03][require_auth] Generated dynamic test student_id: test_student_1753092616
2025-07-21 11:10:27,735 - INFO - [main.py:71] - ============================================================
2025-07-21 11:10:27,735 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-21 11:10:27,736 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-21 11:10:27,736 - INFO - [main.py:74] - Platform: win32
2025-07-21 11:10:27,736 - INFO - [main.py:75] - ============================================================
2025-07-21 11:10:27,755 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-21 11:10:27,755 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-21 11:10:27,759 - INFO - [main.py:634] - ================================================================================
2025-07-21 11:10:27,759 - INFO - [main.py:635] - LESSON MANAGER BACKEND STARTING UP
2025-07-21 11:10:27,759 - INFO - [main.py:636] - ================================================================================
2025-07-21 11:10:27,760 - INFO - [main.py:637] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-21 11:10:27,760 - INFO - [main.py:638] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-21 11:10:27,760 - INFO - [main.py:639] - Log level: DEBUG
2025-07-21 11:10:27,760 - INFO - [main.py:640] - ================================================================================
2025-07-21 11:10:27,761 - INFO - [main.py:642] - Logging configuration complete with immediate console output
2025-07-21 11:10:27,761 - INFO - [main.py:643] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-21 11:10:27,780 - INFO - [main.py:1340] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-21 11:10:27,782 - INFO - [main.py:1347] - [OK] Enhanced state and auth managers initialized successfully
2025-07-21 11:10:27,786 - INFO - [main.py:1579] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-21 11:10:27,786 - INFO - [main.py:1608] - Phase transition fixes imported successfully
2025-07-21 11:10:27,791 - INFO - [main.py:5039] - Successfully imported utils functions
2025-07-21 11:10:27,792 - INFO - [main.py:5047] - Successfully imported extract_ai_state functions
2025-07-21 11:10:27,796 - INFO - [main.py:5497] - FLASK: Using unified Firebase initialization approach...
2025-07-21 11:10:27,797 - INFO - [unified_firebase_init.py:42] - Firebase already initialized
2025-07-21 11:10:27,797 - INFO - [main.py:5505] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-21 11:10:27,797 - INFO - [main.py:5595] - Gemini API will be initialized on first use (lazy loading).
2025-07-21 11:10:27,813 - INFO - [main.py:19682] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-21 11:10:27,814 - INFO - [main.py:19725] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-21 11:10:27,825 - INFO - [main.py:2067] - Successfully imported timetable_generator functions
2025-07-21 11:10:27,837 - INFO - [main.py:25819] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-21 11:10:27,953 - INFO - [main.py:25822] - Google Cloud Storage client initialized successfully.
2025-07-21 11:10:28,506 - DEBUG - [connectionpool.py:291] - Resetting dropped connection: oauth2.googleapis.com
2025-07-21 11:10:28,975 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-21 11:10:29,329 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=test_student_1753092616, name=Test Student
2025-07-21 11:10:29,330 - INFO - [auth_decorator.py:164] - [05101dda-8748-47ce-907c-40ff9dfaad03][require_auth] Development auth: uid=test_student_1753092616, name=Test Student
2025-07-21 11:10:29,336 - INFO - [main.py:18893] - [05101dda-8748-47ce-907c-40ff9dfaad03] Routing /api/lesson-content to lesson_content_and_quiz function.
2025-07-21 11:10:29,338 - INFO - [auth_decorator.py:68] - 🔒 AUTH DECORATOR CALLED for /api/lesson-content
2025-07-21 11:10:29,340 - INFO - [auth_decorator.py:69] - 🔒 Headers: {'User-Agent': 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652', 'Content-Type': 'application/json', 'Host': 'localhost:5000', 'Content-Length': '16', 'Connection': 'Keep-Alive'}
2025-07-21 11:10:29,343 - INFO - [auth_decorator.py:70] - 🔒 Request ID: 05101dda-8748-47ce-907c-40ff9dfaad03
2025-07-21 11:10:29,345 - INFO - [auth_decorator.py:74] - [05101dda-8748-47ce-907c-40ff9dfaad03][require_auth] Decorator invoked for path: /api/lesson-content
2025-07-21 11:10:29,346 - INFO - [auth_decorator.py:88] - 🔒 DEVELOPMENT MODE - bypassing authentication
2025-07-21 11:10:29,347 - INFO - [auth_decorator.py:89] - 🔒 Environment: FLASK_ENV=development, NODE_ENV=None
2025-07-21 11:10:29,351 - INFO - [auth_decorator.py:90] - 🔒 Firebase initialized: True
2025-07-21 11:10:29,352 - INFO - [auth_decorator.py:91] - 🔒 Testing header: None
2025-07-21 11:10:29,353 - INFO - [auth_decorator.py:95] - [05101dda-8748-47ce-907c-40ff9dfaad03][require_auth] Development mode detected - bypassing authentication
2025-07-21 11:10:29,354 - INFO - [auth_decorator.py:137] - 🔒 DEVELOPMENT: Generated dynamic test student_id: test_student_1753092629
2025-07-21 11:10:29,354 - INFO - [auth_decorator.py:140] - [05101dda-8748-47ce-907c-40ff9dfaad03][require_auth] Generated dynamic test student_id: test_student_1753092629
2025-07-21 11:10:29,814 - INFO - [auth_decorator.py:160] - 🔒 DEVELOPMENT: Using student_id=test_student_1753092629, name=Test Student
2025-07-21 11:10:29,815 - INFO - [auth_decorator.py:164] - [05101dda-8748-47ce-907c-40ff9dfaad03][require_auth] Development auth: uid=test_student_1753092629, name=Test Student
2025-07-21 11:10:30,069 - INFO - [_internal.py:97] - 127.0.0.1 - - [21/Jul/2025 11:10:30] "[35m[1mPOST /api/lesson-content HTTP/1.1[0m" 500 -
