# Legacy Code Paths Audit

This document identifies alternative lesson flow implementations that need to be removed or refactored to support the unified pathway sequence.

## 1. Legacy Phase Normalization

The `_normalize_phase_name` function in `main.py` maps legacy phase names to the unified pathway phases. This function should be updated to ensure all legacy phases are properly mapped to the unified pathway.

```python
def _normalize_phase_name(cls, phase):
    """
    Normalize legacy phase names to match unified pathway.
    
    Args:
        phase: The phase name to normalize
        
    Returns:
        str: The normalized phase name
    """
    if not phase:
        return cls.TEACHING_START
        
    # Map legacy diagnostic phases to teaching_start
    if phase.startswith('diagnostic_') or phase.startswith('smart_diagnostic_'):
        return cls.TEACHING_START
        
    # Map legacy teaching phases to teaching
    if phase.startswith('teaching_') and phase != cls.TEACHING_START:
        return cls.TEACHING
        
    # Map legacy quiz phases
    if phase == 'quiz' or phase == 'quiz_start':
        return cls.QUIZ_INITIATE
        
    # Map legacy completion phases
    if phase == 'completed' or phase == 'conclusion_summary':
        return cls.COMPLETE
        
    return phase
```

## 2. Alternative Lesson Retrieval Paths

The code contains alternative paths for lesson retrieval that should be standardized:

```python
# Alternative paths to try if primary fails
alternative_paths = [
    (f"lessonRef/{lesson_id_clean}", "root_lessonRef_collection"),
    (f"lessons/{lesson_id_clean}", "legacy_lessons_collection"),
]
```

## 3. Legacy Phase Transitions

The `VALID_TRANSITIONS` dictionary in `LessonPhase` class defines the allowed phase transitions, but there are still legacy phase transitions being handled in various parts of the code:

```python
VALID_TRANSITIONS = {
    TEACHING_START: [TEACHING, QUIZ_INITIATE],
    TEACHING: [QUIZ_INITIATE],
    QUIZ_INITIATE: [QUIZ_QUESTIONS],
    QUIZ_QUESTIONS: [QUIZ_QUESTIONS, QUIZ_RESULTS],  # Allow staying in quiz_questions for multiple questions
    QUIZ_RESULTS: [FINAL_REPORT_INPROGRESS],
    FINAL_REPORT_INPROGRESS: [COMPLETE],
    COMPLETE: []  # Terminal state
}
```

## 4. Diagnostic Phase Handling

The code still handles legacy diagnostic phases that should be removed or refactored to align with the unified pathway:

```python
# Map legacy diagnostic phases to teaching_start
if phase.startswith('diagnostic_') or phase.startswith('smart_diagnostic_'):
    return cls.TEACHING_START
```

## 5. Phase Transition Validation

The `validate_transition` method in `LessonPhase` class validates phase transitions, but there are still legacy validation methods being used:

```python
def _is_valid_phase_transition(self, current_phase, new_phase):
    """Validate if phase transition is allowed"""
    valid_transitions = {
        LESSON_PHASE_DIAGNOSTIC: [LESSON_PHASE_TEACHING, LESSON_PHASE_QUIZ_QUESTIONS],
        # ...
    }
    
    if not current_phase:
        return True  # Allow any transition from None/empty
    
    return new_phase in valid_transitions.get(current_phase, [])
```

## 6. Phase Handlers

The code contains multiple phase handlers that should be consolidated into the unified pathway handlers:

```python
# Route to appropriate phase handler based on unified pathway
if current_phase in [LessonPhase.TEACHING_START, LessonPhase.TEACHING]:
    logger.info(f"[{request_id}] 🎯 ROUTING: Teaching phase handler")
    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_teaching_phase(
        user_query, chat_history, context_for_enhance, session_state_data, request_id
    )
elif current_phase == LessonPhase.QUIZ_INITIATE:
    logger.info(f"[{request_id}] 🎯 ROUTING: Quiz initiate phase handler")
    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_quiz_initiate_phase(
        user_query, chat_history, context_for_enhance, session_state_data, request_id
    )
# ...
```

## 7. Phase Transition Integrity Module

The `phase_transition_integrity.py` module contains a `VALID_TRANSITIONS` dictionary that should be aligned with the unified pathway:

```python
# Valid phase transition mappings
VALID_TRANSITIONS = {
    # Diagnostic phases
    'smart_diagnostic_start': ['smart_diagnostic_q1', 'teaching_start'],
    'smart_diagnostic_q1': ['smart_diagnostic_q2'],
    # ...
}
```

## 8. Frontend Phase Handling

The frontend code in `ClassroomContent.tsx` contains logic for handling phase transitions that should be updated to trust the backend completely:

```typescript
// CRITICAL REFACTOR: Simplified phase handling that trusts backend completely
// Backend now returns {success: true, data: {enhanced_content, state_updates, current_phase, ...}}
let actualResponseData = response;

// Check if this is the standardized response format
if (response?.success === true && response?.data) {
    console.log('[handleLessonPhaseUpdates] 🎯 DETECTED STANDARDIZED RESPONSE FORMAT');
    actualResponseData = response.data; // Extract the actual lesson data from the wrapper
}

// Extract phase information with clear priority:
// 1. state_updates.new_phase (highest priority)
// 2. current_phase from response data
const serverStateUpdates = actualResponseData?.state_updates || actualResponseData?.parsed_state;
let phaseFromServer = actualResponseData?.current_phase; // Direct from response data
```

## Recommendations

1. Consolidate all phase transition logic into the `LessonPhase` class
2. Remove legacy phase normalization and use only unified pathway phases
3. Update all phase handlers to support only the unified pathway sequence
4. Align the `VALID_TRANSITIONS` dictionaries across all modules
5. Update frontend code to trust backend phase transitions completely
6. Remove alternative lesson retrieval paths and standardize on a single approach
7. Remove legacy diagnostic phase handling and use only the unified pathway