#!/usr/bin/env python3
"""
Test for AI instructor role boundaries enforcement.

This test verifies that the AI instructor role is properly limited to teaching phases
and that handoff to backend occurs correctly at quiz_initiate phase.
"""

import unittest
from teaching_rules import enforce_ai_role_boundaries

class TestAIInstructorRoleBoundaries(unittest.TestCase):
    """Test cases for AI instructor role boundaries enforcement."""
    
    def test_teaching_phase_valid(self):
        """Test that valid teaching content in teaching phase is allowed."""
        current_phase = "teaching"
        ai_response = "Let me explain the concept of photosynthesis. Plants use sunlight to convert carbon dioxide and water into glucose and oxygen."
        
        is_valid, reason = enforce_ai_role_boundaries(current_phase, ai_response)
        
        self.assertTrue(is_valid)
        self.assertEqual(reason, "role_boundaries_respected")
        
    def test_teaching_phase_with_quiz_content(self):
        """Test that quiz content in teaching phase is rejected."""
        current_phase = "teaching"
        ai_response = "Now let's test your knowledge. Question 1: Which of the following is a product of photosynthesis? a) Carbon dioxide b) Oxygen c) Water d) Nitrogen"
        
        is_valid, reason = enforce_ai_role_boundaries(current_phase, ai_response)
        
        self.assertFalse(is_valid)
        self.assertEqual(reason, "premature_quiz_content_generation")
        
    def test_quiz_initiate_with_proper_handoff(self):
        """Test that proper handoff message in quiz_initiate phase is allowed."""
        current_phase = "quiz_initiate"
        ai_response = "Excellent work! You've mastered the key concepts. I'm now handing you over to our assessment system for your quiz. Good luck! // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"quiz_initiate\", \"handoff_to_backend\": true} // AI_STATE_UPDATE_BLOCK_END"
        
        is_valid, reason = enforce_ai_role_boundaries(current_phase, ai_response)
        
        self.assertTrue(is_valid)
        self.assertEqual(reason, "role_boundaries_respected")
        
    def test_quiz_initiate_without_handoff(self):
        """Test that missing handoff message in quiz_initiate phase is rejected."""
        current_phase = "quiz_initiate"
        ai_response = "Let's start the quiz. Question 1: What is the capital of France? // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"quiz_questions\"} // AI_STATE_UPDATE_BLOCK_END"
        
        is_valid, reason = enforce_ai_role_boundaries(current_phase, ai_response)
        
        self.assertFalse(is_valid)
        self.assertEqual(reason, "missing_handoff_message")
        
    def test_backend_controlled_phase(self):
        """Test that AI attempting to operate in backend-controlled phase is rejected."""
        current_phase = "quiz_questions"
        ai_response = "Here's the next question: What is the formula for water?"
        
        is_valid, reason = enforce_ai_role_boundaries(current_phase, ai_response)
        
        self.assertFalse(is_valid)
        self.assertEqual(reason, "ai_role_boundary_violation_in_quiz_questions")
        
    def test_complete_phase(self):
        """Test that AI attempting to operate in complete phase is rejected."""
        current_phase = "complete"
        ai_response = "Congratulations on completing the lesson!"
        
        is_valid, reason = enforce_ai_role_boundaries(current_phase, ai_response)
        
        self.assertFalse(is_valid)
        self.assertEqual(reason, "ai_role_boundary_violation_in_complete")

if __name__ == "__main__":
    unittest.main()