# -*- coding: utf-8 -*-
"""
Phase Transition Integrity System

This module implements comprehensive phase transition validation, data preservation,
and state consistency management for the lesson flow system.

Task 7: Implement Phase Transition Integrity System
- Add phase transition validation to ensure appropriate flow progression
- Implement data preservation mechanisms during phase transitions
- Create state consistency validation for phase changes
- Add transition failure recovery with appropriate error logging
- Implement consolidated data flow for lesson completion
- Ensure all accumulated lesson data is preserved throughout phase transitions
"""

import logging
import time
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timezone
from dataclasses import dataclass, field
from enum import Enum
import json
import copy

logger = logging.getLogger(__name__)

class PhaseTransitionResult(Enum):
    """Results of phase transition validation"""
    VALID = "valid"
    INVALID = "invalid"
    BLOCKED = "blocked"
    CORRECTED = "corrected"
    RECOVERED = "recovered"

@dataclass
class TransitionValidationResult:
    """Result of phase transition validation"""
    result: PhaseTransitionResult
    from_phase: str
    to_phase: str
    corrected_phase: Optional[str] = None
    message: str = ""
    data_preserved: bool = True
    recovery_applied: bool = False
    validation_errors: List[str] = field(default_factory=list)
    preserved_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class LessonDataSnapshot:
    """Snapshot of lesson data at a specific point in time"""
    timestamp: datetime
    phase: str
    session_id: str
    student_id: str
    lesson_ref: str
    teaching_level: Optional[int] = None
    diagnostic_complete: bool = False
    teaching_interactions: int = 0
    quiz_interactions: int = 0
    quiz_complete: bool = False
    lesson_complete: bool = False
    quiz_questions: List[Dict] = field(default_factory=list)
    quiz_answers: List[Dict] = field(default_factory=list)
    concepts_covered: List[str] = field(default_factory=list)
    learning_objectives_met: List[str] = field(default_factory=list)
    performance_data: Dict[str, Any] = field(default_factory=dict)
    raw_state_data: Dict[str, Any] = field(default_factory=dict)

class PhaseTransitionIntegrityManager:
    """
    Manages phase transition integrity, validation, and data preservation
    """
    
    # Valid phase transition mappings
    VALID_TRANSITIONS = {
        # Diagnostic phases
        'smart_diagnostic_start': ['smart_diagnostic_q1'], # FIX: Only allows progression to the first question
        'smart_diagnostic_q1': ['smart_diagnostic_q2'],
        'smart_diagnostic_q2': ['smart_diagnostic_q3'],
        'smart_diagnostic_q3': ['smart_diagnostic_q4'],
        'smart_diagnostic_q4': ['smart_diagnostic_q5'],
        'smart_diagnostic_q5': ['teaching_start'],
        
        # Legacy diagnostic phases (for backward compatibility)
        'diagnostic_start_probe': ['diagnostic_probing_L1_ask_q1', 'diagnostic_probing_L2_ask_q1', 
                                  'diagnostic_probing_L3_ask_q1', 'diagnostic_probing_L4_ask_q1',
                                  'diagnostic_probing_L5_ask_q1', 'teaching_start'],
        
        # Teaching phases
        'teaching_start': ['teaching', 'quiz_initiate'],
        'teaching': ['quiz_initiate', 'teaching_start'],
        
        # Quiz phases
        'quiz_initiate': ['quiz_questions'],
        'quiz_questions': ['quiz_results', 'quiz_questions'],  # Allow staying in quiz_questions
        'quiz_results': ['conclusion_summary', 'completed'],
        
        # Completion phases
        'conclusion_summary': ['completed'],
        'completed': ['completed'],  # Allow staying in completed
        
        # Special transitions for recovery
        'error_recovery': ['smart_diagnostic_start', 'teaching_start', 'quiz_initiate']
    }
    
    # Phases that require specific data to be present (made more flexible)
    PHASE_DATA_REQUIREMENTS = {
        'teaching_start': ['assigned_level_for_teaching'],  # Reduced requirements
        'teaching': ['assigned_level_for_teaching'],
        'quiz_initiate': ['assigned_level_for_teaching'],  # Reduced requirements
        'quiz_questions': [],  # Made optional - can be recovered
        'quiz_results': [],  # Made optional - can be recovered
        'conclusion_summary': [],  # Made optional
        'completed': []  # Made optional
    }
    
    # Critical data fields that must be preserved across transitions
    CRITICAL_DATA_FIELDS = [
        'session_id', 'student_id', 'lesson_ref', 'current_phase',
        'diagnostic_complete', 'assigned_level_for_teaching',
        'teaching_interactions', 'quiz_interactions', 'teaching_complete',
        'quiz_complete', 'lesson_complete', 'quiz_questions_generated',
        'quiz_answers', 'concepts_covered', 'learning_objectives_met',
        'lesson_start_time', 'phase_history', 'performance_data'
    ]
    
    def __init__(self, db_client=None):
        """Initialize the Phase Transition Integrity Manager"""
        self.db = db_client
        self.data_snapshots: Dict[str, List[LessonDataSnapshot]] = {}
        self.transition_history: Dict[str, List[Dict]] = {}
        
    def validate_phase_transition(self, 
                                from_phase: str, 
                                to_phase: str, 
                                session_data: Dict[str, Any],
                                request_id: str = None) -> TransitionValidationResult:
        """
        Validate a phase transition and return detailed results
        
        Args:
            from_phase: Current phase
            to_phase: Proposed next phase
            session_data: Current session state data
            request_id: Request ID for logging
            
        Returns:
            TransitionValidationResult with validation outcome
        """
        request_id = request_id or f"transition-{int(time.time())}"
        
        logger.info(f"[{request_id}] 🔍 PHASE TRANSITION VALIDATION: {from_phase} → {to_phase}")
        
        # Create initial result
        result = TransitionValidationResult(
            result=PhaseTransitionResult.VALID,
            from_phase=from_phase,
            to_phase=to_phase,
            message="Transition validation in progress"
        )
        
        try:
            # Step 1: Basic transition validation
            if not self._is_valid_basic_transition(from_phase, to_phase):
                result.result = PhaseTransitionResult.INVALID
                result.message = f"Invalid transition: {from_phase} → {to_phase} not allowed"
                result.validation_errors.append(f"Transition {from_phase} → {to_phase} not in valid transitions map")
                logger.warning(f"[{request_id}] ❌ INVALID TRANSITION: {from_phase} → {to_phase}")
                return result
            
            # Step 2: Backward transition protection
            backward_check = self._check_backward_transition(from_phase, to_phase, session_data)
            if backward_check['is_backward']:
                result.result = PhaseTransitionResult.BLOCKED
                result.message = backward_check['message']
                result.validation_errors.append(backward_check['message'])
                logger.error(f"[{request_id}] 🚫 BACKWARD TRANSITION BLOCKED: {backward_check['message']}")
                return result
            
            # Step 3: Data requirements validation
            data_validation = self._validate_phase_data_requirements(to_phase, session_data)
            if not data_validation['valid']:
                # Try to recover missing data
                recovery_result = self._attempt_data_recovery(to_phase, session_data, request_id)
                if recovery_result['recovered']:
                    result.result = PhaseTransitionResult.RECOVERED
                    result.message = f"Transition allowed with data recovery: {recovery_result['message']}"
                    result.recovery_applied = True
                    result.preserved_data.update(recovery_result['recovered_data'])
                    logger.warning(f"[{request_id}] 🔧 DATA RECOVERY APPLIED: {recovery_result['message']}")
                else:
                    result.result = PhaseTransitionResult.INVALID
                    result.message = f"Missing required data for phase {to_phase}: {data_validation['missing_fields']}"
                    result.validation_errors.extend(data_validation['errors'])
                    logger.error(f"[{request_id}] ❌ DATA REQUIREMENTS NOT MET: {data_validation['message']}")
                    return result
            
            # Step 4: State consistency validation
            consistency_check = self._validate_state_consistency(from_phase, to_phase, session_data)

            # ============================ START OF FIX ============================
            # REMOVE the entire 'else' block that performs the flawed auto-correction.
            # REPLACE it with this new logic that correctly handles the inconsistency.

            if not consistency_check['consistent']:
                # If there are consistency errors, the transition is INVALID. Do not try to patch it.
                result.result = PhaseTransitionResult.INVALID
                result.message = f"State inconsistency cannot be resolved: {consistency_check['message']}"
                result.validation_errors.extend(consistency_check['errors'])
                logger.error(f"[{request_id}] ❌ STATE INCONSISTENCY: {consistency_check['message']}")
                # By returning here, we block the invalid transition and allow the recovery mechanism
                # in the main loop to correctly revert to the 'from_phase'.
                return result
            # ============================= END OF FIX =============================
            
            # Step 5: Final validation passed
            result.result = PhaseTransitionResult.VALID
            result.message = f"Valid transition: {from_phase} → {to_phase}"
            logger.info(f"[{request_id}] ✅ TRANSITION VALIDATED: {from_phase} → {to_phase}")
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ TRANSITION VALIDATION ERROR: {str(e)}")
            result.result = PhaseTransitionResult.INVALID
            result.message = f"Validation error: {str(e)}"
            result.validation_errors.append(f"Exception during validation: {str(e)}")
        
        return result
    
    def preserve_lesson_data(self, 
                           session_id: str, 
                           current_state: Dict[str, Any],
                           request_id: str = None) -> LessonDataSnapshot:
        """
        Create a snapshot of current lesson data for preservation
        
        Args:
            session_id: Session identifier
            current_state: Current session state
            request_id: Request ID for logging
            
        Returns:
            LessonDataSnapshot containing preserved data
        """
        request_id = request_id or f"preserve-{int(time.time())}"
        
        try:
            # Create comprehensive data snapshot
            snapshot = LessonDataSnapshot(
                timestamp=datetime.now(timezone.utc),
                phase=current_state.get('current_phase', 'unknown'),
                session_id=session_id,
                student_id=current_state.get('student_id', ''),
                lesson_ref=current_state.get('lesson_ref', ''),
                teaching_level=current_state.get('assigned_level_for_teaching'),
                diagnostic_complete=current_state.get('diagnostic_complete', False),
                teaching_interactions=current_state.get('teaching_interactions', 0),
                quiz_interactions=current_state.get('quiz_interactions', 0),
                quiz_complete=current_state.get('quiz_complete', False),
                lesson_complete=current_state.get('lesson_complete', False),
                quiz_questions=current_state.get('quiz_questions_generated', []),
                quiz_answers=current_state.get('quiz_answers', []),
                concepts_covered=current_state.get('concepts_covered', []),
                learning_objectives_met=current_state.get('learning_objectives_met', []),
                performance_data=current_state.get('performance_data', {}),
                raw_state_data=copy.deepcopy(current_state)
            )
            
            # Store snapshot
            if session_id not in self.data_snapshots:
                self.data_snapshots[session_id] = []
            self.data_snapshots[session_id].append(snapshot)
            
            # Keep only last 10 snapshots per session
            if len(self.data_snapshots[session_id]) > 10:
                self.data_snapshots[session_id] = self.data_snapshots[session_id][-10:]
            
            logger.info(f"[{request_id}] 📸 DATA SNAPSHOT CREATED: Session {session_id}, Phase {snapshot.phase}")
            return snapshot
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ DATA PRESERVATION ERROR: {str(e)}")
            # Return minimal snapshot on error
            return LessonDataSnapshot(
                timestamp=datetime.now(timezone.utc),
                phase=current_state.get('current_phase', 'error'),
                session_id=session_id,
                student_id=current_state.get('student_id', ''),
                lesson_ref=current_state.get('lesson_ref', ''),
                raw_state_data=current_state
            )
    
    def apply_transition_with_integrity(self,
                                      from_phase: str,
                                      to_phase: str,
                                      session_data: Dict[str, Any],
                                      state_updates: Dict[str, Any],
                                      request_id: str = None) -> Dict[str, Any]:
        """
        Apply phase transition with full integrity checks and data preservation
        
        Args:
            from_phase: Current phase
            to_phase: Target phase
            session_data: Current session state
            state_updates: Proposed state updates
            request_id: Request ID for logging
            
        Returns:
            Dict containing the final state updates with integrity applied
        """
        request_id = request_id or f"apply-{int(time.time())}"
        session_id = session_data.get('session_id', 'unknown')
        
        logger.info(f"[{request_id}] 🔄 APPLYING TRANSITION WITH INTEGRITY: {from_phase} → {to_phase}")
        
        try:
            # Step 1: Preserve current data
            snapshot = self.preserve_lesson_data(session_id, session_data, request_id)
            
            # Step 2: Create merged data for validation (current state + proposed updates)
            merged_data_for_validation = copy.deepcopy(session_data)
            merged_data_for_validation.update(state_updates)
            
            # DEBUG: Log what's in the merged data for teaching_start validation
            if to_phase == 'teaching_start':
                logger.warning(f"[{request_id}] 🔍 DEBUG MERGED DATA FOR TEACHING_START:")
                logger.warning(f"[{request_id}] 🔍   - assigned_level_for_teaching in session_data: {'assigned_level_for_teaching' in session_data}")
                logger.warning(f"[{request_id}] 🔍   - assigned_level_for_teaching in state_updates: {'assigned_level_for_teaching' in state_updates}")
                logger.warning(f"[{request_id}] 🔍   - assigned_level_for_teaching in merged_data: {'assigned_level_for_teaching' in merged_data_for_validation}")
                if 'assigned_level_for_teaching' in merged_data_for_validation:
                    logger.warning(f"[{request_id}] 🔍   - assigned_level_for_teaching value: {merged_data_for_validation['assigned_level_for_teaching']}")
                logger.warning(f"[{request_id}] 🔍   - state_updates keys: {list(state_updates.keys())}")
                logger.warning(f"[{request_id}] 🔍   - merged_data keys (first 20): {list(merged_data_for_validation.keys())[:20]}")
            
            # Step 3: Validate transition with merged data
            validation_result = self.validate_phase_transition(from_phase, to_phase, merged_data_for_validation, request_id)
            
            # Step 4: Handle validation result
            final_state_updates = copy.deepcopy(state_updates)
            
            if validation_result.result == PhaseTransitionResult.VALID:
                # Transition is valid - apply as requested
                final_state_updates['new_phase'] = to_phase
                logger.info(f"[{request_id}] ✅ TRANSITION APPLIED: {from_phase} → {to_phase}")
                
            elif validation_result.result == PhaseTransitionResult.CORRECTED:
                # Transition was corrected - use corrected phase
                corrected_phase = validation_result.corrected_phase
                final_state_updates['new_phase'] = corrected_phase
                final_state_updates.update(validation_result.preserved_data)
                logger.warning(f"[{request_id}] 🔧 TRANSITION CORRECTED: {to_phase} → {corrected_phase}")
                
            elif validation_result.result == PhaseTransitionResult.RECOVERED:
                # Data was recovered - apply with recovered data
                final_state_updates['new_phase'] = to_phase
                final_state_updates.update(validation_result.preserved_data)
                logger.warning(f"[{request_id}] 🔧 TRANSITION WITH RECOVERY: {from_phase} → {to_phase}")
                logger.warning(f"[{request_id}] 🔧 RECOVERED DATA: {validation_result.preserved_data}")
                
            elif validation_result.result == PhaseTransitionResult.BLOCKED:
                # Transition blocked - stay in current phase
                final_state_updates['new_phase'] = from_phase
                final_state_updates['transition_blocked'] = True
                final_state_updates['block_reason'] = validation_result.message
                logger.error(f"[{request_id}] 🚫 TRANSITION BLOCKED: Staying in {from_phase}")
                
            else:  # INVALID
                # Invalid transition - apply recovery
                recovery_phase = self._determine_recovery_phase(from_phase, session_data)
                final_state_updates['new_phase'] = recovery_phase
                final_state_updates['transition_recovered'] = True
                final_state_updates['recovery_reason'] = validation_result.message
                logger.error(f"[{request_id}] 🔧 TRANSITION RECOVERY: {from_phase} → {recovery_phase}")
            
            # Step 4: Ensure critical data preservation
            final_state_updates = self._ensure_critical_data_preservation(
                final_state_updates, session_data, snapshot, request_id
            )
            
            # Step 5: Record transition in history
            self._record_transition_history(session_id, from_phase, 
                                          final_state_updates.get('new_phase'), 
                                          validation_result, request_id)
            
            # Step 6: Add integrity metadata
            final_state_updates['transition_integrity_applied'] = True
            final_state_updates['transition_timestamp'] = datetime.now(timezone.utc).isoformat()
            final_state_updates['validation_result'] = validation_result.result.value
            
            logger.info(f"[{request_id}] ✅ TRANSITION INTEGRITY COMPLETE: Final phase = {final_state_updates.get('new_phase')}")
            return final_state_updates
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ TRANSITION INTEGRITY ERROR: {str(e)}")
            # Return safe fallback
            safe_updates = copy.deepcopy(state_updates)
            safe_updates['new_phase'] = from_phase  # Stay in current phase on error
            safe_updates['integrity_error'] = str(e)
            return safe_updates
    
    def get_consolidated_lesson_data(self, 
                                   session_id: str,
                                   request_id: str = None) -> Dict[str, Any]:
        """
        Get consolidated lesson data for completion processing
        
        Args:
            session_id: Session identifier
            request_id: Request ID for logging
            
        Returns:
            Dict containing consolidated lesson data
        """
        request_id = request_id or f"consolidate-{int(time.time())}"
        
        try:
            # Get all snapshots for this session
            snapshots = self.data_snapshots.get(session_id, [])
            if not snapshots:
                logger.warning(f"[{request_id}] ⚠️ NO SNAPSHOTS FOUND for session {session_id}")
                return {}
            
            # Get the latest snapshot
            latest_snapshot = snapshots[-1]
            
            # Consolidate data from all snapshots - use raw_state_data for missing attributes
            latest_raw_data = latest_snapshot.raw_state_data
            
            # Consolidate data from all snapshots
            consolidated_data = {
                'session_id': session_id,
                'lesson_ref': latest_snapshot.lesson_ref,
                'student_id': latest_snapshot.student_id,
                'final_phase': latest_snapshot.phase,
                'teaching_level': latest_snapshot.teaching_level,
                'lesson_start_time': snapshots[0].timestamp.isoformat() if snapshots else None,
                'lesson_end_time': latest_snapshot.timestamp.isoformat(),
                'total_teaching_interactions': latest_snapshot.teaching_interactions,
                'total_quiz_interactions': latest_snapshot.quiz_interactions,
                'diagnostic_complete': latest_snapshot.diagnostic_complete,
                'teaching_complete': latest_raw_data.get('teaching_complete', False),
                'quiz_complete': latest_snapshot.quiz_complete,
                'lesson_complete': latest_snapshot.lesson_complete,
                'quiz_questions': latest_snapshot.quiz_questions,
                'quiz_answers': latest_snapshot.quiz_answers,
                'concepts_covered': latest_snapshot.concepts_covered,
                'learning_objectives_met': latest_snapshot.learning_objectives_met,
                'performance_data': latest_snapshot.performance_data,
                'phase_progression': [s.phase for s in snapshots],
                'transition_history': self.transition_history.get(session_id, []),
                'data_snapshots_count': len(snapshots),
                'consolidation_timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            logger.info(f"[{request_id}] 📊 LESSON DATA CONSOLIDATED: Session {session_id}")
            logger.info(f"[{request_id}]   - Phases: {' → '.join(consolidated_data['phase_progression'])}")
            logger.info(f"[{request_id}]   - Teaching interactions: {consolidated_data['total_teaching_interactions']}")
            logger.info(f"[{request_id}]   - Quiz interactions: {consolidated_data['total_quiz_interactions']}")
            
            return consolidated_data
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ DATA CONSOLIDATION ERROR: {str(e)}")
            return {'error': str(e), 'session_id': session_id}
    
    # Private helper methods
    
    
    def _is_valid_basic_transition(self, from_phase: str, to_phase: str) -> bool:
        '''
        Check if transition is in valid transitions map based on the unified pathway.
        
        Args:
            from_phase: Current phase
            to_phase: Proposed next phase
            
        Returns:
            bool: True if transition is valid, False otherwise
        '''
        if not from_phase or not to_phase:
            return False
        
        # CRITICAL: Allow same-phase transitions (staying in the same phase)
        if from_phase == to_phase:
            return True
            
        # Check explicit transitions from the unified pathway
        valid_next_phases = self.VALID_TRANSITIONS.get(from_phase, [])
        if to_phase in valid_next_phases:
            return True
            
        return False
    
        
        # CRITICAL: Allow same-phase transitions (staying in the same phase)
        if from_phase == to_phase:
            return True
        
        # Handle dynamic diagnostic phases
        if from_phase.startswith('diagnostic_probing_L') and to_phase.startswith('diagnostic_probing_L'):
            return True
        
        # Handle dynamic teaching phases
        if from_phase.startswith('teaching') and to_phase.startswith(('teaching', 'quiz_initiate')):
            return True
        
        # Handle teaching level phases (teaching_start_level_X)
        if from_phase.startswith('teaching_start_level_') and to_phase.startswith(('teaching', 'quiz_initiate')):
            return True
        
        # Check explicit transitions
        valid_next_phases = self.VALID_TRANSITIONS.get(from_phase, [])
        if to_phase in valid_next_phases:
            return True
        
        # Handle wildcard patterns for common transitions
        if from_phase.startswith('smart_diagnostic_q') and to_phase.startswith('smart_diagnostic_q'):
            # Allow progression between diagnostic questions
            return True
        
        # CRITICAL: Allow smart_diagnostic_q5 to teaching_start transition
        if from_phase == 'smart_diagnostic_q5' and to_phase == 'teaching_start':
            return True
        
        # CRITICAL: Allow teaching_start to teaching transition
        if from_phase == 'teaching_start' and to_phase == 'teaching':
            return True
        
        # Allow smart_diagnostic_start to smart_diagnostic_q1 transition
        if from_phase == 'smart_diagnostic_start' and to_phase == 'smart_diagnostic_q1':
            return True
        
        if from_phase.startswith('teaching') and to_phase in ['quiz_initiate', 'quiz_questions']:
            # Allow teaching to quiz transitions
            return True
        
        if from_phase in ['quiz_questions', 'quiz_results'] and to_phase in ['quiz_results', 'conclusion_summary', 'completed']:
            # Allow quiz phase progressions
            return True
        
        return False
    
    def _check_backward_transition(self, from_phase: str, to_phase: str, session_data: Dict) -> Dict:
        """Check for backward transitions that should be blocked"""
        
        # Define phase progression order with more granular control
        phase_order = {
            'smart_diagnostic_start': 1,
            'smart_diagnostic_q1': 2,
            'smart_diagnostic_q2': 3,
            'smart_diagnostic_q3': 4,
            'smart_diagnostic_q4': 5,
            'smart_diagnostic_q5': 6,
            'teaching_start': 7,
            'teaching': 8,
            'quiz_initiate': 9,
            'quiz_questions': 10,
            'quiz_results': 11,
            'conclusion_summary': 12,
            'completed': 13
        }
        
        # Handle dynamic phase names
        from_order = phase_order.get(from_phase, 0)
        to_order = phase_order.get(to_phase, 0)
        
        # Handle teaching level phases
        if from_phase.startswith('teaching_start_level_'):
            from_order = 7
        if to_phase.startswith('teaching_start_level_'):
            to_order = 7
        
        # Flag backward transition for invalid order
        if from_order > to_order:
            return {
                'is_backward': True,
                'message': f'Backward transition not allowed: {from_phase} (order {from_order}) → {to_phase} (order {to_order})'
            }
        
        teaching_complete = session_data.get('teaching_complete', False)
        if teaching_complete and to_phase.startswith('teaching') and from_phase.startswith('quiz'):
            return {
                'is_backward': True,
                'message': f'Cannot return to teaching phase when teaching is complete'
            }
        
        # Allow most other transitions (including minor backward adjustments)
        return {'is_backward': False, 'message': 'Transition allowed'}
    
    def _validate_phase_data_requirements(self, phase: str, session_data: Dict) -> Dict:
        """Validate that required data is present for the target phase"""
        required_fields = self.PHASE_DATA_REQUIREMENTS.get(phase, [])
        missing_fields = []
        errors = []
        
        for field in required_fields:
            if field not in session_data or session_data[field] is None:
                missing_fields.append(field)
                errors.append(f"Missing required field '{field}' for phase '{phase}'")
        
        return {
            'valid': len(missing_fields) == 0,
            'missing_fields': missing_fields,
            'errors': errors,
            'message': f"Data validation for phase '{phase}': {len(missing_fields)} missing fields"
        }
    
    def _attempt_data_recovery(self, phase: str, session_data: Dict, request_id: str) -> Dict:
        """Attempt to recover missing data for phase transition"""
        recovered_data = {}
        recovery_messages = []
        
        try:
            # Recovery logic for specific phases
            if phase in ['teaching_start', 'teaching', 'quiz_initiate']:
                if 'diagnostic_complete' not in session_data:
                    # Infer diagnostic completion from phase history or teaching level
                    if session_data.get('assigned_level_for_teaching'):
                        recovered_data['diagnostic_complete'] = True
                        recovery_messages.append("Inferred diagnostic_complete from assigned_level_for_teaching")
                
                if 'assigned_level_for_teaching' not in session_data:
                    # Try to recover from grade or default
                    grade = session_data.get('grade', '5')
                    default_level = 5  # Safe default
                    try:
                        if 'primary' in grade.lower() or 'p' in grade.lower():
                            grade_num = int(''.join(filter(str.isdigit, grade)))
                            default_level = min(max(grade_num, 1), 10)
                    except:
                        pass
                    recovered_data['assigned_level_for_teaching'] = default_level
                    recovery_messages.append(f"Recovered assigned_level_for_teaching as {default_level}")
            
            elif phase == 'quiz_initiate':
                if 'teaching_complete' not in session_data:
                    # Infer from teaching interactions or phase
                    teaching_interactions = session_data.get('teaching_interactions', 0)
                    if teaching_interactions >= 10:  # Reasonable threshold
                        recovered_data['teaching_complete'] = True
                        recovery_messages.append("Inferred teaching_complete from interaction count")
            
            elif phase == 'quiz_questions':
                if 'quiz_started' not in session_data:
                    recovered_data['quiz_started'] = True
                    recovery_messages.append("Set quiz_started for quiz_questions phase")
                
                if 'quiz_questions_generated' not in session_data:
                    recovered_data['quiz_questions_generated'] = []
                    recovery_messages.append("Initialized empty quiz_questions_generated")
            
            elif phase == 'quiz_results':
                if 'quiz_complete' not in session_data:
                    # Check if we have quiz answers
                    quiz_answers = session_data.get('quiz_answers', [])
                    if len(quiz_answers) > 0:
                        recovered_data['quiz_complete'] = True
                        recovery_messages.append("Inferred quiz_complete from quiz_answers")
            
            return {
                'recovered': len(recovered_data) > 0,
                'recovered_data': recovered_data,
                'message': '; '.join(recovery_messages) if recovery_messages else 'No data recovered'
            }
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ DATA RECOVERY ERROR: {str(e)}")
            return {'recovered': False, 'recovered_data': {}, 'message': f'Recovery failed: {str(e)}'}
    
    def _validate_state_consistency(self, from_phase: str, to_phase: str, session_data: Dict) -> Dict:
        """Validate state consistency for the transition"""
        errors = []
        
        # Check diagnostic consistency
        diagnostic_complete = session_data.get('diagnostic_complete', False)
        assigned_level = session_data.get('assigned_level_for_teaching')
        
        if diagnostic_complete and not assigned_level:
            errors.append("Diagnostic marked complete but no teaching level assigned")
        
        if to_phase.startswith('teaching') and not diagnostic_complete:
            errors.append("Cannot enter teaching phase without completing diagnostic")
        
        # Check teaching consistency
        teaching_complete = session_data.get('teaching_complete', False)
        teaching_interactions = session_data.get('teaching_interactions', 0)
        
        if teaching_complete and teaching_interactions < 5:
            errors.append("Teaching marked complete but insufficient interactions")
        
        if to_phase.startswith('quiz') and not teaching_complete and teaching_interactions < 10:
            errors.append("Cannot enter quiz phase without sufficient teaching")
        
        # Check quiz consistency
        quiz_complete = session_data.get('quiz_complete', False)
        quiz_answers = session_data.get('quiz_answers', [])
        
        if quiz_complete and len(quiz_answers) == 0:
            errors.append("Quiz marked complete but no answers recorded")
        
        # DEBUG: Log the specific errors for debugging
        if len(errors) > 0:
            logger.error(f"🔍 STATE CONSISTENCY ERRORS DETECTED:")
            for i, error in enumerate(errors, 1):
                logger.error(f"🔍   {i}. {error}")
            logger.error(f"🔍 SESSION DATA DEBUG:")
            logger.error(f"🔍   - diagnostic_complete: {diagnostic_complete}")
            logger.error(f"🔍   - assigned_level_for_teaching: {assigned_level}")
            logger.error(f"🔍   - teaching_complete: {session_data.get('teaching_complete', False)}")
            logger.error(f"🔍   - teaching_interactions: {session_data.get('teaching_interactions', 0)}")
            logger.error(f"🔍   - from_phase: {from_phase}, to_phase: {to_phase}")
    
        return {
            'consistent': len(errors) == 0,
            'errors': errors,
            'message': f"State consistency check: {len(errors)} errors found"
        }
    
    def _attempt_state_correction(self, from_phase: str, to_phase: str, session_data: Dict, request_id: str) -> Dict:
        """Attempt to correct state inconsistencies"""
        corrected_data = {}
        correction_messages = []
        corrected_phase = to_phase
        
        logger.error(f"[{request_id}] 🔧 STATE CORRECTION ATTEMPT: {from_phase} → {to_phase}")
        
        try:
            # Correction logic based on common inconsistencies
            diagnostic_complete = session_data.get('diagnostic_complete', False)
            assigned_level = session_data.get('assigned_level_for_teaching')
            
            logger.error(f"[{request_id}] 🔧 CORRECTION CHECK: diagnostic_complete={diagnostic_complete}, assigned_level={assigned_level}")
            
            # If entering teaching without diagnostic complete, but we have a level
            if to_phase.startswith('teaching') and not diagnostic_complete and assigned_level:
                corrected_data['diagnostic_complete'] = True
                correction_messages.append("Corrected diagnostic_complete to True based on assigned level")
                logger.error(f"[{request_id}] 🔧 APPLYING CORRECTION: Setting diagnostic_complete=True")
            
            # If teaching is complete but insufficient interactions, adjust
            teaching_complete = session_data.get('teaching_complete', False)
            teaching_interactions = session_data.get('teaching_interactions', 0)
            
            if teaching_complete and teaching_interactions < 5:
                corrected_data['teaching_interactions'] = max(teaching_interactions, 10)
                correction_messages.append(f"Adjusted teaching_interactions to {corrected_data['teaching_interactions']}")
            
            # If quiz complete but no answers, this might be a premature completion
            quiz_complete = session_data.get('quiz_complete', False)
            quiz_answers = session_data.get('quiz_answers', [])
            
            if quiz_complete and len(quiz_answers) == 0 and to_phase == 'quiz_results':
                # Redirect to quiz_questions instead
                corrected_phase = 'quiz_questions'
                corrected_data['quiz_complete'] = False
                correction_messages.append("Redirected to quiz_questions due to missing answers")
            
            correction_result = {
                'corrected': len(corrected_data) > 0 or corrected_phase != to_phase,
                'corrected_phase': corrected_phase,
                'corrected_data': corrected_data,
                'message': '; '.join(correction_messages) if correction_messages else 'No corrections applied'
            }
            
            logger.error(f"[{request_id}] 🔧 CORRECTION RESULT: {correction_result}")
            return correction_result
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ STATE CORRECTION ERROR: {str(e)}")
            return {'corrected': False, 'corrected_phase': to_phase, 'corrected_data': {}, 'message': f'Correction failed: {str(e)}'}
    
    def _determine_recovery_phase(self, current_phase: str, session_data: Dict) -> str:
        """Determine appropriate recovery phase for invalid transitions"""
        
        # Recovery logic based on session state
        diagnostic_complete = session_data.get('diagnostic_complete', False)
        teaching_complete = session_data.get('teaching_complete', False)
        quiz_complete = session_data.get('quiz_complete', False)
        assigned_level = session_data.get('assigned_level_for_teaching')
        
        # If we have completed diagnostic and have a level, go to teaching
        if diagnostic_complete and assigned_level:
            if teaching_complete:
                return 'quiz_initiate'
            else:
                return 'teaching_start'
        
        # If we have some progress but no diagnostic completion, restart diagnostic
        if not diagnostic_complete:
            return 'smart_diagnostic_start'
        
        # Default recovery based on current phase
        if current_phase.startswith('diagnostic'):
            return 'smart_diagnostic_start'
        elif current_phase.startswith('teaching'):
            return 'teaching_start'
        elif current_phase.startswith('quiz'):
            return 'quiz_initiate'
        else:
            return 'smart_diagnostic_start'  # Ultimate fallback
    
    def _ensure_critical_data_preservation(self, 
                                         state_updates: Dict[str, Any], 
                                         session_data: Dict[str, Any],
                                         snapshot: LessonDataSnapshot,
                                         request_id: str) -> Dict[str, Any]:
        """Ensure critical data fields are preserved in state updates"""
        
        preserved_updates = copy.deepcopy(state_updates)
        
        # Preserve critical fields that might be missing
        for field in self.CRITICAL_DATA_FIELDS:
            if field not in preserved_updates and field in session_data:
                preserved_updates[field] = session_data[field]
        
        # Ensure phase history is maintained
        phase_history = session_data.get('phase_history', [])
        current_phase = session_data.get('current_phase')
        if current_phase and current_phase not in phase_history:
            phase_history.append(current_phase)
        preserved_updates['phase_history'] = phase_history
        
        # Preserve lesson timing
        if 'lesson_start_time' not in preserved_updates and 'lesson_start_time' in session_data:
            preserved_updates['lesson_start_time'] = session_data['lesson_start_time']
        
        # Add data preservation metadata
        preserved_updates['data_preservation_applied'] = True
        preserved_updates['snapshot_timestamp'] = snapshot.timestamp.isoformat()
        
        logger.debug(f"[{request_id}] 🔒 CRITICAL DATA PRESERVED: {len(self.CRITICAL_DATA_FIELDS)} fields checked")
        
        return preserved_updates
    
    def _record_transition_history(self, 
                                 session_id: str, 
                                 from_phase: str, 
                                 to_phase: str,
                                 validation_result: TransitionValidationResult,
                                 request_id: str):
        """Record transition in history for audit purposes"""
        
        if session_id not in self.transition_history:
            self.transition_history[session_id] = []
        
        transition_record = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'from_phase': from_phase,
            'to_phase': to_phase,
            'validation_result': validation_result.result.value,
            'message': validation_result.message,
            'corrected_phase': validation_result.corrected_phase,
            'data_preserved': validation_result.data_preserved,
            'recovery_applied': validation_result.recovery_applied,
            'request_id': request_id
        }
        
        self.transition_history[session_id].append(transition_record)
        
        # Keep only last 50 transitions per session
        if len(self.transition_history[session_id]) > 50:
            self.transition_history[session_id] = self.transition_history[session_id][-50:]
        
        logger.debug(f"[{request_id}] 📝 TRANSITION RECORDED: {from_phase} → {to_phase} ({validation_result.result.value})")

# Global instance for use throughout the application
phase_transition_manager = PhaseTransitionIntegrityManager()