"""
Test module for data_persistence.py

This module tests the structured data generation logic for the unified lesson pathway.
"""

import unittest
import json
from unittest.mock import <PERSON>Mock, patch
from data_persistence import DataPersistenceManager, persist_data_to_firestore

class TestDataPersistenceManager(unittest.TestCase):
    """Test cases for DataPersistenceManager class"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Sample session data for testing
        self.session_data = {
            'interactions': [
                {'role': 'assistant', 'content': 'Welcome to this introduction lesson on mathematics.'},
                {'role': 'user', 'content': 'I am ready to learn.'},
                {'role': 'assistant', 'content': 'Great! Let\'s start with the concept of fractions.'},
                {'role': 'user', 'content': 'I understand fractions are parts of a whole.'},
                {'role': 'assistant', 'content': 'Exactly! Now let\'s move to a quiz.', 'phase': 'quiz_initiate'}
            ],
            'quiz_responses': [
                {
                    'question': 'What is 1/2 + 1/4?',
                    'student_answer': '3/4',
                    'correct_answer': '3/4',
                    'is_correct': True
                },
                {
                    'question': 'Simplify 4/8.',
                    'student_answer': '1/2',
                    'correct_answer': '1/2',
                    'is_correct': True
                }
            ],
            'lesson_metadata': {
                'grade_level': 'Grade 5',
                'subject': 'Mathematics',
                'title': 'Introduction to Fractions',
                'learning_objectives': [
                    'Understand fraction concepts',
                    'Add and subtract fractions',
                    'Simplify fractions'
                ]
            },
            'student_info': {
                'name': 'Test Student',
                'grade': 'Grade 5'
            }
        }
        
        # Create DataPersistenceManager instance
        self.manager = DataPersistenceManager(self.session_data)
    
    def test_generate_student_summary(self):
        """Test generate_student_summary method"""
        student_summary = self.manager.generate_student_summary()
        
        # Verify structure
        self.assertIsInstance(student_summary, dict)
        self.assertIn('learning_journey_phases', student_summary)
        self.assertIn('homework_assignments', student_summary)
        self.assertIn('performance_metrics', student_summary)
        self.assertIn('engagement_analysis', student_summary)
        
        # Verify content
        self.assertIsInstance(student_summary['learning_journey_phases'], list)
        self.assertIsInstance(student_summary['homework_assignments'], list)
        self.assertIsInstance(student_summary['performance_metrics'], dict)
        self.assertIsInstance(student_summary['engagement_analysis'], dict)
        
        # Verify performance metrics
        metrics = student_summary['performance_metrics']
        self.assertEqual(metrics['total_questions'], 2)
        self.assertEqual(metrics['correct_answers'], 2)
        self.assertEqual(metrics['accuracy'], 100.0)
    
    def test_generate_lesson_notes(self):
        """Test generate_lesson_notes method"""
        lesson_notes = self.manager.generate_lesson_notes()
        
        # Verify structure
        self.assertIsInstance(lesson_notes, dict)
        self.assertIn('lesson_summary', lesson_notes)
        self.assertIn('quiz_performance', lesson_notes)
        self.assertIn('interaction_analysis', lesson_notes)
        self.assertIn('teaching_effectiveness', lesson_notes)
        
        # Verify content
        self.assertEqual(lesson_notes['lesson_summary']['title'], 'Introduction to Fractions')
        self.assertEqual(lesson_notes['lesson_summary']['subject'], 'Mathematics')
        self.assertEqual(lesson_notes['lesson_summary']['grade_level'], 'Grade 5')
        
        # Verify quiz performance
        quiz_performance = lesson_notes['quiz_performance']
        self.assertIsInstance(quiz_performance['question_breakdown'], list)
        self.assertEqual(len(quiz_performance['question_breakdown']), 2)
        self.assertGreaterEqual(quiz_performance['overall_score'], 0)
    
    def test_generate_blooms_taxonomy_analysis(self):
        """Test generate_blooms_taxonomy_analysis method"""
        blooms_analysis = self.manager.generate_blooms_taxonomy_analysis()
        
        # Verify structure
        self.assertIsInstance(blooms_analysis, dict)
        self.assertIn('cognitive_complexity_score', blooms_analysis)
        self.assertIn('lesson_cognitive_levels', blooms_analysis)
        self.assertIn('learning_objectives_met', blooms_analysis)
        self.assertIn('cognitive_progression', blooms_analysis)
        
        # Verify content
        self.assertIsInstance(blooms_analysis['cognitive_complexity_score'], float)
        self.assertIsInstance(blooms_analysis['lesson_cognitive_levels'], dict)
        self.assertIsInstance(blooms_analysis['learning_objectives_met'], list)
        self.assertIsInstance(blooms_analysis['cognitive_progression'], dict)
        
        # Verify learning objectives
        objectives = blooms_analysis['learning_objectives_met']
        self.assertEqual(len(objectives), 3)
        self.assertIn('Understand fraction concepts', objectives)
    
    def test_generate_all_data(self):
        """Test generate_all_data method"""
        all_data = self.manager.generate_all_data()
        
        # Verify structure
        self.assertIsInstance(all_data, dict)
        self.assertIn('student_summary', all_data)
        self.assertIn('lesson_notes', all_data)
        self.assertIn('blooms_taxonomy_analysis', all_data)
        self.assertIn('last_updated', all_data)
        
        # Verify each component is a dictionary
        self.assertIsInstance(all_data['student_summary'], dict)
        self.assertIsInstance(all_data['lesson_notes'], dict)
        self.assertIsInstance(all_data['blooms_taxonomy_analysis'], dict)

class TestFirestorePersistence(unittest.TestCase):
    """Test cases for Firestore persistence functions"""
    
    @patch('data_persistence.logger')
    def test_persist_data_to_firestore(self, mock_logger):
        """Test persist_data_to_firestore function"""
        # Create mock Firestore database
        mock_db = MagicMock()
        mock_collection = MagicMock()
        mock_doc = MagicMock()
        
        # Set up the mock chain
        mock_db.collection.return_value = mock_collection
        mock_collection.document.return_value = mock_doc
        
        # Test data
        session_id = 'test-session-123'
        data = {
            'student_summary': {'test': 'data'},
            'lesson_notes': {'test': 'notes'},
            'blooms_taxonomy_analysis': {'test': 'analysis'}
        }
        
        # Call the function
        result = persist_data_to_firestore(mock_db, session_id, data)
        
        # Verify the result
        self.assertTrue(result)
        
        # Verify the Firestore calls
        mock_db.collection.assert_called_once_with('lesson_sessions')
        mock_collection.document.assert_called_once_with(session_id)
        mock_doc.set.assert_called_once_with(data, merge=True)
        
        # Verify logging
        mock_logger.info.assert_called_with(f"Successfully persisted data to Firestore for session {session_id}")
    
    @patch('data_persistence.logger')
    def test_persist_data_to_firestore_error(self, mock_logger):
        """Test persist_data_to_firestore function with error"""
        # Create mock Firestore database that raises an exception
        mock_db = MagicMock()
        mock_collection = MagicMock()
        mock_doc = MagicMock()
        
        # Set up the mock chain
        mock_db.collection.return_value = mock_collection
        mock_collection.document.return_value = mock_doc
        mock_doc.set.side_effect = Exception("Test error")
        
        # Test data
        session_id = 'test-session-123'
        data = {'test': 'data'}
        
        # Call the function
        result = persist_data_to_firestore(mock_db, session_id, data)
        
        # Verify the result
        self.assertFalse(result)
        
        # Verify logging
        mock_logger.error.assert_called_with("Error persisting data to Firestore: Test error")

if __name__ == '__main__':
    unittest.main()