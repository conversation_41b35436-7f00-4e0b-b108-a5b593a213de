# -*- coding: utf-8 -*-
"""
Teaching Phase Integration for Unified Lesson Flow

This module integrates the teaching phase handler with the main enhance_content_api function
to implement the unified lesson flow state machine.

It provides functions to be imported into main.py for handling teaching phases
and AI handoff detection.
"""

import logging
import time
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timezone

# Local imports
from teaching_phase_handler import teaching_phase_handler

logger = logging.getLogger(__name__)

def handle_teaching_phase(
    current_phase: str,
    session_data: Dict[str, Any],
    user_input: str,
    ai_response: str,
    context: Dict[str, Any],
    request_id: str
) -> Tuple[str, Dict[str, Any]]:
    """
    Handle teaching phase processing for the unified lesson flow state machine
    
    Args:
        current_phase: Current lesson phase
        session_data: Current session state
        user_input: User's input message
        ai_response: AI-generated response
        context: Additional context data
        request_id: Request identifier for logging
        
    Returns:
        Tuple of (next_phase, state_updates)
    """
    logger.info(f"[{request_id}] 🎓 TEACHING PHASE INTEGRATION: Current phase = {current_phase}")
    
    try:
        # Handle teaching_start phase
        if current_phase == 'teaching_start':
            return teaching_phase_handler.handle_teaching_start(
                session_data, user_input, context, request_id
            )
        
        # Handle teaching phase with AI handoff detection
        elif current_phase == 'teaching':
            return teaching_phase_handler.handle_teaching(
                session_data, user_input, ai_response, context, request_id
            )
        
        # Unsupported phase
        else:
            logger.warning(f"[{request_id}] ⚠️ Unsupported phase in teaching phase integration: {current_phase}")
            return current_phase, {}
            
    except Exception as e:
        logger.error(f"[{request_id}] ❌ Error in teaching phase integration: {e}")
        # Safe fallback - stay in current phase
        return current_phase, {'error': str(e)}

def apply_teaching_guardrails(
    ai_response: str,
    session_data: Dict[str, Any],
    context: Dict[str, Any],
    request_id: str
) -> str:
    """
    Apply teaching guardrails to AI response
    
    Args:
        ai_response: AI-generated response
        session_data: Current session state
        context: Additional context data
        request_id: Request identifier for logging
        
    Returns:
        Enhanced AI response with guardrails applied
    """
    return teaching_phase_handler.apply_intelligent_guardrails_to_response(
        ai_response, session_data, context, request_id
    )

def detect_ai_handoff(
    ai_response: str,
    session_data: Dict[str, Any],
    context: Dict[str, Any],
    request_id: str
) -> bool:
    """
    Detect AI handoff indicators in the response
    
    Args:
        ai_response: AI-generated response
        session_data: Current session state
        context: Additional context data
        request_id: Request identifier for logging
        
    Returns:
        True if handoff indicators are detected
    """
    # Check if teaching is complete
    from teaching_rules import validate_teaching_completion
    is_complete, _, _ = validate_teaching_completion(session_data, context)
    
    # Only allow handoff if teaching is complete
    if is_complete:
        return teaching_phase_handler._detect_handoff_indicators(ai_response)
    
    return False