#!/usr/bin/env python3
"""
Verification script for AI instructor role boundaries.

This script tests the AI instructor role boundaries enforcement in a simulated
lesson flow scenario, ensuring proper handoff from AI to backend at quiz_initiate phase.
"""

import logging
import json
from teaching_rules import enforce_ai_role_boundaries

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simulate_lesson_flow():
    """Simulate a lesson flow to test AI instructor role boundaries."""
    
    # Define test scenarios for different phases
    scenarios = [
        {
            "phase": "teaching_start",
            "ai_response": "Welcome to our lesson on photosynthesis! Today we'll learn about how plants convert sunlight into energy. Let's start by exploring the basic components involved in this process. // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"teaching\"} // AI_STATE_UPDATE_BLOCK_END",
            "expected_valid": True,
            "description": "Valid teaching_start phase response"
        },
        {
            "phase": "teaching",
            "ai_response": "Photosynthesis occurs in the chloroplasts of plant cells. The process requires sunlight, water, and carbon dioxide to produce glucose and oxygen. Let's explore each component in more detail. // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"teaching\"} // AI_STATE_UPDATE_BLOCK_END",
            "expected_valid": True,
            "description": "Valid teaching phase response"
        },
        {
            "phase": "teaching",
            "ai_response": "Now let's test your understanding with a quick quiz. Question 1: What are the products of photosynthesis? a) Oxygen and glucose b) Carbon dioxide and water c) Nitrogen and hydrogen d) Methane and ammonia // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"teaching\"} // AI_STATE_UPDATE_BLOCK_END",
            "expected_valid": False,
            "description": "Invalid teaching phase response with quiz content"
        },
        {
            "phase": "quiz_initiate",
            "ai_response": "Excellent work! You've mastered the key concepts with 95% coverage in 12 interactions. I'm now handing you over to our assessment system for your quiz. Good luck! // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"quiz_initiate\", \"handoff_to_backend\": true} // AI_STATE_UPDATE_BLOCK_END",
            "expected_valid": True,
            "description": "Valid quiz_initiate phase response with proper handoff"
        },
        {
            "phase": "quiz_initiate",
            "ai_response": "Let's start the quiz. Here's your first question about photosynthesis. // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"quiz_questions\"} // AI_STATE_UPDATE_BLOCK_END",
            "expected_valid": False,
            "description": "Invalid quiz_initiate phase response without handoff"
        },
        {
            "phase": "quiz_questions",
            "ai_response": "Here's the next question: What is the role of chlorophyll in photosynthesis? // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"quiz_questions\"} // AI_STATE_UPDATE_BLOCK_END",
            "expected_valid": False,
            "description": "Invalid AI attempt to operate in backend-controlled quiz_questions phase"
        },
        {
            "phase": "quiz_results",
            "ai_response": "Congratulations! You scored 8/10 on the quiz. Let's review your answers. // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"conclusion_summary\"} // AI_STATE_UPDATE_BLOCK_END",
            "expected_valid": False,
            "description": "Invalid AI attempt to operate in backend-controlled quiz_results phase"
        },
        {
            "phase": "final_report_inprogress",
            "ai_response": "Generating your final lesson report... // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"complete\"} // AI_STATE_UPDATE_BLOCK_END",
            "expected_valid": False,
            "description": "Invalid AI attempt to operate in backend-controlled final_report_inprogress phase"
        },
        {
            "phase": "complete",
            "ai_response": "Lesson complete! You've successfully finished the photosynthesis lesson. // AI_STATE_UPDATE_BLOCK_START {\"new_phase\": \"complete\"} // AI_STATE_UPDATE_BLOCK_END",
            "expected_valid": False,
            "description": "Invalid AI attempt to operate in backend-controlled complete phase"
        }
    ]
    
    # Run the test scenarios
    results = []
    for i, scenario in enumerate(scenarios, 1):
        phase = scenario["phase"]
        ai_response = scenario["ai_response"]
        expected_valid = scenario["expected_valid"]
        description = scenario["description"]
        
        # Test the scenario
        is_valid, reason = enforce_ai_role_boundaries(phase, ai_response)
        
        # Check if the result matches the expectation
        result_matches = is_valid == expected_valid
        
        # Log the result
        status = "✅ PASS" if result_matches else "❌ FAIL"
        logger.info(f"Scenario {i}: {status} - {description}")
        logger.info(f"  Phase: {phase}")
        logger.info(f"  Expected valid: {expected_valid}, Actual valid: {is_valid}")
        logger.info(f"  Reason: {reason}")
        
        # Store the result
        results.append({
            "scenario": i,
            "description": description,
            "phase": phase,
            "expected_valid": expected_valid,
            "actual_valid": is_valid,
            "reason": reason,
            "passed": result_matches
        })
    
    # Calculate overall results
    total_scenarios = len(scenarios)
    passed_scenarios = sum(1 for result in results if result["passed"])
    
    # Log overall results
    logger.info(f"\nOverall Results: {passed_scenarios}/{total_scenarios} scenarios passed")
    
    # Return the results
    return {
        "total_scenarios": total_scenarios,
        "passed_scenarios": passed_scenarios,
        "success_rate": passed_scenarios / total_scenarios * 100,
        "results": results
    }

if __name__ == "__main__":
    logger.info("Starting AI instructor role boundaries verification...")
    results = simulate_lesson_flow()
    
    # Save results to file
    with open("ai_instructor_role_boundaries_verification.json", "w") as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Verification complete. Success rate: {results['success_rate']:.1f}%")
    logger.info(f"Detailed results saved to ai_instructor_role_boundaries_verification.json")