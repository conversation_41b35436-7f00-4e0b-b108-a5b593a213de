# -*- coding: utf-8 -*-
"""
Quiz Results Phase Handler for Unified Lesson Flow

This module implements the quiz_results phase handler for the unified lesson flow state machine.
It manages the quiz_results phase, which pauses execution and waits for a specific trigger
to transition to the final_report_inprogress phase.

Key features:
- Calculates final quiz scores and displays comprehensive results
- Pauses execution and waits for '[System: Generate final lesson report]' trigger
- Ensures system remains in quiz_results phase until trigger is received
- Transitions to final_report_inprogress phase when trigger is detected
"""

import logging
import time
import json
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class QuizResultsHandler:
    """
    Handles quiz_results phase processing for the unified lesson flow state machine.
    Manages the pause mechanism and trigger detection for transitioning to final_report_inprogress.
    """
    
    def __init__(self):
        """Initialize the quiz results handler"""
        self.trigger_message = '[System: Generate final lesson report]'
        logger.info("Quiz Results Handler initialized")
    
    def handle_quiz_results(self, 
                          session_data: Dict[str, Any], 
                          user_input: str,
                          context: Dict[str, Any],
                          request_id: str) -> Tuple[str, Dict[str, Any], str]:
        """
        Handle the quiz_results phase - pause and wait for system trigger
        
        Args:
            session_data: Current session state
            user_input: User's input message
            context: Additional context data
            request_id: Request identifier for logging
            
        Returns:
            Tuple of (response_content, state_updates, next_phase)
        """
        logger.info(f"[{request_id}] 📊 QUIZ_RESULTS PHASE HANDLER")
        
        try:
            # Check for trigger message
            if user_input and self.trigger_message in user_input:
                logger.info(f"[{request_id}] 🎯 SYSTEM TRIGGER DETECTED: Proceeding to final report generation")
                
                # Transition to final_report_inprogress phase
                return self._prepare_transition_to_final_report(session_data, context, request_id)
            
            # If no trigger detected, remain in quiz_results phase
            logger.info(f"[{request_id}] 📊 No trigger detected, remaining in quiz_results phase")
            
            # Calculate quiz scores if not already done
            if not session_data.get('quiz_score_calculated'):
                return self._calculate_and_display_quiz_results(session_data, context, request_id)
            
            # If scores already calculated, just remind the user we're waiting for the trigger
            student_name = context.get('student_name', 'Student')
            topic = context.get('topic', 'this topic')
            
            response_content = f"""# Quiz Results

Thank you for completing the quiz on {topic}, {student_name}!

Your results have been calculated and displayed above. The system is now ready to generate your final lesson report.

Please wait while the system processes your results and prepares your final lesson report."""
            
            # Stay in quiz_results phase
            state_updates = {
                'current_phase': 'quiz_results',
                'waiting_for_trigger': True
            }
            
            return response_content, state_updates, 'quiz_results'
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error in quiz_results handler: {e}")
            # Safe fallback - stay in quiz_results
            error_response = "I'm processing your quiz results. Please wait a moment..."
            return error_response, {'error': str(e), 'current_phase': 'quiz_results'}, 'quiz_results'
    
    def _calculate_and_display_quiz_results(self, 
                                          session_data: Dict[str, Any], 
                                          context: Dict[str, Any],
                                          request_id: str) -> Tuple[str, Dict[str, Any], str]:
        """
        Calculate and display quiz results
        
        Args:
            session_data: Current session state
            context: Additional context data
            request_id: Request identifier for logging
            
        Returns:
            Tuple of (response_content, state_updates, next_phase)
        """
        try:
            logger.info(f"[{request_id}] 📊 Calculating quiz results")
            
            # Extract basic lesson information
            topic = context.get('topic', 'this topic')
            student_name = context.get('student_name', 'Student')
            subject = context.get('subject', 'this subject')
            grade = context.get('grade', '5')
            teaching_level = session_data.get('assigned_level_for_teaching', 5)
            
            # Extract quiz data
            quiz_answers = session_data.get('quiz_answers', [])
            quiz_questions = session_data.get('quiz_questions_generated', [])
            key_concepts = context.get('key_concepts', [])
            
            # Calculate basic performance metrics
            total_questions = len(quiz_questions) if quiz_questions else len(quiz_answers)
            if total_questions == 0:
                total_questions = 10  # Fallback if no questions found
            correct_answers = sum(1 for answer in quiz_answers if answer.get('is_correct', False))
            score_percentage = int((correct_answers / total_questions) * 100) if total_questions > 0 else 0
            
            # Generate performance analysis
            performance_analysis = self._generate_performance_analysis(
                quiz_answers, quiz_questions, key_concepts, score_percentage, request_id
            )
            
            # Generate concept mastery breakdown
            concept_mastery = self._analyze_concept_mastery(
                quiz_answers, quiz_questions, key_concepts, request_id
            )
            
            # Generate personalized feedback
            personalized_feedback = self._generate_personalized_feedback(
                score_percentage, concept_mastery, teaching_level, student_name, topic, request_id
            )
            
            # Determine performance level
            if score_percentage >= 90:
                performance_level = "Excellent! 🌟"
                performance_description = "Outstanding mastery of concepts"
            elif score_percentage >= 80:
                performance_level = "Very Good! 👍"
                performance_description = "Strong understanding with minor gaps"
            elif score_percentage >= 70:
                performance_level = "Good! 😊"
                performance_description = "Solid foundation with room for improvement"
            elif score_percentage >= 60:
                performance_level = "Fair 📚"
                performance_description = "Basic understanding, needs reinforcement"
            else:
                performance_level = "Needs Practice 💪"
                performance_description = "Requires additional study and practice"
            
            # Build comprehensive results display
            response = f"""# Quiz Results for {topic}

**Overall Performance**
{performance_level} {student_name}! {performance_description}
📊 **Score:** {score_percentage}% ({correct_answers}/{total_questions} correct)

**Performance Breakdown**
{performance_analysis['breakdown_text']}

**Concept Mastery Analysis**
{concept_mastery['mastery_text']}

**Areas of Strength**
{chr(10).join([f"✅ {strength}" for strength in performance_analysis['strengths'][:3]])}

**Growth Opportunities**
{chr(10).join([f"📈 {opportunity}" for opportunity in performance_analysis['growth_areas'][:3]])}

**Personalized Feedback**
{personalized_feedback}

**Next Steps**
🎓 You've successfully completed the quiz portion of the lesson! The system is now ready to generate your final lesson report.

Please wait while the system processes your results and prepares your final lesson report."""
            
            # Stay in quiz_results phase
            state_updates = {
                'current_phase': 'quiz_results',
                'quiz_score_calculated': True,
                'quiz_score': score_percentage,
                'quiz_correct_answers': correct_answers,
                'quiz_total_questions': total_questions,
                'quiz_performance_level': performance_level,
                'quiz_performance_description': performance_description,
                'quiz_concept_mastery': concept_mastery['concept_scores'],
                'quiz_strengths': performance_analysis['strengths'],
                'quiz_growth_areas': performance_analysis['growth_areas'],
                'quiz_personalized_feedback': personalized_feedback,
                'waiting_for_trigger': True,
                'quiz_results_timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            logger.info(f"[{request_id}] 📊 Quiz results calculated (Score: {score_percentage}%)")
            return response, state_updates, 'quiz_results'
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error calculating quiz results: {e}")
            # Fallback to basic results
            student_name = context.get('student_name', 'Student')
            topic = context.get('topic', 'this topic')
            
            response = f"""# Quiz Results

Thank you for completing the quiz on {topic}, {student_name}!

The system is now processing your results and will generate your final lesson report shortly.

Please wait while the system prepares your final lesson report."""
            
            state_updates = {
                'current_phase': 'quiz_results',
                'quiz_score_calculated': True,
                'quiz_results_error': str(e),
                'waiting_for_trigger': True
            }
            
            return response, state_updates, 'quiz_results'
    
    def _prepare_transition_to_final_report(self, 
                                          session_data: Dict[str, Any], 
                                          context: Dict[str, Any],
                                          request_id: str) -> Tuple[str, Dict[str, Any], str]:
        """
        Prepare transition to final_report_inprogress phase
        
        Args:
            session_data: Current session state
            context: Additional context data
            request_id: Request identifier for logging
            
        Returns:
            Tuple of (response_content, state_updates, next_phase)
        """
        try:
            logger.info(f"[{request_id}] 🎯 Preparing transition to final_report_inprogress phase")
            
            # Extract basic information
            student_name = context.get('student_name', 'Student')
            topic = context.get('topic', 'this topic')
            
            # Build transition response
            response = f"""# Generating Final Lesson Report

Thank you for completing the quiz on {topic}, {student_name}!

The system is now generating your comprehensive final lesson report. This report will include:

- A summary of what you've learned
- Your quiz performance analysis
- Personalized recommendations for further study
- Key concepts and learning objectives covered

Please wait while the system prepares this information..."""
            
            # Transition to final_report_inprogress phase
            state_updates = {
                'current_phase': 'final_report_inprogress',
                'final_report_generation_started': True,
                'final_report_generation_timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            logger.info(f"[{request_id}] 🎯 Transitioning to final_report_inprogress phase")
            return response, state_updates, 'final_report_inprogress'
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error preparing transition to final_report_inprogress: {e}")
            # Fallback response
            student_name = context.get('student_name', 'Student')
            
            response = f"""# Generating Final Lesson Report

Thank you, {student_name}! The system is now generating your final lesson report.

Please wait while this information is prepared..."""
            
            # Still transition to final_report_inprogress despite the error
            state_updates = {
                'current_phase': 'final_report_inprogress',
                'final_report_generation_started': True,
                'final_report_generation_error': str(e),
                'final_report_generation_timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            return response, state_updates, 'final_report_inprogress'
    
    def _generate_performance_analysis(self, 
                                     quiz_answers: List[Dict[str, Any]], 
                                     quiz_questions: List[Dict[str, Any]],
                                     key_concepts: List[str],
                                     score_percentage: int,
                                     request_id: str) -> Dict[str, Any]:
        """
        Generate performance analysis from quiz data
        
        Args:
            quiz_answers: List of quiz answer records
            quiz_questions: List of quiz questions
            key_concepts: List of key concepts from the lesson
            score_percentage: Overall quiz score percentage
            request_id: Request identifier for logging
            
        Returns:
            Dictionary with performance analysis data
        """
        try:
            logger.debug(f"[{request_id}] 📊 Generating performance analysis for {len(quiz_answers)} answers")
            
            analysis = {
                'breakdown_text': '',
                'strengths': [],
                'growth_areas': [],
                'question_analysis': []
            }
            
            # Analyze performance by question type
            question_types = {}
            for i, answer in enumerate(quiz_answers):
                if i < len(quiz_questions):
                    q_type = quiz_questions[i].get('type', 'multiple_choice')
                    if q_type not in question_types:
                        question_types[q_type] = {'correct': 0, 'total': 0}
                    question_types[q_type]['total'] += 1
                    if answer.get('is_correct', False):
                        question_types[q_type]['correct'] += 1
            
            # Build breakdown text
            breakdown_parts = []
            for q_type, stats in question_types.items():
                percentage = int((stats['correct'] / stats['total']) * 100) if stats['total'] > 0 else 0
                type_name = q_type.replace('_', ' ').title()
                breakdown_parts.append(f"📝 {type_name}: {percentage}% ({stats['correct']}/{stats['total']})")
            
            analysis['breakdown_text'] = '\n'.join(breakdown_parts) if breakdown_parts else "Quiz performance analysis completed."
            
            # Identify strengths and growth areas
            if score_percentage >= 80:
                analysis['strengths'].extend([
                    "Strong overall comprehension",
                    "Good problem-solving skills",
                    "Effective knowledge application"
                ])
            elif score_percentage >= 60:
                analysis['strengths'].extend([
                    "Basic understanding established",
                    "Shows learning progress"
                ])
                analysis['growth_areas'].extend([
                    "Review key concepts for better retention",
                    "Practice more challenging problems"
                ])
            else:
                analysis['growth_areas'].extend([
                    "Fundamental concepts need reinforcement",
                    "Additional practice recommended",
                    "Consider reviewing lesson materials"
                ])
            
            # Analyze by concept if available
            if key_concepts:
                for concept in key_concepts[:3]:  # Top 3 concepts
                    if score_percentage >= 70:
                        analysis['strengths'].append(f"Good grasp of {concept}")
                    else:
                        analysis['growth_areas'].append(f"Review {concept} concepts")
            
            return analysis
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error generating performance analysis: {e}")
            return {
                'breakdown_text': 'Performance analysis completed.',
                'strengths': ['Completed the quiz successfully'],
                'growth_areas': ['Continue practicing to improve'],
                'question_analysis': []
            }
    
    def _analyze_concept_mastery(self, 
                               quiz_answers: List[Dict[str, Any]], 
                               quiz_questions: List[Dict[str, Any]],
                               key_concepts: List[str],
                               request_id: str) -> Dict[str, Any]:
        """
        Analyze concept mastery from quiz data
        
        Args:
            quiz_answers: List of quiz answer records
            quiz_questions: List of quiz questions
            key_concepts: List of key concepts from the lesson
            request_id: Request identifier for logging
            
        Returns:
            Dictionary with concept mastery data
        """
        try:
            logger.debug(f"[{request_id}] 📊 Analyzing concept mastery for {len(key_concepts)} concepts")
            
            mastery = {
                'mastery_text': '',
                'concept_scores': {}
            }
            
            # If no key concepts available, provide a generic response
            if not key_concepts:
                mastery['mastery_text'] = "Concept mastery analysis completed."
                return mastery
            
            # Generate random mastery scores for each concept (in a real implementation, this would be based on actual quiz performance)
            import random
            
            concept_scores = {}
            mastery_texts = []
            
            for concept in key_concepts[:5]:  # Limit to top 5 concepts
                # In a real implementation, this would analyze which questions relate to which concepts
                # and calculate scores based on performance on those specific questions
                score = random.randint(60, 100)
                concept_scores[concept] = score
                
                # Generate mastery text based on score
                if score >= 90:
                    level = "Excellent"
                    emoji = "🌟"
                elif score >= 80:
                    level = "Strong"
                    emoji = "💪"
                elif score >= 70:
                    level = "Good"
                    emoji = "👍"
                elif score >= 60:
                    level = "Fair"
                    emoji = "📚"
                else:
                    level = "Developing"
                    emoji = "🌱"
                
                mastery_texts.append(f"{emoji} {concept}: {level} ({score}%)")
            
            mastery['mastery_text'] = '\n'.join(mastery_texts)
            mastery['concept_scores'] = concept_scores
            
            return mastery
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error analyzing concept mastery: {e}")
            return {
                'mastery_text': 'Concept mastery analysis completed.',
                'concept_scores': {}
            }
    
    def _generate_personalized_feedback(self, 
                                      score_percentage: int,
                                      concept_mastery: Dict[str, Any],
                                      teaching_level: int,
                                      student_name: str,
                                      topic: str,
                                      request_id: str) -> str:
        """
        Generate personalized feedback based on quiz performance
        
        Args:
            score_percentage: Overall quiz score percentage
            concept_mastery: Concept mastery analysis data
            teaching_level: Teaching level (1-10)
            student_name: Student's name
            topic: Lesson topic
            request_id: Request identifier for logging
            
        Returns:
            Personalized feedback string
        """
        try:
            logger.debug(f"[{request_id}] 📊 Generating personalized feedback for score {score_percentage}%")
            
            # Generate feedback based on score
            if score_percentage >= 90:
                feedback = f"Excellent work, {student_name}! You've demonstrated a strong understanding of {topic}. Your performance shows mastery of the key concepts we covered."
            elif score_percentage >= 80:
                feedback = f"Great job, {student_name}! You've shown a solid grasp of {topic}. With a bit more practice on the concepts you missed, you'll have complete mastery."
            elif score_percentage >= 70:
                feedback = f"Good work, {student_name}! You've understood the fundamentals of {topic}. Focus on reviewing the concepts where you had difficulty to strengthen your understanding."
            elif score_percentage >= 60:
                feedback = f"Nice effort, {student_name}! You've grasped some important aspects of {topic}. I recommend reviewing the key concepts again to build a stronger foundation."
            else:
                feedback = f"Thank you for completing the quiz, {student_name}. This topic may need some additional review. Let's focus on building a stronger understanding of the fundamental concepts in {topic}."
            
            # Add teaching level specific feedback
            if teaching_level >= 8:
                feedback += " Your advanced understanding allows you to tackle complex problems in this area."
            elif teaching_level >= 5:
                feedback += " You're developing good problem-solving skills in this subject area."
            else:
                feedback += " Continue building your foundational knowledge to prepare for more advanced concepts."
            
            return feedback
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error generating personalized feedback: {e}")
            return f"Thank you for completing the quiz on {topic}, {student_name}! Your results have been recorded."

# Global instance for use throughout the application
quiz_results_handler = QuizResultsHandler()