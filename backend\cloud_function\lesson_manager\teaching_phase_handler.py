# -*- coding: utf-8 -*-
"""
Teaching Phase Handler with AI Handoff Detection

This module implements the teaching phase handler for the unified lesson flow state machine.
It manages the teaching_start and teaching phases, and detects when to transition to quiz_initiate.

Key features:
- Manages teaching_start and teaching phases
- Detects AI handoff points for quiz transition
- Validates teaching completion before allowing quiz transition
- Enforces phase consistency and prevents premature transitions
"""

import logging
import time
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timezone

# Local imports
from teaching_rules import (
    teaching_rules_engine,
    validate_teaching_completion,
    enforce_phase_consistency
)
from teaching_phase_enhancement import teaching_phase_manager
from intelligent_guardrails import apply_intelligent_guardrails

logger = logging.getLogger(__name__)

class TeachingPhaseHandler:
    """
    Handles teaching phase processing and AI handoff detection
    for the unified lesson flow state machine.
    """
    
    def __init__(self):
        """Initialize the teaching phase handler"""
        self.handoff_indicators = [
            "let's start the quiz",
            "let's begin the quiz",
            "time for a quiz",
            "ready for the quiz",
            "let's test your understanding",
            "quiz time",
            "let's assess your understanding",
            "now for some questions",
            "let's check what you've learned"
        ]
        logger.info("Teaching Phase Handler initialized with AI handoff detection")
    
    def handle_teaching_start(self, 
                             session_data: Dict[str, Any], 
                             user_input: str,
                             context: Dict[str, Any],
                             request_id: str) -> Tuple[str, Dict[str, Any]]:
        """
        Handle the teaching_start phase
        
        Args:
            session_data: Current session state
            user_input: User's input message
            context: Additional context data
            request_id: Request identifier for logging
            
        Returns:
            Tuple of (next_phase, state_updates)
        """
        logger.info(f"[{request_id}] 🎓 TEACHING_START PHASE HANDLER")
        
        try:
            # Initialize teaching phase tracking if not already done
            if not session_data.get('teaching_start_time'):
                teaching_start_time = datetime.now(timezone.utc).isoformat()
                
                # Initialize teaching phase with objectives
                learning_objectives = context.get('learning_objectives', [])
                teaching_level = session_data.get('assigned_level_for_teaching', 5)
                
                # Initialize teaching phase manager
                session_id = session_data.get('session_id', 'unknown')
                teaching_phase_manager.initialize_teaching_phase(
                    session_id, teaching_level, learning_objectives
                )
                
                logger.info(f"[{request_id}] 🎓 Teaching phase initialized: Level {teaching_level}, {len(learning_objectives)} objectives")
                
                # Prepare state updates
                state_updates = {
                    'teaching_start_time': teaching_start_time,
                    'teaching_interactions': 0,
                    'content_depth_score': 0.0,
                    'teaching_complete': False,
                    'current_phase': 'teaching_start'
                }
                
                # Transition to teaching phase
                return 'teaching', state_updates
            
            # If already initialized, transition to teaching phase
            return 'teaching', {'current_phase': 'teaching'}
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error in teaching_start handler: {e}")
            # Safe fallback - stay in teaching_start
            return 'teaching_start', {'error': str(e)}
    
    def handle_teaching(self, 
                       session_data: Dict[str, Any], 
                       user_input: str,
                       ai_response: str,
                       context: Dict[str, Any],
                       request_id: str) -> Tuple[str, Dict[str, Any]]:
        """
        Handle the teaching phase with AI handoff detection
        
        Args:
            session_data: Current session state
            user_input: User's input message
            ai_response: AI-generated response
            context: Additional context data
            request_id: Request identifier for logging
            
        Returns:
            Tuple of (next_phase, state_updates)
        """
        logger.info(f"[{request_id}] 🎓 TEACHING PHASE HANDLER")
        
        try:
            # Increment teaching interactions counter
            teaching_interactions = session_data.get('teaching_interactions', 0) + 1
            
            # Process teaching interaction with teaching phase manager
            session_id = session_data.get('session_id', 'unknown')
            interaction_result = teaching_phase_manager.process_teaching_interaction(
                session_id, ai_response
            )
            
            # Update content depth score
            content_depth_score = interaction_result.get('content_depth_score', 0.0)
            
            # Check if teaching is complete using teaching rules engine
            is_complete, completion_reason, validation_details = validate_teaching_completion(
                session_data, context
            )
            
            # Prepare state updates
            state_updates = {
                'teaching_interactions': teaching_interactions,
                'content_depth_score': content_depth_score,
                'teaching_complete': is_complete,
                'completion_reason': completion_reason,
                'validation_details': validation_details,
                'current_phase': 'teaching'
            }
            
            # Update objectives tracking
            objectives_tracking = {
                'completion_percentage': interaction_result.get('coverage_percentage', 0),
                'covered_objectives': interaction_result.get('objectives_covered', 0),
                'total_objectives': interaction_result.get('total_objectives', 0)
            }
            state_updates['objectives_tracking'] = objectives_tracking
            
            # Check for AI handoff indicators in the response
            handoff_detected = self._detect_handoff_indicators(ai_response)
            
            # Determine next phase based on teaching completion and handoff detection
            if is_complete and handoff_detected:
                logger.info(f"[{request_id}] 🎯 AI HANDOFF DETECTED: Teaching complete, transitioning to quiz_initiate")
                state_updates['handoff_detected'] = True
                state_updates['handoff_timestamp'] = datetime.now(timezone.utc).isoformat()
                return 'quiz_initiate', state_updates
            elif is_complete:
                logger.info(f"[{request_id}] 🎓 Teaching complete but no handoff detected yet")
                # Stay in teaching phase but mark as complete
                return 'teaching', state_updates
            else:
                # Teaching not complete, enforce phase consistency
                corrected_phase, corrected_response, correction_reason = enforce_phase_consistency(
                    'teaching', ai_response, session_data, context
                )
                
                if corrected_phase != 'teaching':
                    logger.warning(f"[{request_id}] ⚠️ Phase consistency enforced: {correction_reason}")
                    state_updates['phase_correction_applied'] = True
                    state_updates['correction_reason'] = correction_reason
                
                # Stay in teaching phase
                return 'teaching', state_updates
                
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error in teaching handler: {e}")
            # Safe fallback - stay in teaching
            return 'teaching', {'error': str(e)}
    
    def _detect_handoff_indicators(self, ai_response: str) -> bool:
        """
        Detect AI handoff indicators in the response
        
        Args:
            ai_response: AI-generated response
            
        Returns:
            True if handoff indicators are detected
        """
        try:
            response_lower = ai_response.lower()
            
            # Check for handoff indicators
            for indicator in self.handoff_indicators:
                if indicator in response_lower:
                    logger.info(f"🎯 AI HANDOFF INDICATOR DETECTED: '{indicator}'")
                    return True
            
            # Check for quiz question patterns
            quiz_patterns = [
                r"question 1[:.)]",
                r"first question[:.)]",
                r"quiz question[:.)]",
                r"multiple choice",
                r"choose the correct",
                r"select the best",
                r"true or false"
            ]
            
            import re
            for pattern in quiz_patterns:
                if re.search(pattern, response_lower):
                    logger.info(f"🎯 AI HANDOFF QUIZ PATTERN DETECTED: '{pattern}'")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error detecting handoff indicators: {e}")
            return False
    
    def apply_intelligent_guardrails_to_response(self,
                                              ai_response: str,
                                              session_data: Dict[str, Any],
                                              context: Dict[str, Any],
                                              request_id: str) -> str:
        """
        Apply intelligent guardrails to AI response
        
        Args:
            ai_response: AI-generated response
            session_data: Current session state
            context: Additional context data
            request_id: Request identifier for logging
            
        Returns:
            Enhanced AI response with guardrails applied
        """
        try:
            # Check if teaching is truly complete
            teaching_truly_complete, _, _ = validate_teaching_completion(session_data, context)
            
            # Apply intelligent guardrails
            is_valid, violations, enhanced_response = apply_intelligent_guardrails(
                ai_response, context, session_data, teaching_truly_complete, request_id
            )
            
            if not is_valid:
                logger.warning(f"[{request_id}] ⚠️ Guardrails applied to AI response: {len(violations)} violations")
                return enhanced_response
            
            return ai_response
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error applying guardrails: {e}")
            return ai_response

# Global instance for use throughout the application
teaching_phase_handler = TeachingPhaseHandler()