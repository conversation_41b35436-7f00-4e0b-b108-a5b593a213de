#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Identify Legacy Lesson Flow Paths

This script identifies all alternative lesson flow implementations that need to be
removed or refactored to support the unified pathway sequence.

The unified lesson pathway is defined as:
teaching_start/teaching → quiz_initiate → quiz_questions → quiz_results → final_report_inprogress → complete
"""

import os
import re
import sys
import logging
import json
from typing import List, Dict, Any, Tuple, Optional, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Constants
MAIN_PY_PATH = "backend/cloud_function/lesson_manager/main.py"
PHASE_TRANSITION_INTEGRITY_PATH = "backend/cloud_function/lesson_manager/phase_transition_integrity.py"
TEACHING_RULES_PATH = "backend/cloud_function/lesson_manager/teaching_rules.py"
FRONTEND_PATH = "frontend/lesson-platform/src/app/classroom/ClassroomContent.tsx"
DIAGNOSTIC_PROGRESS_PATH = "frontend/lesson-platform/src/components/DiagnosticProgress.tsx"

# Unified pathway phases
UNIFIED_PHASES = [
    'teaching_start', 'teaching', 'quiz_initiate', 
    'quiz_questions', 'quiz_results', 'final_report_inprogress', 'complete'
]

# Valid transitions in the unified pathway
UNIFIED_TRANSITIONS = {
    'teaching_start': ['teaching', 'quiz_initiate'],
    'teaching': ['quiz_initiate'],
    'quiz_initiate': ['quiz_questions'],
    'quiz_questions': ['quiz_questions', 'quiz_results'],
    'quiz_results': ['final_report_inprogress'],
    'final_report_inprogress': ['complete'],
    'complete': []
}

def read_file(file_path: str) -> str:
    """Read a file and return its contents."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return ""

def find_phase_definitions(content: str) -> Dict[str, List[str]]:
    """Find all phase definitions in the code."""
    phase_definitions = {}
    
    # Find LessonPhase class definition
    lesson_phase_match = re.search(r'class LessonPhase.*?:', content, re.DOTALL)
    if lesson_phase_match:
        # Extract all phase constants
        phase_constants = re.findall(r'(\w+)\s*=\s*[\'"]([^\'"]+)[\'"]', content[lesson_phase_match.start():])
        for name, value in phase_constants:
            phase_definitions[name] = value
    
    # Find other phase definitions
    phase_constants = re.findall(r'LESSON_PHASE_(\w+)\s*=\s*[\'"]([^\'"]+)[\'"]', content)
    for name, value in phase_constants:
        phase_definitions[f"LESSON_PHASE_{name}"] = value
    
    return phase_definitions

def find_phase_transitions(content: str) -> Dict[str, List[str]]:
    """Find all phase transition definitions in the code."""
    transitions = {}
    
    # Find VALID_TRANSITIONS dictionary
    valid_transitions_match = re.search(r'VALID_TRANSITIONS\s*=\s*\{.*?\}', content, re.DOTALL)
    if valid_transitions_match:
        transitions_text = valid_transitions_match.group(0)
        
        # Extract transitions using regex
        transition_patterns = re.findall(r'[\'"]?(\w+)[\'"]?\s*:\s*\[(.*?)\]', transitions_text, re.DOTALL)
        for from_phase, to_phases_text in transition_patterns:
            # Extract to_phases
            to_phases = re.findall(r'[\'"]?(\w+)[\'"]?', to_phases_text)
            transitions[from_phase] = to_phases
    
    return transitions

def find_phase_handlers(content: str) -> Dict[str, str]:
    """Find all phase handler functions in the code."""
    handlers = {}
    
    # Find handle_* functions
    handler_matches = re.finditer(r'(async\s+)?def\s+(handle_\w+)_phase\(', content)
    for match in handler_matches:
        handler_name = match.group(2)
        phase_name = handler_name.replace('handle_', '')
        handlers[phase_name] = handler_name
    
    return handlers

def find_alternative_paths(content: str) -> List[str]:
    """Find all alternative lesson retrieval paths in the code."""
    alternative_paths = []
    
    # Find alternative_paths list
    alt_paths_match = re.search(r'alternative_paths\s*=\s*\[(.*?)\]', content, re.DOTALL)
    if alt_paths_match:
        paths_text = alt_paths_match.group(1)
        
        # Extract paths using regex
        path_patterns = re.findall(r'[\'"]([^\'"]+)[\'"]', paths_text)
        alternative_paths.extend(path_patterns)
    
    return alternative_paths

def find_phase_normalizations(content: str) -> Dict[str, str]:
    """Find all phase normalization mappings in the code."""
    normalizations = {}
    
    # Find _normalize_phase_name function
    normalize_match = re.search(r'def\s+_normalize_phase_name.*?return\s+\w+', content, re.DOTALL)
    if normalize_match:
        normalize_text = normalize_match.group(0)
        
        # Extract mappings using regex
        mapping_patterns = re.findall(r'if\s+(.*?):\s*return\s+(\w+)', normalize_text, re.DOTALL)
        for condition, target_phase in mapping_patterns:
            normalizations[condition.strip()] = target_phase
    
    return normalizations

def find_frontend_phase_handling(content: str) -> Dict[str, Any]:
    """Find frontend phase handling logic."""
    frontend_handling = {
        "phase_detection": [],
        "phase_transitions": [],
        "ui_components": []
    }
    
    # Find phase detection logic
    phase_detection_matches = re.finditer(r'(currentLessonPhase|current_phase)\s*===?\s*[\'"](\w+)[\'"]', content)
    for match in phase_detection_matches:
        frontend_handling["phase_detection"].append(match.group(2))
    
    # Find phase transition logic
    phase_transition_matches = re.finditer(r'setCurrentLessonPhase\([\'"](\w+)[\'"]\)', content)
    for match in phase_transition_matches:
        frontend_handling["phase_transitions"].append(match.group(1))
    
    # Find UI components that depend on phases
    ui_component_matches = re.finditer(r'(currentLessonPhase|current_phase)\s*===?\s*[\'"](\w+)[\'"]\s*\?\s*<', content)
    for match in ui_component_matches:
        frontend_handling["ui_components"].append(match.group(2))
    
    return frontend_handling

def find_diagnostic_progress_phases(content: str) -> List[str]:
    """Find phases used in DiagnosticProgress component."""
    phases = []
    
    # Find phase checks in DiagnosticProgress
    phase_matches = re.finditer(r'currentPhase\?\.includes\([\'"](\w+)[\'"]\)', content)
    for match in phase_matches:
        phases.append(match.group(1))
    
    # Find explicit phase checks
    explicit_matches = re.finditer(r'currentPhase\s*===?\s*[\'"](\w+)[\'"]', content)
    for match in explicit_matches:
        phases.append(match.group(1))
    
    return phases

def analyze_main_py():
    """Analyze main.py for legacy code paths."""
    content = read_file(MAIN_PY_PATH)
    if not content:
        return {}
    
    results = {
        "phase_definitions": find_phase_definitions(content),
        "phase_transitions": find_phase_transitions(content),
        "phase_handlers": find_phase_handlers(content),
        "alternative_paths": find_alternative_paths(content),
        "phase_normalizations": find_phase_normalizations(content)
    }
    
    return results

def analyze_phase_transition_integrity():
    """Analyze phase_transition_integrity.py for legacy code paths."""
    content = read_file(PHASE_TRANSITION_INTEGRITY_PATH)
    if not content:
        return {}
    
    results = {
        "phase_transitions": find_phase_transitions(content)
    }
    
    return results

def analyze_teaching_rules():
    """Analyze teaching_rules.py for legacy code paths."""
    content = read_file(TEACHING_RULES_PATH)
    if not content:
        return {}
    
    # Find AI instructor role boundaries
    ai_role_boundaries = []
    role_boundaries_match = re.search(r'AI Instructor Role Definition:.*?"""', content, re.DOTALL)
    if role_boundaries_match:
        boundaries_text = role_boundaries_match.group(0)
        ai_role_boundaries = [line.strip('- ') for line in boundaries_text.split('\n') if line.strip().startswith('-')]
    
    results = {
        "ai_role_boundaries": ai_role_boundaries
    }
    
    return results

def analyze_frontend():
    """Analyze frontend code for legacy phase handling."""
    content = read_file(FRONTEND_PATH)
    if not content:
        return {}
    
    results = {
        "frontend_phase_handling": find_frontend_phase_handling(content)
    }
    
    return results

def analyze_diagnostic_progress():
    """Analyze DiagnosticProgress component for legacy phase handling."""
    content = read_file(DIAGNOSTIC_PROGRESS_PATH)
    if not content:
        return {}
    
    results = {
        "diagnostic_progress_phases": find_diagnostic_progress_phases(content)
    }
    
    return results

def identify_legacy_phases(analysis_results):
    """Identify legacy phases that are not part of the unified pathway."""
    all_phases = set()
    
    # Collect all phases from various sources
    if "phase_definitions" in analysis_results.get("main_py", {}):
        all_phases.update(analysis_results["main_py"]["phase_definitions"].values())
    
    if "phase_transitions" in analysis_results.get("main_py", {}):
        all_phases.update(analysis_results["main_py"]["phase_transitions"].keys())
        for to_phases in analysis_results["main_py"]["phase_transitions"].values():
            all_phases.update(to_phases)
    
    if "phase_handlers" in analysis_results.get("main_py", {}):
        all_phases.update(analysis_results["main_py"]["phase_handlers"].keys())
    
    if "frontend_phase_handling" in analysis_results.get("frontend", {}):
        all_phases.update(analysis_results["frontend"]["frontend_phase_handling"]["phase_detection"])
        all_phases.update(analysis_results["frontend"]["frontend_phase_handling"]["phase_transitions"])
        all_phases.update(analysis_results["frontend"]["frontend_phase_handling"]["ui_components"])
    
    if "diagnostic_progress_phases" in analysis_results.get("diagnostic_progress", {}):
        all_phases.update(analysis_results["diagnostic_progress"]["diagnostic_progress_phases"])
    
    # Identify legacy phases
    legacy_phases = all_phases - set(UNIFIED_PHASES)
    
    return sorted(list(legacy_phases))

def identify_legacy_transitions(analysis_results):
    """Identify legacy transitions that are not part of the unified pathway."""
    legacy_transitions = []
    
    # Check main.py transitions
    if "phase_transitions" in analysis_results.get("main_py", {}):
        for from_phase, to_phases in analysis_results["main_py"]["phase_transitions"].items():
            # Skip if from_phase is not in unified pathway
            if from_phase not in UNIFIED_TRANSITIONS:
                continue
            
            # Check if to_phases are valid for this from_phase
            valid_to_phases = set(UNIFIED_TRANSITIONS.get(from_phase, []))
            for to_phase in to_phases:
                if to_phase not in valid_to_phases:
                    legacy_transitions.append((from_phase, to_phase))
    
    # Check phase_transition_integrity.py transitions
    if "phase_transitions" in analysis_results.get("phase_transition_integrity", {}):
        for from_phase, to_phases in analysis_results["phase_transition_integrity"]["phase_transitions"].items():
            # Skip if from_phase is not in unified pathway
            if from_phase not in UNIFIED_TRANSITIONS:
                continue
            
            # Check if to_phases are valid for this from_phase
            valid_to_phases = set(UNIFIED_TRANSITIONS.get(from_phase, []))
            for to_phase in to_phases:
                if to_phase not in valid_to_phases:
                    legacy_transitions.append((from_phase, to_phase))
    
    return legacy_transitions

def generate_audit_report(analysis_results, legacy_phases, legacy_transitions):
    """Generate a comprehensive audit report."""
    report = {
        "unified_pathway": {
            "phases": UNIFIED_PHASES,
            "transitions": UNIFIED_TRANSITIONS
        },
        "legacy_phases": legacy_phases,
        "legacy_transitions": legacy_transitions,
        "analysis_results": analysis_results
    }
    
    return report

def main():
    """Main function to identify legacy lesson flow paths."""
    logger.info("Starting identification of legacy lesson flow paths")
    
    # Analyze main.py
    logger.info("Analyzing main.py")
    main_py_results = analyze_main_py()
    
    # Analyze phase_transition_integrity.py
    logger.info("Analyzing phase_transition_integrity.py")
    phase_transition_integrity_results = analyze_phase_transition_integrity()
    
    # Analyze teaching_rules.py
    logger.info("Analyzing teaching_rules.py")
    teaching_rules_results = analyze_teaching_rules()
    
    # Analyze frontend code
    logger.info("Analyzing frontend code")
    frontend_results = analyze_frontend()
    
    # Analyze DiagnosticProgress component
    logger.info("Analyzing DiagnosticProgress component")
    diagnostic_progress_results = analyze_diagnostic_progress()
    
    # Combine all analysis results
    analysis_results = {
        "main_py": main_py_results,
        "phase_transition_integrity": phase_transition_integrity_results,
        "teaching_rules": teaching_rules_results,
        "frontend": frontend_results,
        "diagnostic_progress": diagnostic_progress_results
    }
    
    # Identify legacy phases and transitions
    logger.info("Identifying legacy phases")
    legacy_phases = identify_legacy_phases(analysis_results)
    
    logger.info("Identifying legacy transitions")
    legacy_transitions = identify_legacy_transitions(analysis_results)
    
    # Generate audit report
    logger.info("Generating audit report")
    audit_report = generate_audit_report(analysis_results, legacy_phases, legacy_transitions)
    
    # Write audit report to file
    report_path = "backend/cloud_function/lesson_manager/legacy_paths_audit_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(audit_report, f, indent=2)
    
    logger.info(f"Audit report written to {report_path}")
    
    # Print summary
    logger.info(f"Found {len(legacy_phases)} legacy phases")
    for phase in legacy_phases:
        logger.info(f"  - {phase}")
    
    logger.info(f"Found {len(legacy_transitions)} legacy transitions")
    for from_phase, to_phase in legacy_transitions:
        logger.info(f"  - {from_phase} → {to_phase}")
    
    logger.info("Legacy path identification complete")

if __name__ == "__main__":
    main()