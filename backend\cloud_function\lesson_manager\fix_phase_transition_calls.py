#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Fix Phase Transition Calls

This script fixes the remaining phase transition calls that were not updated by the
clean_redundant_phase_logic.py script.
"""

import os
import re
import sys
import logging
from typing import List, Dict, Any, Tuple, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Constants
MAIN_PY_PATH = "backend/cloud_function/lesson_manager/main.py"

def read_file(file_path: str) -> str:
    """Read a file and return its contents."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return ""

def write_file(file_path: str, content: str) -> bool:
    """Write content to a file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        logger.error(f"Error writing to file {file_path}: {e}")
        return False

def fix_remaining_phase_transition_calls(content: str) -> str:
    """Fix remaining phase transition calls."""
    
    # Replace the specific line with the old phase transition call
    old_code = "            # Validate phase transition\n            if self._is_valid_phase_transition(current_state.get('current_phase'), new_phase):"
    new_code = """            # Validate phase transition using consolidated handler
            valid_phase, state_updates = handle_phase_transition(
                current_state.get('current_phase', 'unknown'), new_phase, current_state, "session-update"
            )
            new_phase = valid_phase
            if state_updates:
                for key, value in state_updates.items():
                    current_state[key] = value
            
            # Continue with phase update
            if True:  # Always proceed with the validated phase"""
    
    updated_content = content.replace(old_code, new_code)
    
    if old_code in content:
        logger.info("Updated remaining phase transition call")
    else:
        logger.warning("Could not find the specific phase transition call")
    
    # Try a more flexible approach with regex to find and replace the method
    pattern = r'def _is_valid_phase_transition\(self, current_phase, new_phase\):.*?return new_phase in valid_transitions\.get\(current_phase, \[\]\)'
    
    new_method = """    def _is_valid_phase_transition(self, current_phase, new_phase):
        '''
        DEPRECATED: Use handle_phase_transition instead
        This method is kept for backward compatibility but delegates to the consolidated handler
        '''
        # Use the consolidated handler via LessonPhase
        return LessonPhase.validate_transition(current_phase, new_phase)"""
    
    match = re.search(pattern, updated_content, re.DOTALL)
    if match:
        start, end = match.span()
        updated_content = updated_content[:start] + new_method + updated_content[end:]
        logger.info("Updated _is_valid_phase_transition method using regex")
    else:
        logger.warning("Could not find _is_valid_phase_transition method")
    
    return updated_content

def main():
    """Main function to fix remaining phase transition calls."""
    logger.info("Starting fix for remaining phase transition calls")
    
    # Read the main.py file
    main_py_content = read_file(MAIN_PY_PATH)
    if not main_py_content:
        logger.error(f"Could not read {MAIN_PY_PATH}")
        return
    
    # Fix remaining phase transition calls
    logger.info("Fixing remaining phase transition calls")
    main_py_content = fix_remaining_phase_transition_calls(main_py_content)
    
    # Write the updated main.py file
    logger.info(f"Writing updated {MAIN_PY_PATH}")
    if not write_file(MAIN_PY_PATH, main_py_content):
        logger.error(f"Could not write to {MAIN_PY_PATH}")
        return
    
    logger.info("Successfully fixed remaining phase transition calls")

if __name__ == "__main__":
    main()