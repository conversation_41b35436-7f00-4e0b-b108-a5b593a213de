"""
Example script demonstrating how to use the data persistence functionality.

This script shows how to generate structured data and persist it to Firestore.
"""

import json
import logging
from data_persistence import DataPersistenceManager, persist_data_to_firestore

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main function demonstrating data persistence functionality"""
    # Sample session data
    session_data = {
        'interactions': [
            {'role': 'assistant', 'content': 'Welcome to this introduction lesson on mathematics.'},
            {'role': 'user', 'content': 'I am ready to learn.'},
            {'role': 'assistant', 'content': 'Great! Let\'s start with the concept of fractions.'},
            {'role': 'user', 'content': 'I understand fractions are parts of a whole.'},
            {'role': 'assistant', 'content': 'Exactly! Now let\'s move to a quiz.', 'phase': 'quiz_initiate'}
        ],
        'quiz_responses': [
            {
                'question': 'What is 1/2 + 1/4?',
                'student_answer': '3/4',
                'correct_answer': '3/4',
                'is_correct': True
            },
            {
                'question': 'Simplify 4/8.',
                'student_answer': '1/2',
                'correct_answer': '1/2',
                'is_correct': True
            }
        ],
        'lesson_metadata': {
            'grade_level': 'Grade 5',
            'subject': 'Mathematics',
            'title': 'Introduction to Fractions',
            'learning_objectives': [
                'Understand fraction concepts',
                'Add and subtract fractions',
                'Simplify fractions'
            ]
        },
        'student_info': {
            'name': 'Test Student',
            'grade': 'Grade 5'
        }
    }
    
    # Create DataPersistenceManager instance
    manager = DataPersistenceManager(session_data)
    
    # Generate all data structures
    logger.info("Generating all data structures...")
    all_data = manager.generate_all_data()
    
    # Print the generated data (pretty-printed JSON)
    logger.info("Generated data structures:")
    print(json.dumps(all_data, indent=2, default=str))
    
    # Example of how to persist to Firestore (commented out)
    """
    # Import Firebase Admin SDK
    import firebase_admin
    from firebase_admin import credentials, firestore
    
    # Initialize Firebase (assuming credentials are set up)
    if not firebase_admin._apps:
        cred = credentials.ApplicationDefault()
        firebase_admin.initialize_app(cred)
    
    # Get Firestore database
    db = firestore.client()
    
    # Persist data to Firestore
    session_id = 'example-session-123'
    success = persist_data_to_firestore(db, session_id, all_data)
    
    if success:
        logger.info(f"Successfully persisted data for session {session_id}")
    else:
        logger.error(f"Failed to persist data for session {session_id}")
    """

if __name__ == "__main__":
    main()