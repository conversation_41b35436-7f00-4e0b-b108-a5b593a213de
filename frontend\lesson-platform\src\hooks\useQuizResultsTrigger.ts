import { useEffect, useRef } from 'react';

/**
 * Hook to automatically trigger final report generation when quiz_results phase is detected
 * 
 * @param currentLessonPhase - The current lesson phase from the backend
 * @param handleAiInteraction - Function to send messages to the backend
 * @param sessionId - The current session ID
 * @returns void
 */
export function useQuizResultsTrigger(
  currentLessonPhase: string | null,
  handleAiInteraction: (message: string, isSystemMessage: boolean, sessionId: string) => void,
  sessionId: string | null
): void {
  // Use a ref to track if we've already sent the trigger for this phase
  const triggerSentRef = useRef<boolean>(false);

  useEffect(() => {
    // Only proceed if we have all required data
    if (!currentLessonPhase || !sessionId || !handleAiInteraction) {
      return;
    }

    // Check if the current phase is quiz_results and we haven't sent the trigger yet
    if (currentLessonPhase === 'quiz_results' && !triggerSentRef.current) {
      console.log('📊 QUIZ_RESULTS phase detected - Automatically triggering final report generation');
      
      // Set a small delay to ensure the UI has updated and the user sees the quiz results first
      const triggerTimeout = setTimeout(() => {
        // Send the system trigger message
        handleAiInteraction('[System: Generate final lesson report]', true, sessionId);
        
        // Mark that we've sent the trigger
        triggerSentRef.current = true;
        
        console.log('📊 Final report generation trigger sent');
      }, 2000); // 2-second delay
      
      return () => clearTimeout(triggerTimeout);
    }
    
    // Reset the trigger sent flag if we're no longer in quiz_results phase
    if (currentLessonPhase !== 'quiz_results') {
      triggerSentRef.current = false;
    }
  }, [currentLessonPhase, handleAiInteraction, sessionId]);
}