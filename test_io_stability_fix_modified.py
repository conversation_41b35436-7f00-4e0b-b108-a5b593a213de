#!/usr/bin/env python3
"""
Test script for I/O Operation Stability Fix in ImmediateConsoleHandler.

This script tests the fix for task 1 of the unified-lesson-flow-refactor spec:
- Modify the ImmediateConsoleHandler class to check if sys.stdout and sys.stderr are closed before writing
- Add proper exception handling for ValueError and AttributeError when accessing closed streams
- Test the fix with various logging scenarios to ensure stability

Requirements tested: 5.1, 5.2, 5.3, 5.4, 5.5
"""

import sys
import logging
import io
import os
import tempfile
import contextlib
from unittest.mock import Mock, patch

# Import the ImmediateConsoleHandler from main.py
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager'))

# We need to import the class after setting up the path
try:
    from main import ImmediateConsoleHandler
    print("✓ Successfully imported ImmediateConsoleHandler from main.py")
except ImportError as e:
    print(f"✗ Failed to import ImmediateConsoleHandler: {e}")
    sys.exit(1)

class TestIOStabilityFix:
    """Test suite for I/O Operation Stability Fix."""
    
    def __init__(self):
        self.test_results = []
        self.setup_test_logger()
    
    def setup_test_logger(self):
        """Set up a test logger with our fixed ImmediateConsoleHandler."""
        self.logger = logging.getLogger('test_io_stability')
        self.logger.setLevel(logging.DEBUG)
        
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Add our fixed handler
        self.handler = ImmediateConsoleHandler()
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        self.handler.setFormatter(formatter)
        self.logger.addHandler(self.handler)
    
    def log_test_result(self, test_name, passed, details=""):
        """Log test result."""
        status = "PASS" if passed else "FAIL"
        result = f"[{status}] {test_name}"
        if details:
            result += f" - {details}"
        print(result)
        self.test_results.append((test_name, passed, details))
    
    def test_normal_logging(self):
        """Test 1: Normal logging scenario - should work without issues."""
        test_name = "Normal Logging"
        try:
            self.logger.info("Test message for normal logging")
            self.logger.debug("Debug message")
            self.logger.warning("Warning message")
            self.logger.error("Error message")
            self.log_test_result(test_name, True, "All log levels work correctly")
        except Exception as e:
            self.log_test_result(test_name, False, f"Exception: {e}")
    
    def test_closed_stdout(self):
        """Test 2: Logging when stdout is closed - should handle gracefully."""
        test_name = "Closed stdout handling"
        
        # Create a log record
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message with closed stdout", args=(), exc_info=None
        )
        
        # Use patch to simulate closed stdout
        with patch('sys.stdout') as mock_stdout:
            # Configure mock to raise ValueError when write is called
            mock_stdout.write.side_effect = ValueError("I/O operation on closed file")
            
            try:
                # Test our handler directly - should not crash
                self.handler.emit(record)
                self.log_test_result(test_name, True, "No exception when stdout is closed")
            except Exception as e:
                self.log_test_result(test_name, False, f"Exception: {e}")
    
    def test_closed_stderr(self):
        """Test 3: Logging when stderr is closed - should handle gracefully."""
        test_name = "Closed stderr handling"
        
        # Create a log record
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message with closed stderr", args=(), exc_info=None
        )
        
        # Use patch to simulate closed stderr
        with patch('sys.stderr') as mock_stderr:
            # Configure mock to raise ValueError when write is called
            mock_stderr.write.side_effect = ValueError("I/O operation on closed file")
            
            try:
                # Test our handler directly - should not crash
                self.handler.emit(record)
                self.log_test_result(test_name, True, "No exception when stderr is closed")
            except Exception as e:
                self.log_test_result(test_name, False, f"Exception: {e}")
    
    def test_both_streams_closed(self):
        """Test 4: Logging when both stdout and stderr are closed."""
        test_name = "Both streams closed"
        
        # Create a log record
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message with both streams closed", args=(), exc_info=None
        )
        
        # Use patch to simulate both streams closed
        with patch('sys.stdout') as mock_stdout, patch('sys.stderr') as mock_stderr:
            # Configure mocks to raise ValueError when write is called
            mock_stdout.write.side_effect = ValueError("I/O operation on closed file")
            mock_stderr.write.side_effect = ValueError("I/O operation on closed file")
            
            try:
                # Test our handler directly - should not crash
                self.handler.emit(record)
                self.log_test_result(test_name, True, "No exception when both streams are closed")
            except Exception as e:
                self.log_test_result(test_name, False, f"Exception: {e}")
    
    def test_none_streams(self):
        """Test 5: Logging when streams are None - should handle AttributeError."""
        test_name = "None streams handling"
        
        # Create a log record
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message with None streams", args=(), exc_info=None
        )
        
        # Use patch to simulate None streams
        with patch('sys.stdout', None), patch('sys.stderr', None):
            try:
                # Test our handler directly - should not crash
                self.handler.emit(record)
                self.log_test_result(test_name, True, "No exception when streams are None")
            except Exception as e:
                self.log_test_result(test_name, False, f"Exception: {e}")
    
    def test_mock_streams_without_closed_attribute(self):
        """Test 6: Logging with mock streams that don't have 'closed' attribute."""
        test_name = "Streams without 'closed' attribute"
        
        # Create a log record
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message with mock streams", args=(), exc_info=None
        )
        
        # Use patch to simulate streams without 'closed' attribute
        with patch('sys.stdout') as mock_stdout, patch('sys.stderr') as mock_stderr:
            # Remove 'closed' attribute if it exists
            if hasattr(mock_stdout, 'closed'):
                delattr(mock_stdout, 'closed')
            if hasattr(mock_stderr, 'closed'):
                delattr(mock_stderr, 'closed')
            
            try:
                # Test our handler directly - should not crash
                self.handler.emit(record)
                self.log_test_result(test_name, True, "No exception with streams lacking 'closed' attribute")
            except Exception as e:
                self.log_test_result(test_name, False, f"Exception: {e}")
    
    def test_unicode_handling_with_closed_streams(self):
        """Test 7: Unicode message handling when streams are closed."""
        test_name = "Unicode handling with closed streams"
        
        # Create a log record with Unicode
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message with Unicode: 🚀 ✅ → 📊", args=(), exc_info=None
        )
        
        # Use patch to simulate closed streams
        with patch('sys.stdout') as mock_stdout, patch('sys.stderr') as mock_stderr:
            # Configure mocks to raise ValueError when write is called
            mock_stdout.write.side_effect = ValueError("I/O operation on closed file")
            mock_stderr.write.side_effect = ValueError("I/O operation on closed file")
            
            try:
                # Test our handler directly - should not crash
                self.handler.emit(record)
                self.log_test_result(test_name, True, "Unicode handling works with closed streams")
            except Exception as e:
                self.log_test_result(test_name, False, f"Exception: {e}")
    
    def test_exception_in_format(self):
        """Test 8: Exception during message formatting - should handle gracefully."""
        test_name = "Exception during formatting"
        
        try:
            # Create a record that might cause formatting issues
            record = logging.LogRecord(
                name="test", level=logging.INFO, pathname="", lineno=0,
                msg="Test %s %d", args=("string", "not_a_number"), exc_info=None
            )
            
            # Try to emit the problematic record
            self.handler.emit(record)
            self.log_test_result(test_name, True, "Handled formatting exception gracefully")
            
        except Exception as e:
            self.log_test_result(test_name, False, f"Exception: {e}")
    
    def test_high_volume_logging(self):
        """Test 9: High volume logging to test stability under load."""
        test_name = "High volume logging"
        
        try:
            # Log many messages quickly
            for i in range(100):
                self.logger.info(f"High volume test message {i}")
            
            self.log_test_result(test_name, True, "Handled 100 log messages without issues")
            
        except Exception as e:
            self.log_test_result(test_name, False, f"Exception: {e}")
    
    def test_concurrent_logging(self):
        """Test 10: Concurrent logging from multiple threads."""
        test_name = "Concurrent logging"
        
        import threading
        import time
        
        def log_worker(worker_id):
            """Worker function for concurrent logging."""
            for i in range(10):
                self.logger.info(f"Worker {worker_id} - Message {i}")
                time.sleep(0.01)  # Small delay to simulate real work
        
        try:
            # Create and start multiple threads
            threads = []
            for i in range(5):
                thread = threading.Thread(target=log_worker, args=(i,))
                threads.append(thread)
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            self.log_test_result(test_name, True, "Handled concurrent logging from 5 threads")
            
        except Exception as e:
            self.log_test_result(test_name, False, f"Exception: {e}")
    
    def run_all_tests(self):
        """Run all test scenarios."""
        print("=" * 80)
        print("I/O OPERATION STABILITY FIX - TEST SUITE")
        print("=" * 80)
        print("Testing ImmediateConsoleHandler improvements for:")
        print("- Requirement 5.1: Check if streams are closed before writing")
        print("- Requirement 5.2: Handle closed file operations gracefully")
        print("- Requirement 5.3: Prevent logging errors from crashing application")
        print("- Requirement 5.4: Verify stream availability before writing")
        print("- Requirement 5.5: Continue operating without interruption on stream errors")
        print("=" * 80)
        print()
        
        # Run all test methods
        test_methods = [
            self.test_normal_logging,
            self.test_closed_stdout,
            self.test_closed_stderr,
            self.test_both_streams_closed,
            self.test_none_streams,
            self.test_mock_streams_without_closed_attribute,
            self.test_unicode_handling_with_closed_streams,
            self.test_exception_in_format,
            self.test_high_volume_logging,
            self.test_concurrent_logging
        ]
        
        for test_method in test_methods:
            try:
                test_method()
            except Exception as e:
                test_name = test_method.__name__.replace('test_', '').replace('_', ' ').title()
                self.log_test_result(test_name, False, f"Test method exception: {e}")
        
        # Print summary
        print("\n" + "=" * 80)
        print("TEST SUMMARY")
        print("=" * 80)
        
        passed_tests = sum(1 for _, passed, _ in self.test_results if passed)
        total_tests = len(self.test_results)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests / total_tests * 100):.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! I/O Operation Stability Fix is working correctly.")
            return True
        else:
            print(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Please review the implementation.")
            
            # Print failed tests
            print("\nFailed Tests:")
            for test_name, passed, details in self.test_results:
                if not passed:
                    print(f"  - {test_name}: {details}")
            
            return False

def main():
    """Main test execution function."""
    tester = TestIOStabilityFix()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ I/O Operation Stability Fix implementation is complete and working correctly!")
        print("✅ All requirements (5.1, 5.2, 5.3, 5.4, 5.5) have been satisfied.")
        return 0
    else:
        print("\n❌ Some tests failed. The implementation needs review.")
        return 1

if __name__ == "__main__":
    sys.exit(main())