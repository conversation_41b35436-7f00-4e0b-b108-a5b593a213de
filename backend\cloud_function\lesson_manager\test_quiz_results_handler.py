# -*- coding: utf-8 -*-
"""
Test Quiz Results Handler

This script tests the quiz_results_handler implementation for the unified lesson flow state machine.
It verifies that the handler correctly:
1. Calculates final scores and displays comprehensive results
2. Pauses execution and waits for system trigger
3. Only transitions to final_report_inprogress when trigger is received
"""

import sys
import os
import json
import logging
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Import the quiz_results_handler
    from quiz_results_handler import quiz_results_handler
    logger.info("✅ Successfully imported quiz_results_handler")
except Exception as e:
    logger.error(f"❌ Failed to import quiz_results_handler: {e}")
    sys.exit(1)

# Test data
def create_test_session_data() -> Dict[str, Any]:
    """Create test session data for quiz results handler"""
    return {
        'current_phase': 'quiz_results',
        'quiz_answers': [
            {
                'question_index': 0,
                'question_text': 'What is the main topic of this lesson?',
                'user_answer': 'A',
                'correct_answer': 'A. The correct answer',
                'options': ['A. The correct answer', 'B. Wrong answer', 'C. Another wrong answer', 'D. Yet another wrong answer'],
                'is_correct': True,
                'timestamp': datetime.now(timezone.utc).isoformat()
            },
            {
                'question_index': 1,
                'question_text': 'Which of the following best describes the concept?',
                'user_answer': 'B',
                'correct_answer': 'C. The correct answer',
                'options': ['A. Wrong answer', 'B. Wrong answer', 'C. The correct answer', 'D. Wrong answer'],
                'is_correct': False,
                'timestamp': datetime.now(timezone.utc).isoformat()
            },
            {
                'question_index': 2,
                'question_text': 'What is the result of the calculation?',
                'user_answer': 'D',
                'correct_answer': 'D. The correct answer',
                'options': ['A. Wrong answer', 'B. Wrong answer', 'C. Wrong answer', 'D. The correct answer'],
                'is_correct': True,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        ],
        'quiz_questions_generated': [
            {
                'question': 'What is the main topic of this lesson?',
                'options': ['A. The correct answer', 'B. Wrong answer', 'C. Another wrong answer', 'D. Yet another wrong answer'],
                'correct_answer': 'A. The correct answer',
                'type': 'multiple_choice'
            },
            {
                'question': 'Which of the following best describes the concept?',
                'options': ['A. Wrong answer', 'B. Wrong answer', 'C. The correct answer', 'D. Wrong answer'],
                'correct_answer': 'C. The correct answer',
                'type': 'multiple_choice'
            },
            {
                'question': 'What is the result of the calculation?',
                'options': ['A. Wrong answer', 'B. Wrong answer', 'C. Wrong answer', 'D. The correct answer'],
                'correct_answer': 'D. The correct answer',
                'type': 'multiple_choice'
            }
        ],
        'assigned_level_for_teaching': 5
    }

def create_test_context() -> Dict[str, Any]:
    """Create test context data for quiz results handler"""
    return {
        'student_name': 'Test Student',
        'topic': 'Test Topic',
        'subject': 'Test Subject',
        'grade': '5',
        'key_concepts': ['Concept 1', 'Concept 2', 'Concept 3'],
        'learning_objectives': ['Objective 1', 'Objective 2', 'Objective 3']
    }

async def test_quiz_results_handler():
    """Test the quiz_results_handler implementation"""
    logger.info("🧪 TESTING QUIZ RESULTS HANDLER")
    
    # Create test data
    session_data = create_test_session_data()
    context = create_test_context()
    request_id = 'test-request-id'
    
    # Test 1: Initial calculation of quiz results
    logger.info("🧪 Test 1: Initial calculation of quiz results")
    user_input = "Show me my quiz results"
    
    response, state_updates, next_phase = quiz_results_handler.handle_quiz_results(
        session_data, user_input, context, request_id
    )
    
    logger.info(f"Response: {response[:100]}...")
    logger.info(f"State updates: {json.dumps(state_updates, default=str)[:100]}...")
    logger.info(f"Next phase: {next_phase}")
    
    # Verify that we calculated scores and remained in quiz_results phase
    assert next_phase == 'quiz_results', f"Expected next_phase to be 'quiz_results', got '{next_phase}'"
    assert state_updates.get('quiz_score_calculated') == True, "Expected quiz_score_calculated to be True"
    assert state_updates.get('current_phase') == 'quiz_results', "Expected current_phase to be 'quiz_results'"
    assert state_updates.get('waiting_for_trigger') == True, "Expected waiting_for_trigger to be True"
    assert 'quiz_score' in state_updates, "Expected quiz_score in state_updates"
    
    logger.info("✅ Test 1 passed: Quiz results calculated correctly")
    
    # Update session data with state updates
    session_data.update(state_updates)
    
    # Test 2: Subsequent request without trigger
    logger.info("🧪 Test 2: Subsequent request without trigger")
    user_input = "What's my score?"
    
    response, state_updates, next_phase = quiz_results_handler.handle_quiz_results(
        session_data, user_input, context, request_id
    )
    
    logger.info(f"Response: {response[:100]}...")
    logger.info(f"State updates: {json.dumps(state_updates, default=str)[:100]}...")
    logger.info(f"Next phase: {next_phase}")
    
    # Verify that we remained in quiz_results phase
    assert next_phase == 'quiz_results', f"Expected next_phase to be 'quiz_results', got '{next_phase}'"
    assert state_updates.get('current_phase') == 'quiz_results', "Expected current_phase to be 'quiz_results'"
    assert state_updates.get('waiting_for_trigger') == True, "Expected waiting_for_trigger to be True"
    
    logger.info("✅ Test 2 passed: Remained in quiz_results phase without trigger")
    
    # Test 3: Request with trigger
    logger.info("🧪 Test 3: Request with trigger")
    user_input = "[System: Generate final lesson report]"
    
    response, state_updates, next_phase = quiz_results_handler.handle_quiz_results(
        session_data, user_input, context, request_id
    )
    
    logger.info(f"Response: {response[:100]}...")
    logger.info(f"State updates: {json.dumps(state_updates, default=str)[:100]}...")
    logger.info(f"Next phase: {next_phase}")
    
    # Verify that we transitioned to final_report_inprogress phase
    assert next_phase == 'final_report_inprogress', f"Expected next_phase to be 'final_report_inprogress', got '{next_phase}'"
    assert state_updates.get('current_phase') == 'final_report_inprogress', "Expected current_phase to be 'final_report_inprogress'"
    assert state_updates.get('final_report_generation_started') == True, "Expected final_report_generation_started to be True"
    
    logger.info("✅ Test 3 passed: Transitioned to final_report_inprogress phase with trigger")
    
    # Test 4: Error handling with empty quiz data
    logger.info("🧪 Test 4: Error handling with empty quiz data")
    
    # Create session data with empty quiz data to test error handling
    empty_quiz_session_data = {
        'current_phase': 'quiz_results',
        'quiz_answers': [],
        'quiz_questions_generated': []
    }
    user_input = "Show me my results"
    
    response, state_updates, next_phase = quiz_results_handler.handle_quiz_results(
        empty_quiz_session_data, user_input, context, request_id
    )
    
    logger.info(f"Response: {response[:100]}...")
    logger.info(f"State updates: {json.dumps(state_updates, default=str)[:100]}...")
    logger.info(f"Next phase: {next_phase}")
    
    # Verify that we handled the empty quiz data gracefully
    assert next_phase == 'quiz_results', f"Expected next_phase to be 'quiz_results', got '{next_phase}'"
    assert state_updates.get('current_phase') == 'quiz_results', "Expected current_phase to be 'quiz_results'"
    
    logger.info("✅ Test 4 passed: Error handled gracefully")
    
    logger.info("✅ ALL TESTS PASSED")

if __name__ == "__main__":
    asyncio.run(test_quiz_results_handler())