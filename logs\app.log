2025-07-22 12:42:36,488 - INFO - [main.py:71] - ============================================================
2025-07-22 12:42:36,494 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-22 12:42:36,495 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:42:36,495 - INFO - [main.py:74] - Platform: win32
2025-07-22 12:42:36,496 - INFO - [main.py:75] - ============================================================
2025-07-22 12:42:39,317 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-22 12:42:39,365 - INFO - [teaching_rules.py:88] - 🎓 Enhanced Teaching Rules Engine initialized with adaptive validation
2025-07-22 12:42:39,836 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-22 12:42:39,837 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-22 12:42:39,838 - INFO - [main.py:683] - ================================================================================
2025-07-22 12:42:39,838 - INFO - [main.py:684] - LESSON MANAGER BACKEND STARTING UP
2025-07-22 12:42:39,839 - INFO - [main.py:685] - ================================================================================
2025-07-22 12:42:39,839 - INFO - [main.py:686] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:42:39,840 - INFO - [main.py:687] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-22 12:42:39,840 - INFO - [main.py:688] - Log level: DEBUG
2025-07-22 12:42:39,841 - INFO - [main.py:689] - ================================================================================
2025-07-22 12:42:39,841 - INFO - [main.py:691] - Logging configuration complete with immediate console output
2025-07-22 12:42:39,841 - INFO - [main.py:692] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-22 12:42:39,844 - INFO - [main.py:1389] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-22 12:42:39,844 - INFO - [main.py:1396] - [OK] Enhanced state and auth managers initialized successfully
2025-07-22 12:42:39,848 - INFO - [main.py:1628] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-22 12:42:39,859 - INFO - [main.py:1657] - Phase transition fixes imported successfully
2025-07-22 12:42:39,864 - INFO - [main.py:5141] - Successfully imported utils functions
2025-07-22 12:42:39,866 - INFO - [main.py:5149] - Successfully imported extract_ai_state functions
2025-07-22 12:42:39,872 - INFO - [main.py:5599] - FLASK: Using unified Firebase initialization approach...
2025-07-22 12:42:39,875 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-22 12:42:39,876 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-22 12:42:39,877 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-22 12:42:39,877 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-22 12:42:39,935 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-22 12:42:39,936 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-22 12:42:40,531 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-22 12:42:41,085 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-22 12:42:41,965 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-22 12:42:41,966 - INFO - [main.py:5607] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-22 12:42:41,966 - INFO - [main.py:5697] - Gemini API will be initialized on first use (lazy loading).
2025-07-22 12:42:41,974 - INFO - [main.py:20256] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-22 12:42:41,975 - INFO - [main.py:20299] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-22 12:42:41,987 - INFO - [main.py:2116] - Successfully imported timetable_generator functions
2025-07-22 12:42:41,997 - INFO - [main.py:26699] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-22 12:42:42,089 - INFO - [main.py:26702] - Google Cloud Storage client initialized successfully.
2025-07-22 12:42:42,093 - INFO - [test_io_stability_fix.py:66] - Test message for normal logging
2025-07-22 12:42:42,094 - DEBUG - [test_io_stability_fix.py:67] - Debug message
2025-07-22 12:42:42,096 - WARNING - [test_io_stability_fix.py:68] - Warning message
2025-07-22 12:42:42,096 - ERROR - [test_io_stability_fix.py:69] - Error message
2025-07-22 12:42:42,098 - INFO - [test_io_stability_fix.py:90] - Test message with closed stdout
2025-07-22 12:42:42,098 - INFO - [test_io_stability_fix.py:115] - Test message with closed stderr
2025-07-22 12:42:42,105 - INFO - [test_io_stability_fix.py:144] - Test message with both streams closed
2025-07-22 12:42:42,106 - INFO - [test_io_stability_fix.py:168] - Test message with None streams
2025-07-22 12:42:42,106 - INFO - [test_io_stability_fix.py:202] - Test message with mock streams
2025-07-22 12:42:42,141 - INFO - [test_io_stability_fix.py:232] - Test message with Unicode: 🚀 ✅ → 📊
2025-07-22 12:42:42,150 - INFO - [test_io_stability_fix.py:267] - High volume test message 0
2025-07-22 12:42:42,151 - INFO - [test_io_stability_fix.py:267] - High volume test message 1
2025-07-22 12:42:42,151 - INFO - [test_io_stability_fix.py:267] - High volume test message 2
2025-07-22 12:42:42,152 - INFO - [test_io_stability_fix.py:267] - High volume test message 3
2025-07-22 12:42:42,152 - INFO - [test_io_stability_fix.py:267] - High volume test message 4
2025-07-22 12:42:42,153 - INFO - [test_io_stability_fix.py:267] - High volume test message 5
2025-07-22 12:42:42,154 - INFO - [test_io_stability_fix.py:267] - High volume test message 6
2025-07-22 12:42:42,154 - INFO - [test_io_stability_fix.py:267] - High volume test message 7
2025-07-22 12:42:42,155 - INFO - [test_io_stability_fix.py:267] - High volume test message 8
2025-07-22 12:42:42,156 - INFO - [test_io_stability_fix.py:267] - High volume test message 9
2025-07-22 12:42:42,156 - INFO - [test_io_stability_fix.py:267] - High volume test message 10
2025-07-22 12:42:42,157 - INFO - [test_io_stability_fix.py:267] - High volume test message 11
2025-07-22 12:42:42,158 - INFO - [test_io_stability_fix.py:267] - High volume test message 12
2025-07-22 12:42:42,160 - INFO - [test_io_stability_fix.py:267] - High volume test message 13
2025-07-22 12:42:42,167 - INFO - [test_io_stability_fix.py:267] - High volume test message 14
2025-07-22 12:42:42,169 - INFO - [test_io_stability_fix.py:267] - High volume test message 15
2025-07-22 12:42:42,173 - INFO - [test_io_stability_fix.py:267] - High volume test message 16
2025-07-22 12:42:42,175 - INFO - [test_io_stability_fix.py:267] - High volume test message 17
2025-07-22 12:42:42,177 - INFO - [test_io_stability_fix.py:267] - High volume test message 18
2025-07-22 12:42:42,180 - INFO - [test_io_stability_fix.py:267] - High volume test message 19
2025-07-22 12:42:42,181 - INFO - [test_io_stability_fix.py:267] - High volume test message 20
2025-07-22 12:42:42,182 - INFO - [test_io_stability_fix.py:267] - High volume test message 21
2025-07-22 12:42:42,183 - INFO - [test_io_stability_fix.py:267] - High volume test message 22
2025-07-22 12:42:42,183 - INFO - [test_io_stability_fix.py:267] - High volume test message 23
2025-07-22 12:42:42,185 - INFO - [test_io_stability_fix.py:267] - High volume test message 24
2025-07-22 12:42:42,186 - INFO - [test_io_stability_fix.py:267] - High volume test message 25
2025-07-22 12:42:42,187 - INFO - [test_io_stability_fix.py:267] - High volume test message 26
2025-07-22 12:42:42,188 - INFO - [test_io_stability_fix.py:267] - High volume test message 27
2025-07-22 12:42:42,188 - INFO - [test_io_stability_fix.py:267] - High volume test message 28
2025-07-22 12:42:42,189 - INFO - [test_io_stability_fix.py:267] - High volume test message 29
2025-07-22 12:42:42,190 - INFO - [test_io_stability_fix.py:267] - High volume test message 30
2025-07-22 12:42:42,191 - INFO - [test_io_stability_fix.py:267] - High volume test message 31
2025-07-22 12:42:42,191 - INFO - [test_io_stability_fix.py:267] - High volume test message 32
2025-07-22 12:42:42,192 - INFO - [test_io_stability_fix.py:267] - High volume test message 33
2025-07-22 12:42:42,194 - INFO - [test_io_stability_fix.py:267] - High volume test message 34
2025-07-22 12:42:42,196 - INFO - [test_io_stability_fix.py:267] - High volume test message 35
2025-07-22 12:42:42,198 - INFO - [test_io_stability_fix.py:267] - High volume test message 36
2025-07-22 12:42:42,199 - INFO - [test_io_stability_fix.py:267] - High volume test message 37
2025-07-22 12:42:42,200 - INFO - [test_io_stability_fix.py:267] - High volume test message 38
2025-07-22 12:42:42,201 - INFO - [test_io_stability_fix.py:267] - High volume test message 39
2025-07-22 12:42:42,202 - INFO - [test_io_stability_fix.py:267] - High volume test message 40
2025-07-22 12:42:42,203 - INFO - [test_io_stability_fix.py:267] - High volume test message 41
2025-07-22 12:42:42,203 - INFO - [test_io_stability_fix.py:267] - High volume test message 42
2025-07-22 12:42:42,204 - INFO - [test_io_stability_fix.py:267] - High volume test message 43
2025-07-22 12:42:42,204 - INFO - [test_io_stability_fix.py:267] - High volume test message 44
2025-07-22 12:42:42,205 - INFO - [test_io_stability_fix.py:267] - High volume test message 45
2025-07-22 12:42:42,205 - INFO - [test_io_stability_fix.py:267] - High volume test message 46
2025-07-22 12:42:42,206 - INFO - [test_io_stability_fix.py:267] - High volume test message 47
2025-07-22 12:42:42,206 - INFO - [test_io_stability_fix.py:267] - High volume test message 48
2025-07-22 12:42:42,207 - INFO - [test_io_stability_fix.py:267] - High volume test message 49
2025-07-22 12:42:42,208 - INFO - [test_io_stability_fix.py:267] - High volume test message 50
2025-07-22 12:42:42,208 - INFO - [test_io_stability_fix.py:267] - High volume test message 51
2025-07-22 12:42:42,209 - INFO - [test_io_stability_fix.py:267] - High volume test message 52
2025-07-22 12:42:42,210 - INFO - [test_io_stability_fix.py:267] - High volume test message 53
2025-07-22 12:42:42,210 - INFO - [test_io_stability_fix.py:267] - High volume test message 54
2025-07-22 12:42:42,212 - INFO - [test_io_stability_fix.py:267] - High volume test message 55
2025-07-22 12:42:42,213 - INFO - [test_io_stability_fix.py:267] - High volume test message 56
2025-07-22 12:42:42,213 - INFO - [test_io_stability_fix.py:267] - High volume test message 57
2025-07-22 12:42:42,214 - INFO - [test_io_stability_fix.py:267] - High volume test message 58
2025-07-22 12:42:42,215 - INFO - [test_io_stability_fix.py:267] - High volume test message 59
2025-07-22 12:42:42,215 - INFO - [test_io_stability_fix.py:267] - High volume test message 60
2025-07-22 12:42:42,216 - INFO - [test_io_stability_fix.py:267] - High volume test message 61
2025-07-22 12:42:42,216 - INFO - [test_io_stability_fix.py:267] - High volume test message 62
2025-07-22 12:42:42,217 - INFO - [test_io_stability_fix.py:267] - High volume test message 63
2025-07-22 12:42:42,217 - INFO - [test_io_stability_fix.py:267] - High volume test message 64
2025-07-22 12:42:42,217 - INFO - [test_io_stability_fix.py:267] - High volume test message 65
2025-07-22 12:42:42,218 - INFO - [test_io_stability_fix.py:267] - High volume test message 66
2025-07-22 12:42:42,218 - INFO - [test_io_stability_fix.py:267] - High volume test message 67
2025-07-22 12:42:42,219 - INFO - [test_io_stability_fix.py:267] - High volume test message 68
2025-07-22 12:42:42,219 - INFO - [test_io_stability_fix.py:267] - High volume test message 69
2025-07-22 12:42:42,220 - INFO - [test_io_stability_fix.py:267] - High volume test message 70
2025-07-22 12:42:42,220 - INFO - [test_io_stability_fix.py:267] - High volume test message 71
2025-07-22 12:42:42,221 - INFO - [test_io_stability_fix.py:267] - High volume test message 72
2025-07-22 12:42:42,221 - INFO - [test_io_stability_fix.py:267] - High volume test message 73
2025-07-22 12:42:42,222 - INFO - [test_io_stability_fix.py:267] - High volume test message 74
2025-07-22 12:42:42,222 - INFO - [test_io_stability_fix.py:267] - High volume test message 75
2025-07-22 12:42:42,222 - INFO - [test_io_stability_fix.py:267] - High volume test message 76
2025-07-22 12:42:42,223 - INFO - [test_io_stability_fix.py:267] - High volume test message 77
2025-07-22 12:42:42,223 - INFO - [test_io_stability_fix.py:267] - High volume test message 78
2025-07-22 12:42:42,224 - INFO - [test_io_stability_fix.py:267] - High volume test message 79
2025-07-22 12:42:42,224 - INFO - [test_io_stability_fix.py:267] - High volume test message 80
2025-07-22 12:42:42,225 - INFO - [test_io_stability_fix.py:267] - High volume test message 81
2025-07-22 12:42:42,225 - INFO - [test_io_stability_fix.py:267] - High volume test message 82
2025-07-22 12:42:42,226 - INFO - [test_io_stability_fix.py:267] - High volume test message 83
2025-07-22 12:42:42,227 - INFO - [test_io_stability_fix.py:267] - High volume test message 84
2025-07-22 12:42:42,229 - INFO - [test_io_stability_fix.py:267] - High volume test message 85
2025-07-22 12:42:42,230 - INFO - [test_io_stability_fix.py:267] - High volume test message 86
2025-07-22 12:42:42,231 - INFO - [test_io_stability_fix.py:267] - High volume test message 87
2025-07-22 12:42:42,232 - INFO - [test_io_stability_fix.py:267] - High volume test message 88
2025-07-22 12:42:42,233 - INFO - [test_io_stability_fix.py:267] - High volume test message 89
2025-07-22 12:42:42,233 - INFO - [test_io_stability_fix.py:267] - High volume test message 90
2025-07-22 12:42:42,234 - INFO - [test_io_stability_fix.py:267] - High volume test message 91
2025-07-22 12:42:42,234 - INFO - [test_io_stability_fix.py:267] - High volume test message 92
2025-07-22 12:42:42,235 - INFO - [test_io_stability_fix.py:267] - High volume test message 93
2025-07-22 12:42:42,236 - INFO - [test_io_stability_fix.py:267] - High volume test message 94
2025-07-22 12:42:42,237 - INFO - [test_io_stability_fix.py:267] - High volume test message 95
2025-07-22 12:42:42,237 - INFO - [test_io_stability_fix.py:267] - High volume test message 96
2025-07-22 12:42:42,238 - INFO - [test_io_stability_fix.py:267] - High volume test message 97
2025-07-22 12:42:42,238 - INFO - [test_io_stability_fix.py:267] - High volume test message 98
2025-07-22 12:42:42,239 - INFO - [test_io_stability_fix.py:267] - High volume test message 99
2025-07-22 12:42:42,241 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 0
2025-07-22 12:42:42,241 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 0
2025-07-22 12:42:42,242 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 0
2025-07-22 12:42:42,244 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 0
2025-07-22 12:42:42,247 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 0
2025-07-22 12:42:42,253 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 1
2025-07-22 12:42:42,253 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 1
2025-07-22 12:42:42,257 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 1
2025-07-22 12:42:42,258 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 1
2025-07-22 12:42:42,259 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 1
2025-07-22 12:42:42,264 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 2
2025-07-22 12:42:42,264 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 2
2025-07-22 12:42:42,268 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 2
2025-07-22 12:42:42,269 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 2
2025-07-22 12:42:42,270 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 2
2025-07-22 12:42:42,275 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 3
2025-07-22 12:42:42,275 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 3
2025-07-22 12:42:42,279 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 3
2025-07-22 12:42:42,280 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 3
2025-07-22 12:42:42,281 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 3
2025-07-22 12:42:42,286 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 4
2025-07-22 12:42:42,287 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 4
2025-07-22 12:42:42,290 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 4
2025-07-22 12:42:42,291 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 4
2025-07-22 12:42:42,292 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 4
2025-07-22 12:42:42,297 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 5
2025-07-22 12:42:42,298 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 5
2025-07-22 12:42:42,301 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 5
2025-07-22 12:42:42,301 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 5
2025-07-22 12:42:42,303 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 5
2025-07-22 12:42:42,308 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 6
2025-07-22 12:42:42,309 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 6
2025-07-22 12:42:42,312 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 6
2025-07-22 12:42:42,313 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 6
2025-07-22 12:42:42,314 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 6
2025-07-22 12:42:42,321 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 7
2025-07-22 12:42:42,323 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 7
2025-07-22 12:42:42,324 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 7
2025-07-22 12:42:42,324 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 7
2025-07-22 12:42:42,325 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 7
2025-07-22 12:42:42,332 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 8
2025-07-22 12:42:42,334 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 8
2025-07-22 12:42:42,335 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 8
2025-07-22 12:42:42,335 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 8
2025-07-22 12:42:42,336 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 8
2025-07-22 12:42:42,343 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 9
2025-07-22 12:42:42,345 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 9
2025-07-22 12:42:42,346 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 9
2025-07-22 12:42:42,346 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 9
2025-07-22 12:42:42,347 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 9
2025-07-22 12:44:31,828 - INFO - [main.py:71] - ============================================================
2025-07-22 12:44:31,831 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-22 12:44:31,831 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:44:31,832 - INFO - [main.py:74] - Platform: win32
2025-07-22 12:44:31,832 - INFO - [main.py:75] - ============================================================
2025-07-22 12:44:33,701 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-22 12:44:33,727 - INFO - [teaching_rules.py:88] - 🎓 Enhanced Teaching Rules Engine initialized with adaptive validation
2025-07-22 12:44:33,887 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-22 12:44:33,887 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-22 12:44:33,888 - INFO - [main.py:706] - ================================================================================
2025-07-22 12:44:33,889 - INFO - [main.py:707] - LESSON MANAGER BACKEND STARTING UP
2025-07-22 12:44:33,889 - INFO - [main.py:708] - ================================================================================
2025-07-22 12:44:33,889 - INFO - [main.py:709] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:44:33,889 - INFO - [main.py:710] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-22 12:44:33,890 - INFO - [main.py:711] - Log level: DEBUG
2025-07-22 12:44:33,890 - INFO - [main.py:712] - ================================================================================
2025-07-22 12:44:33,890 - INFO - [main.py:714] - Logging configuration complete with immediate console output
2025-07-22 12:44:33,890 - INFO - [main.py:715] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-22 12:44:33,892 - INFO - [main.py:1412] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-22 12:44:33,892 - INFO - [main.py:1419] - [OK] Enhanced state and auth managers initialized successfully
2025-07-22 12:44:33,893 - INFO - [main.py:1651] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-22 12:44:33,900 - INFO - [main.py:1680] - Phase transition fixes imported successfully
2025-07-22 12:44:33,903 - INFO - [main.py:5164] - Successfully imported utils functions
2025-07-22 12:44:33,904 - INFO - [main.py:5172] - Successfully imported extract_ai_state functions
2025-07-22 12:44:33,909 - INFO - [main.py:5622] - FLASK: Using unified Firebase initialization approach...
2025-07-22 12:44:33,911 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-22 12:44:33,912 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-22 12:44:33,912 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-22 12:44:33,913 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-22 12:44:34,005 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-22 12:44:34,006 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-22 12:44:34,506 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-22 12:44:34,950 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-22 12:44:35,331 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-22 12:44:35,331 - INFO - [main.py:5630] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-22 12:44:35,332 - INFO - [main.py:5720] - Gemini API will be initialized on first use (lazy loading).
2025-07-22 12:44:35,341 - INFO - [main.py:20279] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-22 12:44:35,343 - INFO - [main.py:20322] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-22 12:44:35,355 - INFO - [main.py:2139] - Successfully imported timetable_generator functions
2025-07-22 12:44:35,367 - INFO - [main.py:26722] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-22 12:44:35,472 - INFO - [main.py:26725] - Google Cloud Storage client initialized successfully.
2025-07-22 12:44:35,474 - INFO - [test_io_stability_fix.py:66] - Test message for normal logging
2025-07-22 12:44:35,474 - DEBUG - [test_io_stability_fix.py:67] - Debug message
2025-07-22 12:44:35,475 - WARNING - [test_io_stability_fix.py:68] - Warning message
2025-07-22 12:44:35,475 - ERROR - [test_io_stability_fix.py:69] - Error message
2025-07-22 12:44:35,476 - INFO - [test_io_stability_fix.py:90] - Test message with closed stdout
2025-07-22 12:44:35,476 - INFO - [test_io_stability_fix.py:115] - Test message with closed stderr
2025-07-22 12:44:35,477 - INFO - [test_io_stability_fix.py:144] - Test message with both streams closed
2025-07-22 12:44:35,477 - INFO - [test_io_stability_fix.py:168] - Test message with None streams
2025-07-22 12:44:35,478 - INFO - [test_io_stability_fix.py:202] - Test message with mock streams
2025-07-22 12:44:35,480 - INFO - [test_io_stability_fix.py:232] - Test message with Unicode: 🚀 ✅ → 📊
2025-07-22 12:44:35,481 - INFO - [test_io_stability_fix.py:267] - High volume test message 0
2025-07-22 12:44:35,482 - INFO - [test_io_stability_fix.py:267] - High volume test message 1
2025-07-22 12:44:35,483 - INFO - [test_io_stability_fix.py:267] - High volume test message 2
2025-07-22 12:44:35,483 - INFO - [test_io_stability_fix.py:267] - High volume test message 3
2025-07-22 12:44:35,484 - INFO - [test_io_stability_fix.py:267] - High volume test message 4
2025-07-22 12:44:35,484 - INFO - [test_io_stability_fix.py:267] - High volume test message 5
2025-07-22 12:44:35,485 - INFO - [test_io_stability_fix.py:267] - High volume test message 6
2025-07-22 12:44:35,486 - INFO - [test_io_stability_fix.py:267] - High volume test message 7
2025-07-22 12:44:35,486 - INFO - [test_io_stability_fix.py:267] - High volume test message 8
2025-07-22 12:44:35,487 - INFO - [test_io_stability_fix.py:267] - High volume test message 9
2025-07-22 12:44:35,488 - INFO - [test_io_stability_fix.py:267] - High volume test message 10
2025-07-22 12:44:35,488 - INFO - [test_io_stability_fix.py:267] - High volume test message 11
2025-07-22 12:44:35,489 - INFO - [test_io_stability_fix.py:267] - High volume test message 12
2025-07-22 12:44:35,489 - INFO - [test_io_stability_fix.py:267] - High volume test message 13
2025-07-22 12:44:35,490 - INFO - [test_io_stability_fix.py:267] - High volume test message 14
2025-07-22 12:44:35,490 - INFO - [test_io_stability_fix.py:267] - High volume test message 15
2025-07-22 12:44:35,491 - INFO - [test_io_stability_fix.py:267] - High volume test message 16
2025-07-22 12:44:35,491 - INFO - [test_io_stability_fix.py:267] - High volume test message 17
2025-07-22 12:44:35,492 - INFO - [test_io_stability_fix.py:267] - High volume test message 18
2025-07-22 12:44:35,492 - INFO - [test_io_stability_fix.py:267] - High volume test message 19
2025-07-22 12:44:35,492 - INFO - [test_io_stability_fix.py:267] - High volume test message 20
2025-07-22 12:44:35,493 - INFO - [test_io_stability_fix.py:267] - High volume test message 21
2025-07-22 12:44:35,493 - INFO - [test_io_stability_fix.py:267] - High volume test message 22
2025-07-22 12:44:35,494 - INFO - [test_io_stability_fix.py:267] - High volume test message 23
2025-07-22 12:44:35,495 - INFO - [test_io_stability_fix.py:267] - High volume test message 24
2025-07-22 12:44:35,497 - INFO - [test_io_stability_fix.py:267] - High volume test message 25
2025-07-22 12:44:35,498 - INFO - [test_io_stability_fix.py:267] - High volume test message 26
2025-07-22 12:44:35,498 - INFO - [test_io_stability_fix.py:267] - High volume test message 27
2025-07-22 12:44:35,499 - INFO - [test_io_stability_fix.py:267] - High volume test message 28
2025-07-22 12:44:35,500 - INFO - [test_io_stability_fix.py:267] - High volume test message 29
2025-07-22 12:44:35,500 - INFO - [test_io_stability_fix.py:267] - High volume test message 30
2025-07-22 12:44:35,501 - INFO - [test_io_stability_fix.py:267] - High volume test message 31
2025-07-22 12:44:35,501 - INFO - [test_io_stability_fix.py:267] - High volume test message 32
2025-07-22 12:44:35,502 - INFO - [test_io_stability_fix.py:267] - High volume test message 33
2025-07-22 12:44:35,502 - INFO - [test_io_stability_fix.py:267] - High volume test message 34
2025-07-22 12:44:35,503 - INFO - [test_io_stability_fix.py:267] - High volume test message 35
2025-07-22 12:44:35,503 - INFO - [test_io_stability_fix.py:267] - High volume test message 36
2025-07-22 12:44:35,504 - INFO - [test_io_stability_fix.py:267] - High volume test message 37
2025-07-22 12:44:35,504 - INFO - [test_io_stability_fix.py:267] - High volume test message 38
2025-07-22 12:44:35,505 - INFO - [test_io_stability_fix.py:267] - High volume test message 39
2025-07-22 12:44:35,505 - INFO - [test_io_stability_fix.py:267] - High volume test message 40
2025-07-22 12:44:35,506 - INFO - [test_io_stability_fix.py:267] - High volume test message 41
2025-07-22 12:44:35,507 - INFO - [test_io_stability_fix.py:267] - High volume test message 42
2025-07-22 12:44:35,507 - INFO - [test_io_stability_fix.py:267] - High volume test message 43
2025-07-22 12:44:35,508 - INFO - [test_io_stability_fix.py:267] - High volume test message 44
2025-07-22 12:44:35,508 - INFO - [test_io_stability_fix.py:267] - High volume test message 45
2025-07-22 12:44:35,509 - INFO - [test_io_stability_fix.py:267] - High volume test message 46
2025-07-22 12:44:35,509 - INFO - [test_io_stability_fix.py:267] - High volume test message 47
2025-07-22 12:44:35,510 - INFO - [test_io_stability_fix.py:267] - High volume test message 48
2025-07-22 12:44:35,510 - INFO - [test_io_stability_fix.py:267] - High volume test message 49
2025-07-22 12:44:35,512 - INFO - [test_io_stability_fix.py:267] - High volume test message 50
2025-07-22 12:44:35,514 - INFO - [test_io_stability_fix.py:267] - High volume test message 51
2025-07-22 12:44:35,515 - INFO - [test_io_stability_fix.py:267] - High volume test message 52
2025-07-22 12:44:35,516 - INFO - [test_io_stability_fix.py:267] - High volume test message 53
2025-07-22 12:44:35,516 - INFO - [test_io_stability_fix.py:267] - High volume test message 54
2025-07-22 12:44:35,517 - INFO - [test_io_stability_fix.py:267] - High volume test message 55
2025-07-22 12:44:35,517 - INFO - [test_io_stability_fix.py:267] - High volume test message 56
2025-07-22 12:44:35,518 - INFO - [test_io_stability_fix.py:267] - High volume test message 57
2025-07-22 12:44:35,518 - INFO - [test_io_stability_fix.py:267] - High volume test message 58
2025-07-22 12:44:35,519 - INFO - [test_io_stability_fix.py:267] - High volume test message 59
2025-07-22 12:44:35,519 - INFO - [test_io_stability_fix.py:267] - High volume test message 60
2025-07-22 12:44:35,519 - INFO - [test_io_stability_fix.py:267] - High volume test message 61
2025-07-22 12:44:35,520 - INFO - [test_io_stability_fix.py:267] - High volume test message 62
2025-07-22 12:44:35,520 - INFO - [test_io_stability_fix.py:267] - High volume test message 63
2025-07-22 12:44:35,521 - INFO - [test_io_stability_fix.py:267] - High volume test message 64
2025-07-22 12:44:35,521 - INFO - [test_io_stability_fix.py:267] - High volume test message 65
2025-07-22 12:44:35,521 - INFO - [test_io_stability_fix.py:267] - High volume test message 66
2025-07-22 12:44:35,522 - INFO - [test_io_stability_fix.py:267] - High volume test message 67
2025-07-22 12:44:35,522 - INFO - [test_io_stability_fix.py:267] - High volume test message 68
2025-07-22 12:44:35,523 - INFO - [test_io_stability_fix.py:267] - High volume test message 69
2025-07-22 12:44:35,523 - INFO - [test_io_stability_fix.py:267] - High volume test message 70
2025-07-22 12:44:35,524 - INFO - [test_io_stability_fix.py:267] - High volume test message 71
2025-07-22 12:44:35,524 - INFO - [test_io_stability_fix.py:267] - High volume test message 72
2025-07-22 12:44:35,525 - INFO - [test_io_stability_fix.py:267] - High volume test message 73
2025-07-22 12:44:35,525 - INFO - [test_io_stability_fix.py:267] - High volume test message 74
2025-07-22 12:44:35,526 - INFO - [test_io_stability_fix.py:267] - High volume test message 75
2025-07-22 12:44:35,526 - INFO - [test_io_stability_fix.py:267] - High volume test message 76
2025-07-22 12:44:35,526 - INFO - [test_io_stability_fix.py:267] - High volume test message 77
2025-07-22 12:44:35,527 - INFO - [test_io_stability_fix.py:267] - High volume test message 78
2025-07-22 12:44:35,528 - INFO - [test_io_stability_fix.py:267] - High volume test message 79
2025-07-22 12:44:35,529 - INFO - [test_io_stability_fix.py:267] - High volume test message 80
2025-07-22 12:44:35,530 - INFO - [test_io_stability_fix.py:267] - High volume test message 81
2025-07-22 12:44:35,531 - INFO - [test_io_stability_fix.py:267] - High volume test message 82
2025-07-22 12:44:35,532 - INFO - [test_io_stability_fix.py:267] - High volume test message 83
2025-07-22 12:44:35,532 - INFO - [test_io_stability_fix.py:267] - High volume test message 84
2025-07-22 12:44:35,533 - INFO - [test_io_stability_fix.py:267] - High volume test message 85
2025-07-22 12:44:35,533 - INFO - [test_io_stability_fix.py:267] - High volume test message 86
2025-07-22 12:44:35,534 - INFO - [test_io_stability_fix.py:267] - High volume test message 87
2025-07-22 12:44:35,534 - INFO - [test_io_stability_fix.py:267] - High volume test message 88
2025-07-22 12:44:35,534 - INFO - [test_io_stability_fix.py:267] - High volume test message 89
2025-07-22 12:44:35,535 - INFO - [test_io_stability_fix.py:267] - High volume test message 90
2025-07-22 12:44:35,535 - INFO - [test_io_stability_fix.py:267] - High volume test message 91
2025-07-22 12:44:35,536 - INFO - [test_io_stability_fix.py:267] - High volume test message 92
2025-07-22 12:44:35,536 - INFO - [test_io_stability_fix.py:267] - High volume test message 93
2025-07-22 12:44:35,537 - INFO - [test_io_stability_fix.py:267] - High volume test message 94
2025-07-22 12:44:35,537 - INFO - [test_io_stability_fix.py:267] - High volume test message 95
2025-07-22 12:44:35,538 - INFO - [test_io_stability_fix.py:267] - High volume test message 96
2025-07-22 12:44:35,538 - INFO - [test_io_stability_fix.py:267] - High volume test message 97
2025-07-22 12:44:35,539 - INFO - [test_io_stability_fix.py:267] - High volume test message 98
2025-07-22 12:44:35,539 - INFO - [test_io_stability_fix.py:267] - High volume test message 99
2025-07-22 12:44:35,540 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 0
2025-07-22 12:44:35,540 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 0
2025-07-22 12:44:35,540 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 0
2025-07-22 12:44:35,541 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 0
2025-07-22 12:44:35,541 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 0
2025-07-22 12:44:35,551 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 1
2025-07-22 12:44:35,552 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 1
2025-07-22 12:44:35,552 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 1
2025-07-22 12:44:35,552 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 1
2025-07-22 12:44:35,553 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 1
2025-07-22 12:44:35,562 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 2
2025-07-22 12:44:35,563 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 2
2025-07-22 12:44:35,563 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 2
2025-07-22 12:44:35,563 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 2
2025-07-22 12:44:35,564 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 2
2025-07-22 12:44:35,574 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 3
2025-07-22 12:44:35,575 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 3
2025-07-22 12:44:35,576 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 3
2025-07-22 12:44:35,576 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 3
2025-07-22 12:44:35,577 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 3
2025-07-22 12:44:35,585 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 4
2025-07-22 12:44:35,586 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 4
2025-07-22 12:44:35,587 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 4
2025-07-22 12:44:35,587 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 4
2025-07-22 12:44:35,588 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 4
2025-07-22 12:44:35,596 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 5
2025-07-22 12:44:35,597 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 5
2025-07-22 12:44:35,598 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 5
2025-07-22 12:44:35,598 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 5
2025-07-22 12:44:35,598 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 5
2025-07-22 12:44:35,608 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 6
2025-07-22 12:44:35,609 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 6
2025-07-22 12:44:35,609 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 6
2025-07-22 12:44:35,609 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 6
2025-07-22 12:44:35,610 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 6
2025-07-22 12:44:35,619 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 7
2025-07-22 12:44:35,620 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 7
2025-07-22 12:44:35,620 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 7
2025-07-22 12:44:35,620 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 7
2025-07-22 12:44:35,621 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 7
2025-07-22 12:44:35,630 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 8
2025-07-22 12:44:35,631 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 8
2025-07-22 12:44:35,631 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 8
2025-07-22 12:44:35,631 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 8
2025-07-22 12:44:35,631 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 8
2025-07-22 12:44:35,642 - INFO - [test_io_stability_fix.py:284] - Worker 1 - Message 9
2025-07-22 12:44:35,642 - INFO - [test_io_stability_fix.py:284] - Worker 2 - Message 9
2025-07-22 12:44:35,643 - INFO - [test_io_stability_fix.py:284] - Worker 0 - Message 9
2025-07-22 12:44:35,644 - INFO - [test_io_stability_fix.py:284] - Worker 4 - Message 9
2025-07-22 12:44:35,644 - INFO - [test_io_stability_fix.py:284] - Worker 3 - Message 9
2025-07-22 12:47:27,141 - INFO - [main.py:71] - ============================================================
2025-07-22 12:47:27,142 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-22 12:47:27,142 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:47:27,143 - INFO - [main.py:74] - Platform: win32
2025-07-22 12:47:27,143 - INFO - [main.py:75] - ============================================================
2025-07-22 12:47:29,560 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-22 12:47:29,603 - INFO - [teaching_rules.py:88] - 🎓 Enhanced Teaching Rules Engine initialized with adaptive validation
2025-07-22 12:47:29,795 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-22 12:47:29,797 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-22 12:47:29,798 - INFO - [main.py:729] - ================================================================================
2025-07-22 12:47:29,798 - INFO - [main.py:730] - LESSON MANAGER BACKEND STARTING UP
2025-07-22 12:47:29,799 - INFO - [main.py:731] - ================================================================================
2025-07-22 12:47:29,799 - INFO - [main.py:732] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:47:29,799 - INFO - [main.py:733] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-22 12:47:29,800 - INFO - [main.py:734] - Log level: DEBUG
2025-07-22 12:47:29,800 - INFO - [main.py:735] - ================================================================================
2025-07-22 12:47:29,800 - INFO - [main.py:737] - Logging configuration complete with immediate console output
2025-07-22 12:47:29,800 - INFO - [main.py:738] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-22 12:47:29,803 - INFO - [main.py:1435] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-22 12:47:29,803 - INFO - [main.py:1442] - [OK] Enhanced state and auth managers initialized successfully
2025-07-22 12:47:29,805 - INFO - [main.py:1674] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-22 12:47:29,813 - INFO - [main.py:1703] - Phase transition fixes imported successfully
2025-07-22 12:47:29,817 - INFO - [main.py:5187] - Successfully imported utils functions
2025-07-22 12:47:29,820 - INFO - [main.py:5195] - Successfully imported extract_ai_state functions
2025-07-22 12:47:29,826 - INFO - [main.py:5645] - FLASK: Using unified Firebase initialization approach...
2025-07-22 12:47:29,829 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-22 12:47:29,829 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-22 12:47:29,830 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-22 12:47:29,830 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-22 12:47:29,887 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-22 12:47:29,888 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-22 12:47:30,438 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-22 12:47:30,992 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-22 12:47:31,805 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-22 12:47:31,806 - INFO - [main.py:5653] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-22 12:47:31,807 - INFO - [main.py:5743] - Gemini API will be initialized on first use (lazy loading).
2025-07-22 12:47:31,827 - INFO - [main.py:20302] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-22 12:47:31,828 - INFO - [main.py:20345] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-22 12:47:31,838 - INFO - [main.py:2162] - Successfully imported timetable_generator functions
2025-07-22 12:47:31,844 - INFO - [main.py:26745] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-22 12:47:31,923 - INFO - [main.py:26748] - Google Cloud Storage client initialized successfully.
2025-07-22 12:47:31,927 - INFO - [test_io_stability_fix.py:66] - Test message for normal logging
2025-07-22 12:47:31,927 - DEBUG - [test_io_stability_fix.py:67] - Debug message
2025-07-22 12:47:31,928 - WARNING - [test_io_stability_fix.py:68] - Warning message
2025-07-22 12:47:31,929 - ERROR - [test_io_stability_fix.py:69] - Error message
2025-07-22 12:47:31,931 - INFO - [test_io_stability_fix.py:121] - Test message with closed stderr
2025-07-22 12:47:31,932 - INFO - [test_io_stability_fix.py:150] - Test message with both streams closed
2025-07-22 12:47:31,933 - INFO - [test_io_stability_fix.py:174] - Test message with None streams
2025-07-22 12:47:31,933 - INFO - [test_io_stability_fix.py:208] - Test message with mock streams
2025-07-22 12:47:31,934 - INFO - [test_io_stability_fix.py:238] - Test message with Unicode: 🚀 ✅ → 📊
2025-07-22 12:47:31,935 - INFO - [test_io_stability_fix.py:273] - High volume test message 0
2025-07-22 12:47:31,936 - INFO - [test_io_stability_fix.py:273] - High volume test message 1
2025-07-22 12:47:31,937 - INFO - [test_io_stability_fix.py:273] - High volume test message 2
2025-07-22 12:47:31,937 - INFO - [test_io_stability_fix.py:273] - High volume test message 3
2025-07-22 12:47:31,938 - INFO - [test_io_stability_fix.py:273] - High volume test message 4
2025-07-22 12:47:31,938 - INFO - [test_io_stability_fix.py:273] - High volume test message 5
2025-07-22 12:47:31,939 - INFO - [test_io_stability_fix.py:273] - High volume test message 6
2025-07-22 12:47:31,939 - INFO - [test_io_stability_fix.py:273] - High volume test message 7
2025-07-22 12:47:31,940 - INFO - [test_io_stability_fix.py:273] - High volume test message 8
2025-07-22 12:47:31,940 - INFO - [test_io_stability_fix.py:273] - High volume test message 9
2025-07-22 12:47:31,941 - INFO - [test_io_stability_fix.py:273] - High volume test message 10
2025-07-22 12:47:31,941 - INFO - [test_io_stability_fix.py:273] - High volume test message 11
2025-07-22 12:47:31,942 - INFO - [test_io_stability_fix.py:273] - High volume test message 12
2025-07-22 12:47:31,943 - INFO - [test_io_stability_fix.py:273] - High volume test message 13
2025-07-22 12:47:31,944 - INFO - [test_io_stability_fix.py:273] - High volume test message 14
2025-07-22 12:47:31,944 - INFO - [test_io_stability_fix.py:273] - High volume test message 15
2025-07-22 12:47:31,945 - INFO - [test_io_stability_fix.py:273] - High volume test message 16
2025-07-22 12:47:31,946 - INFO - [test_io_stability_fix.py:273] - High volume test message 17
2025-07-22 12:47:31,947 - INFO - [test_io_stability_fix.py:273] - High volume test message 18
2025-07-22 12:47:31,947 - INFO - [test_io_stability_fix.py:273] - High volume test message 19
2025-07-22 12:47:31,948 - INFO - [test_io_stability_fix.py:273] - High volume test message 20
2025-07-22 12:47:31,948 - INFO - [test_io_stability_fix.py:273] - High volume test message 21
2025-07-22 12:47:31,949 - INFO - [test_io_stability_fix.py:273] - High volume test message 22
2025-07-22 12:47:31,949 - INFO - [test_io_stability_fix.py:273] - High volume test message 23
2025-07-22 12:47:31,950 - INFO - [test_io_stability_fix.py:273] - High volume test message 24
2025-07-22 12:47:31,951 - INFO - [test_io_stability_fix.py:273] - High volume test message 25
2025-07-22 12:47:31,952 - INFO - [test_io_stability_fix.py:273] - High volume test message 26
2025-07-22 12:47:31,953 - INFO - [test_io_stability_fix.py:273] - High volume test message 27
2025-07-22 12:47:31,954 - INFO - [test_io_stability_fix.py:273] - High volume test message 28
2025-07-22 12:47:31,955 - INFO - [test_io_stability_fix.py:273] - High volume test message 29
2025-07-22 12:47:31,956 - INFO - [test_io_stability_fix.py:273] - High volume test message 30
2025-07-22 12:47:31,956 - INFO - [test_io_stability_fix.py:273] - High volume test message 31
2025-07-22 12:47:31,957 - INFO - [test_io_stability_fix.py:273] - High volume test message 32
2025-07-22 12:47:31,958 - INFO - [test_io_stability_fix.py:273] - High volume test message 33
2025-07-22 12:47:31,958 - INFO - [test_io_stability_fix.py:273] - High volume test message 34
2025-07-22 12:47:31,959 - INFO - [test_io_stability_fix.py:273] - High volume test message 35
2025-07-22 12:47:31,960 - INFO - [test_io_stability_fix.py:273] - High volume test message 36
2025-07-22 12:47:31,960 - INFO - [test_io_stability_fix.py:273] - High volume test message 37
2025-07-22 12:47:31,961 - INFO - [test_io_stability_fix.py:273] - High volume test message 38
2025-07-22 12:47:31,962 - INFO - [test_io_stability_fix.py:273] - High volume test message 39
2025-07-22 12:47:31,963 - INFO - [test_io_stability_fix.py:273] - High volume test message 40
2025-07-22 12:47:31,963 - INFO - [test_io_stability_fix.py:273] - High volume test message 41
2025-07-22 12:47:31,964 - INFO - [test_io_stability_fix.py:273] - High volume test message 42
2025-07-22 12:47:31,964 - INFO - [test_io_stability_fix.py:273] - High volume test message 43
2025-07-22 12:47:31,965 - INFO - [test_io_stability_fix.py:273] - High volume test message 44
2025-07-22 12:47:31,967 - INFO - [test_io_stability_fix.py:273] - High volume test message 45
2025-07-22 12:47:31,968 - INFO - [test_io_stability_fix.py:273] - High volume test message 46
2025-07-22 12:47:31,969 - INFO - [test_io_stability_fix.py:273] - High volume test message 47
2025-07-22 12:47:31,970 - INFO - [test_io_stability_fix.py:273] - High volume test message 48
2025-07-22 12:47:31,971 - INFO - [test_io_stability_fix.py:273] - High volume test message 49
2025-07-22 12:47:31,972 - INFO - [test_io_stability_fix.py:273] - High volume test message 50
2025-07-22 12:47:31,973 - INFO - [test_io_stability_fix.py:273] - High volume test message 51
2025-07-22 12:47:31,973 - INFO - [test_io_stability_fix.py:273] - High volume test message 52
2025-07-22 12:47:31,974 - INFO - [test_io_stability_fix.py:273] - High volume test message 53
2025-07-22 12:47:31,975 - INFO - [test_io_stability_fix.py:273] - High volume test message 54
2025-07-22 12:47:31,975 - INFO - [test_io_stability_fix.py:273] - High volume test message 55
2025-07-22 12:47:31,976 - INFO - [test_io_stability_fix.py:273] - High volume test message 56
2025-07-22 12:47:31,976 - INFO - [test_io_stability_fix.py:273] - High volume test message 57
2025-07-22 12:47:31,976 - INFO - [test_io_stability_fix.py:273] - High volume test message 58
2025-07-22 12:47:31,977 - INFO - [test_io_stability_fix.py:273] - High volume test message 59
2025-07-22 12:47:31,977 - INFO - [test_io_stability_fix.py:273] - High volume test message 60
2025-07-22 12:47:31,978 - INFO - [test_io_stability_fix.py:273] - High volume test message 61
2025-07-22 12:47:31,978 - INFO - [test_io_stability_fix.py:273] - High volume test message 62
2025-07-22 12:47:31,978 - INFO - [test_io_stability_fix.py:273] - High volume test message 63
2025-07-22 12:47:31,979 - INFO - [test_io_stability_fix.py:273] - High volume test message 64
2025-07-22 12:47:31,980 - INFO - [test_io_stability_fix.py:273] - High volume test message 65
2025-07-22 12:47:31,980 - INFO - [test_io_stability_fix.py:273] - High volume test message 66
2025-07-22 12:47:31,981 - INFO - [test_io_stability_fix.py:273] - High volume test message 67
2025-07-22 12:47:31,981 - INFO - [test_io_stability_fix.py:273] - High volume test message 68
2025-07-22 12:47:31,982 - INFO - [test_io_stability_fix.py:273] - High volume test message 69
2025-07-22 12:47:31,982 - INFO - [test_io_stability_fix.py:273] - High volume test message 70
2025-07-22 12:47:31,983 - INFO - [test_io_stability_fix.py:273] - High volume test message 71
2025-07-22 12:47:31,984 - INFO - [test_io_stability_fix.py:273] - High volume test message 72
2025-07-22 12:47:31,985 - INFO - [test_io_stability_fix.py:273] - High volume test message 73
2025-07-22 12:47:31,986 - INFO - [test_io_stability_fix.py:273] - High volume test message 74
2025-07-22 12:47:31,987 - INFO - [test_io_stability_fix.py:273] - High volume test message 75
2025-07-22 12:47:31,988 - INFO - [test_io_stability_fix.py:273] - High volume test message 76
2025-07-22 12:47:31,988 - INFO - [test_io_stability_fix.py:273] - High volume test message 77
2025-07-22 12:47:31,989 - INFO - [test_io_stability_fix.py:273] - High volume test message 78
2025-07-22 12:47:31,989 - INFO - [test_io_stability_fix.py:273] - High volume test message 79
2025-07-22 12:47:31,990 - INFO - [test_io_stability_fix.py:273] - High volume test message 80
2025-07-22 12:47:31,990 - INFO - [test_io_stability_fix.py:273] - High volume test message 81
2025-07-22 12:47:31,991 - INFO - [test_io_stability_fix.py:273] - High volume test message 82
2025-07-22 12:47:31,991 - INFO - [test_io_stability_fix.py:273] - High volume test message 83
2025-07-22 12:47:31,992 - INFO - [test_io_stability_fix.py:273] - High volume test message 84
2025-07-22 12:47:31,993 - INFO - [test_io_stability_fix.py:273] - High volume test message 85
2025-07-22 12:47:31,993 - INFO - [test_io_stability_fix.py:273] - High volume test message 86
2025-07-22 12:47:31,994 - INFO - [test_io_stability_fix.py:273] - High volume test message 87
2025-07-22 12:47:31,995 - INFO - [test_io_stability_fix.py:273] - High volume test message 88
2025-07-22 12:47:31,995 - INFO - [test_io_stability_fix.py:273] - High volume test message 89
2025-07-22 12:47:31,996 - INFO - [test_io_stability_fix.py:273] - High volume test message 90
2025-07-22 12:47:31,996 - INFO - [test_io_stability_fix.py:273] - High volume test message 91
2025-07-22 12:47:31,997 - INFO - [test_io_stability_fix.py:273] - High volume test message 92
2025-07-22 12:47:31,998 - INFO - [test_io_stability_fix.py:273] - High volume test message 93
2025-07-22 12:47:31,999 - INFO - [test_io_stability_fix.py:273] - High volume test message 94
2025-07-22 12:47:31,999 - INFO - [test_io_stability_fix.py:273] - High volume test message 95
2025-07-22 12:47:32,001 - INFO - [test_io_stability_fix.py:273] - High volume test message 96
2025-07-22 12:47:32,002 - INFO - [test_io_stability_fix.py:273] - High volume test message 97
2025-07-22 12:47:32,002 - INFO - [test_io_stability_fix.py:273] - High volume test message 98
2025-07-22 12:47:32,003 - INFO - [test_io_stability_fix.py:273] - High volume test message 99
2025-07-22 12:47:32,004 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 0
2025-07-22 12:47:32,005 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 0
2025-07-22 12:47:32,006 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 0
2025-07-22 12:47:32,006 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 0
2025-07-22 12:47:32,006 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 0
2025-07-22 12:47:32,016 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 1
2025-07-22 12:47:32,017 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 1
2025-07-22 12:47:32,017 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 1
2025-07-22 12:47:32,018 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 1
2025-07-22 12:47:32,018 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 1
2025-07-22 12:47:32,028 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 2
2025-07-22 12:47:32,028 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 2
2025-07-22 12:47:32,029 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 2
2025-07-22 12:47:32,029 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 2
2025-07-22 12:47:32,029 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 2
2025-07-22 12:47:32,039 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 3
2025-07-22 12:47:32,040 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 3
2025-07-22 12:47:32,040 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 3
2025-07-22 12:47:32,040 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 3
2025-07-22 12:47:32,041 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 3
2025-07-22 12:47:32,050 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 4
2025-07-22 12:47:32,051 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 4
2025-07-22 12:47:32,052 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 4
2025-07-22 12:47:32,052 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 4
2025-07-22 12:47:32,053 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 4
2025-07-22 12:47:32,062 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 5
2025-07-22 12:47:32,062 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 5
2025-07-22 12:47:32,063 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 5
2025-07-22 12:47:32,063 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 5
2025-07-22 12:47:32,064 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 5
2025-07-22 12:47:32,073 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 6
2025-07-22 12:47:32,074 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 6
2025-07-22 12:47:32,074 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 6
2025-07-22 12:47:32,075 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 6
2025-07-22 12:47:32,075 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 6
2025-07-22 12:47:32,084 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 7
2025-07-22 12:47:32,085 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 7
2025-07-22 12:47:32,085 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 7
2025-07-22 12:47:32,086 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 7
2025-07-22 12:47:32,086 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 7
2025-07-22 12:47:32,097 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 8
2025-07-22 12:47:32,097 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 8
2025-07-22 12:47:32,097 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 8
2025-07-22 12:47:32,098 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 8
2025-07-22 12:47:32,098 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 8
2025-07-22 12:47:32,108 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 9
2025-07-22 12:47:32,108 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 9
2025-07-22 12:47:32,109 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 9
2025-07-22 12:47:32,109 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 9
2025-07-22 12:47:32,110 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 9
2025-07-22 12:49:15,157 - INFO - [main.py:71] - ============================================================
2025-07-22 12:49:15,158 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-22 12:49:15,158 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:49:15,159 - INFO - [main.py:74] - Platform: win32
2025-07-22 12:49:15,159 - INFO - [main.py:75] - ============================================================
2025-07-22 12:49:17,338 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-22 12:49:17,365 - INFO - [teaching_rules.py:88] - 🎓 Enhanced Teaching Rules Engine initialized with adaptive validation
2025-07-22 12:49:17,520 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-22 12:49:17,521 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-22 12:49:17,522 - INFO - [main.py:757] - ================================================================================
2025-07-22 12:49:17,523 - INFO - [main.py:758] - LESSON MANAGER BACKEND STARTING UP
2025-07-22 12:49:17,523 - INFO - [main.py:759] - ================================================================================
2025-07-22 12:49:17,524 - INFO - [main.py:760] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:49:17,524 - INFO - [main.py:761] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-22 12:49:17,525 - INFO - [main.py:762] - Log level: DEBUG
2025-07-22 12:49:17,525 - INFO - [main.py:763] - ================================================================================
2025-07-22 12:49:17,525 - INFO - [main.py:765] - Logging configuration complete with immediate console output
2025-07-22 12:49:17,525 - INFO - [main.py:766] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-22 12:49:17,527 - INFO - [main.py:1463] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-22 12:49:17,527 - INFO - [main.py:1470] - [OK] Enhanced state and auth managers initialized successfully
2025-07-22 12:49:17,529 - INFO - [main.py:1702] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-22 12:49:17,537 - INFO - [main.py:1731] - Phase transition fixes imported successfully
2025-07-22 12:49:17,540 - INFO - [main.py:5215] - Successfully imported utils functions
2025-07-22 12:49:17,541 - INFO - [main.py:5223] - Successfully imported extract_ai_state functions
2025-07-22 12:49:17,545 - INFO - [main.py:5673] - FLASK: Using unified Firebase initialization approach...
2025-07-22 12:49:17,546 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-22 12:49:17,547 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-22 12:49:17,547 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-22 12:49:17,547 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-22 12:49:17,592 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-22 12:49:17,592 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-22 12:49:18,104 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-22 12:49:18,603 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-22 12:49:19,380 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-22 12:49:19,381 - INFO - [main.py:5681] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-22 12:49:19,381 - INFO - [main.py:5771] - Gemini API will be initialized on first use (lazy loading).
2025-07-22 12:49:19,390 - INFO - [main.py:20330] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-22 12:49:19,391 - INFO - [main.py:20373] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-22 12:49:19,397 - INFO - [main.py:2190] - Successfully imported timetable_generator functions
2025-07-22 12:49:19,403 - INFO - [main.py:26773] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-22 12:49:19,484 - INFO - [main.py:26776] - Google Cloud Storage client initialized successfully.
2025-07-22 12:49:19,489 - INFO - [test_io_stability_fix.py:66] - Test message for normal logging
2025-07-22 12:49:19,490 - DEBUG - [test_io_stability_fix.py:67] - Debug message
2025-07-22 12:49:19,491 - WARNING - [test_io_stability_fix.py:68] - Warning message
2025-07-22 12:49:19,491 - ERROR - [test_io_stability_fix.py:69] - Error message
2025-07-22 12:49:19,493 - INFO - [test_io_stability_fix.py:121] - Test message with closed stderr
2025-07-22 12:49:19,493 - INFO - [test_io_stability_fix.py:150] - Test message with both streams closed
2025-07-22 12:49:19,494 - INFO - [test_io_stability_fix.py:174] - Test message with None streams
2025-07-22 12:49:19,494 - INFO - [test_io_stability_fix.py:208] - Test message with mock streams
2025-07-22 12:49:19,495 - INFO - [test_io_stability_fix.py:238] - Test message with Unicode: 🚀 ✅ → 📊
2025-07-22 12:49:19,496 - INFO - [test_io_stability_fix.py:273] - High volume test message 0
2025-07-22 12:49:19,497 - INFO - [test_io_stability_fix.py:273] - High volume test message 1
2025-07-22 12:49:19,498 - INFO - [test_io_stability_fix.py:273] - High volume test message 2
2025-07-22 12:49:19,498 - INFO - [test_io_stability_fix.py:273] - High volume test message 3
2025-07-22 12:49:19,499 - INFO - [test_io_stability_fix.py:273] - High volume test message 4
2025-07-22 12:49:19,499 - INFO - [test_io_stability_fix.py:273] - High volume test message 5
2025-07-22 12:49:19,500 - INFO - [test_io_stability_fix.py:273] - High volume test message 6
2025-07-22 12:49:19,501 - INFO - [test_io_stability_fix.py:273] - High volume test message 7
2025-07-22 12:49:19,501 - INFO - [test_io_stability_fix.py:273] - High volume test message 8
2025-07-22 12:49:19,502 - INFO - [test_io_stability_fix.py:273] - High volume test message 9
2025-07-22 12:49:19,502 - INFO - [test_io_stability_fix.py:273] - High volume test message 10
2025-07-22 12:49:19,503 - INFO - [test_io_stability_fix.py:273] - High volume test message 11
2025-07-22 12:49:19,503 - INFO - [test_io_stability_fix.py:273] - High volume test message 12
2025-07-22 12:49:19,504 - INFO - [test_io_stability_fix.py:273] - High volume test message 13
2025-07-22 12:49:19,504 - INFO - [test_io_stability_fix.py:273] - High volume test message 14
2025-07-22 12:49:19,505 - INFO - [test_io_stability_fix.py:273] - High volume test message 15
2025-07-22 12:49:19,505 - INFO - [test_io_stability_fix.py:273] - High volume test message 16
2025-07-22 12:49:19,506 - INFO - [test_io_stability_fix.py:273] - High volume test message 17
2025-07-22 12:49:19,506 - INFO - [test_io_stability_fix.py:273] - High volume test message 18
2025-07-22 12:49:19,506 - INFO - [test_io_stability_fix.py:273] - High volume test message 19
2025-07-22 12:49:19,507 - INFO - [test_io_stability_fix.py:273] - High volume test message 20
2025-07-22 12:49:19,507 - INFO - [test_io_stability_fix.py:273] - High volume test message 21
2025-07-22 12:49:19,508 - INFO - [test_io_stability_fix.py:273] - High volume test message 22
2025-07-22 12:49:19,509 - INFO - [test_io_stability_fix.py:273] - High volume test message 23
2025-07-22 12:49:19,510 - INFO - [test_io_stability_fix.py:273] - High volume test message 24
2025-07-22 12:49:19,511 - INFO - [test_io_stability_fix.py:273] - High volume test message 25
2025-07-22 12:49:19,512 - INFO - [test_io_stability_fix.py:273] - High volume test message 26
2025-07-22 12:49:19,512 - INFO - [test_io_stability_fix.py:273] - High volume test message 27
2025-07-22 12:49:19,513 - INFO - [test_io_stability_fix.py:273] - High volume test message 28
2025-07-22 12:49:19,514 - INFO - [test_io_stability_fix.py:273] - High volume test message 29
2025-07-22 12:49:19,515 - INFO - [test_io_stability_fix.py:273] - High volume test message 30
2025-07-22 12:49:19,516 - INFO - [test_io_stability_fix.py:273] - High volume test message 31
2025-07-22 12:49:19,518 - INFO - [test_io_stability_fix.py:273] - High volume test message 32
2025-07-22 12:49:19,519 - INFO - [test_io_stability_fix.py:273] - High volume test message 33
2025-07-22 12:49:19,520 - INFO - [test_io_stability_fix.py:273] - High volume test message 34
2025-07-22 12:49:19,521 - INFO - [test_io_stability_fix.py:273] - High volume test message 35
2025-07-22 12:49:19,522 - INFO - [test_io_stability_fix.py:273] - High volume test message 36
2025-07-22 12:49:19,522 - INFO - [test_io_stability_fix.py:273] - High volume test message 37
2025-07-22 12:49:19,523 - INFO - [test_io_stability_fix.py:273] - High volume test message 38
2025-07-22 12:49:19,524 - INFO - [test_io_stability_fix.py:273] - High volume test message 39
2025-07-22 12:49:19,524 - INFO - [test_io_stability_fix.py:273] - High volume test message 40
2025-07-22 12:49:19,525 - INFO - [test_io_stability_fix.py:273] - High volume test message 41
2025-07-22 12:49:19,526 - INFO - [test_io_stability_fix.py:273] - High volume test message 42
2025-07-22 12:49:19,527 - INFO - [test_io_stability_fix.py:273] - High volume test message 43
2025-07-22 12:49:19,527 - INFO - [test_io_stability_fix.py:273] - High volume test message 44
2025-07-22 12:49:19,528 - INFO - [test_io_stability_fix.py:273] - High volume test message 45
2025-07-22 12:49:19,529 - INFO - [test_io_stability_fix.py:273] - High volume test message 46
2025-07-22 12:49:19,530 - INFO - [test_io_stability_fix.py:273] - High volume test message 47
2025-07-22 12:49:19,530 - INFO - [test_io_stability_fix.py:273] - High volume test message 48
2025-07-22 12:49:19,532 - INFO - [test_io_stability_fix.py:273] - High volume test message 49
2025-07-22 12:49:19,533 - INFO - [test_io_stability_fix.py:273] - High volume test message 50
2025-07-22 12:49:19,534 - INFO - [test_io_stability_fix.py:273] - High volume test message 51
2025-07-22 12:49:19,536 - INFO - [test_io_stability_fix.py:273] - High volume test message 52
2025-07-22 12:49:19,537 - INFO - [test_io_stability_fix.py:273] - High volume test message 53
2025-07-22 12:49:19,538 - INFO - [test_io_stability_fix.py:273] - High volume test message 54
2025-07-22 12:49:19,539 - INFO - [test_io_stability_fix.py:273] - High volume test message 55
2025-07-22 12:49:19,540 - INFO - [test_io_stability_fix.py:273] - High volume test message 56
2025-07-22 12:49:19,540 - INFO - [test_io_stability_fix.py:273] - High volume test message 57
2025-07-22 12:49:19,541 - INFO - [test_io_stability_fix.py:273] - High volume test message 58
2025-07-22 12:49:19,541 - INFO - [test_io_stability_fix.py:273] - High volume test message 59
2025-07-22 12:49:19,542 - INFO - [test_io_stability_fix.py:273] - High volume test message 60
2025-07-22 12:49:19,542 - INFO - [test_io_stability_fix.py:273] - High volume test message 61
2025-07-22 12:49:19,543 - INFO - [test_io_stability_fix.py:273] - High volume test message 62
2025-07-22 12:49:19,544 - INFO - [test_io_stability_fix.py:273] - High volume test message 63
2025-07-22 12:49:19,544 - INFO - [test_io_stability_fix.py:273] - High volume test message 64
2025-07-22 12:49:19,545 - INFO - [test_io_stability_fix.py:273] - High volume test message 65
2025-07-22 12:49:19,545 - INFO - [test_io_stability_fix.py:273] - High volume test message 66
2025-07-22 12:49:19,546 - INFO - [test_io_stability_fix.py:273] - High volume test message 67
2025-07-22 12:49:19,546 - INFO - [test_io_stability_fix.py:273] - High volume test message 68
2025-07-22 12:49:19,547 - INFO - [test_io_stability_fix.py:273] - High volume test message 69
2025-07-22 12:49:19,548 - INFO - [test_io_stability_fix.py:273] - High volume test message 70
2025-07-22 12:49:19,548 - INFO - [test_io_stability_fix.py:273] - High volume test message 71
2025-07-22 12:49:19,549 - INFO - [test_io_stability_fix.py:273] - High volume test message 72
2025-07-22 12:49:19,551 - INFO - [test_io_stability_fix.py:273] - High volume test message 73
2025-07-22 12:49:19,552 - INFO - [test_io_stability_fix.py:273] - High volume test message 74
2025-07-22 12:49:19,553 - INFO - [test_io_stability_fix.py:273] - High volume test message 75
2025-07-22 12:49:19,553 - INFO - [test_io_stability_fix.py:273] - High volume test message 76
2025-07-22 12:49:19,554 - INFO - [test_io_stability_fix.py:273] - High volume test message 77
2025-07-22 12:49:19,554 - INFO - [test_io_stability_fix.py:273] - High volume test message 78
2025-07-22 12:49:19,555 - INFO - [test_io_stability_fix.py:273] - High volume test message 79
2025-07-22 12:49:19,555 - INFO - [test_io_stability_fix.py:273] - High volume test message 80
2025-07-22 12:49:19,555 - INFO - [test_io_stability_fix.py:273] - High volume test message 81
2025-07-22 12:49:19,556 - INFO - [test_io_stability_fix.py:273] - High volume test message 82
2025-07-22 12:49:19,557 - INFO - [test_io_stability_fix.py:273] - High volume test message 83
2025-07-22 12:49:19,557 - INFO - [test_io_stability_fix.py:273] - High volume test message 84
2025-07-22 12:49:19,558 - INFO - [test_io_stability_fix.py:273] - High volume test message 85
2025-07-22 12:49:19,558 - INFO - [test_io_stability_fix.py:273] - High volume test message 86
2025-07-22 12:49:19,559 - INFO - [test_io_stability_fix.py:273] - High volume test message 87
2025-07-22 12:49:19,559 - INFO - [test_io_stability_fix.py:273] - High volume test message 88
2025-07-22 12:49:19,560 - INFO - [test_io_stability_fix.py:273] - High volume test message 89
2025-07-22 12:49:19,561 - INFO - [test_io_stability_fix.py:273] - High volume test message 90
2025-07-22 12:49:19,561 - INFO - [test_io_stability_fix.py:273] - High volume test message 91
2025-07-22 12:49:19,561 - INFO - [test_io_stability_fix.py:273] - High volume test message 92
2025-07-22 12:49:19,562 - INFO - [test_io_stability_fix.py:273] - High volume test message 93
2025-07-22 12:49:19,563 - INFO - [test_io_stability_fix.py:273] - High volume test message 94
2025-07-22 12:49:19,563 - INFO - [test_io_stability_fix.py:273] - High volume test message 95
2025-07-22 12:49:19,564 - INFO - [test_io_stability_fix.py:273] - High volume test message 96
2025-07-22 12:49:19,565 - INFO - [test_io_stability_fix.py:273] - High volume test message 97
2025-07-22 12:49:19,566 - INFO - [test_io_stability_fix.py:273] - High volume test message 98
2025-07-22 12:49:19,568 - INFO - [test_io_stability_fix.py:273] - High volume test message 99
2025-07-22 12:49:19,569 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 0
2025-07-22 12:49:19,570 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 0
2025-07-22 12:49:19,570 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 0
2025-07-22 12:49:19,570 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 0
2025-07-22 12:49:19,571 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 0
2025-07-22 12:49:19,581 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 1
2025-07-22 12:49:19,581 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 1
2025-07-22 12:49:19,582 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 1
2025-07-22 12:49:19,582 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 1
2025-07-22 12:49:19,582 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 1
2025-07-22 12:49:19,593 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 2
2025-07-22 12:49:19,594 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 2
2025-07-22 12:49:19,595 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 2
2025-07-22 12:49:19,595 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 2
2025-07-22 12:49:19,596 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 2
2025-07-22 12:49:19,604 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 3
2025-07-22 12:49:19,605 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 3
2025-07-22 12:49:19,606 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 3
2025-07-22 12:49:19,606 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 3
2025-07-22 12:49:19,607 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 3
2025-07-22 12:49:19,616 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 4
2025-07-22 12:49:19,617 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 4
2025-07-22 12:49:19,617 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 4
2025-07-22 12:49:19,618 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 4
2025-07-22 12:49:19,619 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 4
2025-07-22 12:49:19,628 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 5
2025-07-22 12:49:19,629 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 5
2025-07-22 12:49:19,629 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 5
2025-07-22 12:49:19,630 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 5
2025-07-22 12:49:19,630 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 5
2025-07-22 12:49:19,640 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 6
2025-07-22 12:49:19,640 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 6
2025-07-22 12:49:19,641 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 6
2025-07-22 12:49:19,642 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 6
2025-07-22 12:49:19,642 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 6
2025-07-22 12:49:19,651 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 7
2025-07-22 12:49:19,651 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 7
2025-07-22 12:49:19,652 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 7
2025-07-22 12:49:19,653 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 7
2025-07-22 12:49:19,653 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 7
2025-07-22 12:49:19,662 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 8
2025-07-22 12:49:19,663 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 8
2025-07-22 12:49:19,663 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 8
2025-07-22 12:49:19,664 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 8
2025-07-22 12:49:19,664 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 8
2025-07-22 12:49:19,673 - INFO - [test_io_stability_fix.py:290] - Worker 0 - Message 9
2025-07-22 12:49:19,674 - INFO - [test_io_stability_fix.py:290] - Worker 1 - Message 9
2025-07-22 12:49:19,674 - INFO - [test_io_stability_fix.py:290] - Worker 3 - Message 9
2025-07-22 12:49:19,675 - INFO - [test_io_stability_fix.py:290] - Worker 2 - Message 9
2025-07-22 12:49:19,675 - INFO - [test_io_stability_fix.py:290] - Worker 4 - Message 9
2025-07-22 12:50:48,147 - INFO - [main.py:71] - ============================================================
2025-07-22 12:50:48,149 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-22 12:50:48,150 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:50:48,151 - INFO - [main.py:74] - Platform: win32
2025-07-22 12:50:48,152 - INFO - [main.py:75] - ============================================================
2025-07-22 12:50:51,502 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-22 12:50:51,590 - INFO - [teaching_rules.py:88] - 🎓 Enhanced Teaching Rules Engine initialized with adaptive validation
2025-07-22 12:50:51,852 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-22 12:50:51,853 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-22 12:50:51,855 - INFO - [main.py:757] - ================================================================================
2025-07-22 12:50:51,855 - INFO - [main.py:758] - LESSON MANAGER BACKEND STARTING UP
2025-07-22 12:50:51,856 - INFO - [main.py:759] - ================================================================================
2025-07-22 12:50:51,856 - INFO - [main.py:760] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:50:51,857 - INFO - [main.py:761] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-22 12:50:51,857 - INFO - [main.py:762] - Log level: DEBUG
2025-07-22 12:50:51,858 - INFO - [main.py:763] - ================================================================================
2025-07-22 12:50:51,858 - INFO - [main.py:765] - Logging configuration complete with immediate console output
2025-07-22 12:50:51,859 - INFO - [main.py:766] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-22 12:50:51,861 - INFO - [main.py:1463] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-22 12:50:51,862 - INFO - [main.py:1470] - [OK] Enhanced state and auth managers initialized successfully
2025-07-22 12:50:51,864 - INFO - [main.py:1702] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-22 12:50:51,875 - INFO - [main.py:1731] - Phase transition fixes imported successfully
2025-07-22 12:50:51,878 - INFO - [main.py:5215] - Successfully imported utils functions
2025-07-22 12:50:51,881 - INFO - [main.py:5223] - Successfully imported extract_ai_state functions
2025-07-22 12:50:51,892 - INFO - [main.py:5673] - FLASK: Using unified Firebase initialization approach...
2025-07-22 12:50:51,895 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-22 12:50:51,895 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-22 12:50:51,896 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-22 12:50:51,896 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-22 12:50:51,966 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-22 12:50:51,967 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-22 12:50:52,475 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-22 12:50:52,958 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-22 12:50:53,758 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-22 12:50:53,759 - INFO - [main.py:5681] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-22 12:50:53,759 - INFO - [main.py:5771] - Gemini API will be initialized on first use (lazy loading).
2025-07-22 12:50:53,772 - INFO - [main.py:20330] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-22 12:50:53,773 - INFO - [main.py:20373] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-22 12:50:53,782 - INFO - [main.py:2190] - Successfully imported timetable_generator functions
2025-07-22 12:50:53,791 - INFO - [main.py:26773] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-22 12:50:53,896 - INFO - [main.py:26776] - Google Cloud Storage client initialized successfully.
2025-07-22 12:50:53,900 - INFO - [test_io_stability_fix.py:66] - Test message for normal logging
2025-07-22 12:50:53,901 - DEBUG - [test_io_stability_fix.py:67] - Debug message
2025-07-22 12:50:53,901 - WARNING - [test_io_stability_fix.py:68] - Warning message
2025-07-22 12:50:53,902 - ERROR - [test_io_stability_fix.py:69] - Error message
2025-07-22 12:50:53,903 - INFO - [test_io_stability_fix.py:131] - Test message with closed stderr
2025-07-22 12:50:53,904 - INFO - [test_io_stability_fix.py:200] - Test message with None streams
2025-07-22 12:50:53,905 - INFO - [test_io_stability_fix.py:234] - Test message with mock streams
2025-07-22 12:50:53,907 - INFO - [test_io_stability_fix.py:315] - High volume test message 0
2025-07-22 12:50:53,908 - INFO - [test_io_stability_fix.py:315] - High volume test message 1
2025-07-22 12:50:53,908 - INFO - [test_io_stability_fix.py:315] - High volume test message 2
2025-07-22 12:50:53,909 - INFO - [test_io_stability_fix.py:315] - High volume test message 3
2025-07-22 12:50:53,910 - INFO - [test_io_stability_fix.py:315] - High volume test message 4
2025-07-22 12:50:53,910 - INFO - [test_io_stability_fix.py:315] - High volume test message 5
2025-07-22 12:50:53,911 - INFO - [test_io_stability_fix.py:315] - High volume test message 6
2025-07-22 12:50:53,911 - INFO - [test_io_stability_fix.py:315] - High volume test message 7
2025-07-22 12:50:53,912 - INFO - [test_io_stability_fix.py:315] - High volume test message 8
2025-07-22 12:50:53,912 - INFO - [test_io_stability_fix.py:315] - High volume test message 9
2025-07-22 12:50:53,913 - INFO - [test_io_stability_fix.py:315] - High volume test message 10
2025-07-22 12:50:53,914 - INFO - [test_io_stability_fix.py:315] - High volume test message 11
2025-07-22 12:50:53,914 - INFO - [test_io_stability_fix.py:315] - High volume test message 12
2025-07-22 12:50:53,915 - INFO - [test_io_stability_fix.py:315] - High volume test message 13
2025-07-22 12:50:53,915 - INFO - [test_io_stability_fix.py:315] - High volume test message 14
2025-07-22 12:50:53,916 - INFO - [test_io_stability_fix.py:315] - High volume test message 15
2025-07-22 12:50:53,919 - INFO - [test_io_stability_fix.py:315] - High volume test message 16
2025-07-22 12:50:53,920 - INFO - [test_io_stability_fix.py:315] - High volume test message 17
2025-07-22 12:50:53,920 - INFO - [test_io_stability_fix.py:315] - High volume test message 18
2025-07-22 12:50:53,921 - INFO - [test_io_stability_fix.py:315] - High volume test message 19
2025-07-22 12:50:53,921 - INFO - [test_io_stability_fix.py:315] - High volume test message 20
2025-07-22 12:50:53,922 - INFO - [test_io_stability_fix.py:315] - High volume test message 21
2025-07-22 12:50:53,922 - INFO - [test_io_stability_fix.py:315] - High volume test message 22
2025-07-22 12:50:53,923 - INFO - [test_io_stability_fix.py:315] - High volume test message 23
2025-07-22 12:50:53,924 - INFO - [test_io_stability_fix.py:315] - High volume test message 24
2025-07-22 12:50:53,924 - INFO - [test_io_stability_fix.py:315] - High volume test message 25
2025-07-22 12:50:53,925 - INFO - [test_io_stability_fix.py:315] - High volume test message 26
2025-07-22 12:50:53,925 - INFO - [test_io_stability_fix.py:315] - High volume test message 27
2025-07-22 12:50:53,926 - INFO - [test_io_stability_fix.py:315] - High volume test message 28
2025-07-22 12:50:53,926 - INFO - [test_io_stability_fix.py:315] - High volume test message 29
2025-07-22 12:50:53,927 - INFO - [test_io_stability_fix.py:315] - High volume test message 30
2025-07-22 12:50:53,927 - INFO - [test_io_stability_fix.py:315] - High volume test message 31
2025-07-22 12:50:53,928 - INFO - [test_io_stability_fix.py:315] - High volume test message 32
2025-07-22 12:50:53,928 - INFO - [test_io_stability_fix.py:315] - High volume test message 33
2025-07-22 12:50:53,929 - INFO - [test_io_stability_fix.py:315] - High volume test message 34
2025-07-22 12:50:53,930 - INFO - [test_io_stability_fix.py:315] - High volume test message 35
2025-07-22 12:50:53,930 - INFO - [test_io_stability_fix.py:315] - High volume test message 36
2025-07-22 12:50:53,931 - INFO - [test_io_stability_fix.py:315] - High volume test message 37
2025-07-22 12:50:53,931 - INFO - [test_io_stability_fix.py:315] - High volume test message 38
2025-07-22 12:50:53,933 - INFO - [test_io_stability_fix.py:315] - High volume test message 39
2025-07-22 12:50:53,934 - INFO - [test_io_stability_fix.py:315] - High volume test message 40
2025-07-22 12:50:53,936 - INFO - [test_io_stability_fix.py:315] - High volume test message 41
2025-07-22 12:50:53,936 - INFO - [test_io_stability_fix.py:315] - High volume test message 42
2025-07-22 12:50:53,937 - INFO - [test_io_stability_fix.py:315] - High volume test message 43
2025-07-22 12:50:53,938 - INFO - [test_io_stability_fix.py:315] - High volume test message 44
2025-07-22 12:50:53,940 - INFO - [test_io_stability_fix.py:315] - High volume test message 45
2025-07-22 12:50:53,940 - INFO - [test_io_stability_fix.py:315] - High volume test message 46
2025-07-22 12:50:53,941 - INFO - [test_io_stability_fix.py:315] - High volume test message 47
2025-07-22 12:50:53,942 - INFO - [test_io_stability_fix.py:315] - High volume test message 48
2025-07-22 12:50:53,942 - INFO - [test_io_stability_fix.py:315] - High volume test message 49
2025-07-22 12:50:53,943 - INFO - [test_io_stability_fix.py:315] - High volume test message 50
2025-07-22 12:50:53,943 - INFO - [test_io_stability_fix.py:315] - High volume test message 51
2025-07-22 12:50:53,944 - INFO - [test_io_stability_fix.py:315] - High volume test message 52
2025-07-22 12:50:53,944 - INFO - [test_io_stability_fix.py:315] - High volume test message 53
2025-07-22 12:50:53,945 - INFO - [test_io_stability_fix.py:315] - High volume test message 54
2025-07-22 12:50:53,946 - INFO - [test_io_stability_fix.py:315] - High volume test message 55
2025-07-22 12:50:53,946 - INFO - [test_io_stability_fix.py:315] - High volume test message 56
2025-07-22 12:50:53,947 - INFO - [test_io_stability_fix.py:315] - High volume test message 57
2025-07-22 12:50:53,947 - INFO - [test_io_stability_fix.py:315] - High volume test message 58
2025-07-22 12:50:53,948 - INFO - [test_io_stability_fix.py:315] - High volume test message 59
2025-07-22 12:50:53,948 - INFO - [test_io_stability_fix.py:315] - High volume test message 60
2025-07-22 12:50:53,950 - INFO - [test_io_stability_fix.py:315] - High volume test message 61
2025-07-22 12:50:53,951 - INFO - [test_io_stability_fix.py:315] - High volume test message 62
2025-07-22 12:50:53,952 - INFO - [test_io_stability_fix.py:315] - High volume test message 63
2025-07-22 12:50:53,953 - INFO - [test_io_stability_fix.py:315] - High volume test message 64
2025-07-22 12:50:53,954 - INFO - [test_io_stability_fix.py:315] - High volume test message 65
2025-07-22 12:50:53,955 - INFO - [test_io_stability_fix.py:315] - High volume test message 66
2025-07-22 12:50:53,956 - INFO - [test_io_stability_fix.py:315] - High volume test message 67
2025-07-22 12:50:53,956 - INFO - [test_io_stability_fix.py:315] - High volume test message 68
2025-07-22 12:50:53,957 - INFO - [test_io_stability_fix.py:315] - High volume test message 69
2025-07-22 12:50:53,957 - INFO - [test_io_stability_fix.py:315] - High volume test message 70
2025-07-22 12:50:53,958 - INFO - [test_io_stability_fix.py:315] - High volume test message 71
2025-07-22 12:50:53,958 - INFO - [test_io_stability_fix.py:315] - High volume test message 72
2025-07-22 12:50:53,959 - INFO - [test_io_stability_fix.py:315] - High volume test message 73
2025-07-22 12:50:53,960 - INFO - [test_io_stability_fix.py:315] - High volume test message 74
2025-07-22 12:50:53,960 - INFO - [test_io_stability_fix.py:315] - High volume test message 75
2025-07-22 12:50:53,961 - INFO - [test_io_stability_fix.py:315] - High volume test message 76
2025-07-22 12:50:53,961 - INFO - [test_io_stability_fix.py:315] - High volume test message 77
2025-07-22 12:50:53,962 - INFO - [test_io_stability_fix.py:315] - High volume test message 78
2025-07-22 12:50:53,962 - INFO - [test_io_stability_fix.py:315] - High volume test message 79
2025-07-22 12:50:53,963 - INFO - [test_io_stability_fix.py:315] - High volume test message 80
2025-07-22 12:50:53,963 - INFO - [test_io_stability_fix.py:315] - High volume test message 81
2025-07-22 12:50:53,964 - INFO - [test_io_stability_fix.py:315] - High volume test message 82
2025-07-22 12:50:53,965 - INFO - [test_io_stability_fix.py:315] - High volume test message 83
2025-07-22 12:50:53,966 - INFO - [test_io_stability_fix.py:315] - High volume test message 84
2025-07-22 12:50:53,967 - INFO - [test_io_stability_fix.py:315] - High volume test message 85
2025-07-22 12:50:53,968 - INFO - [test_io_stability_fix.py:315] - High volume test message 86
2025-07-22 12:50:53,969 - INFO - [test_io_stability_fix.py:315] - High volume test message 87
2025-07-22 12:50:53,970 - INFO - [test_io_stability_fix.py:315] - High volume test message 88
2025-07-22 12:50:53,971 - INFO - [test_io_stability_fix.py:315] - High volume test message 89
2025-07-22 12:50:53,971 - INFO - [test_io_stability_fix.py:315] - High volume test message 90
2025-07-22 12:50:53,972 - INFO - [test_io_stability_fix.py:315] - High volume test message 91
2025-07-22 12:50:53,972 - INFO - [test_io_stability_fix.py:315] - High volume test message 92
2025-07-22 12:50:53,973 - INFO - [test_io_stability_fix.py:315] - High volume test message 93
2025-07-22 12:50:53,973 - INFO - [test_io_stability_fix.py:315] - High volume test message 94
2025-07-22 12:50:53,974 - INFO - [test_io_stability_fix.py:315] - High volume test message 95
2025-07-22 12:50:53,974 - INFO - [test_io_stability_fix.py:315] - High volume test message 96
2025-07-22 12:50:53,975 - INFO - [test_io_stability_fix.py:315] - High volume test message 97
2025-07-22 12:50:53,976 - INFO - [test_io_stability_fix.py:315] - High volume test message 98
2025-07-22 12:50:53,976 - INFO - [test_io_stability_fix.py:315] - High volume test message 99
2025-07-22 12:50:53,977 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 0
2025-07-22 12:50:53,978 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 0
2025-07-22 12:50:53,978 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 0
2025-07-22 12:50:53,978 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 0
2025-07-22 12:50:53,979 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 0
2025-07-22 12:50:53,988 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 1
2025-07-22 12:50:53,989 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 1
2025-07-22 12:50:53,990 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 1
2025-07-22 12:50:53,990 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 1
2025-07-22 12:50:53,990 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 1
2025-07-22 12:50:53,999 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 2
2025-07-22 12:50:54,001 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 2
2025-07-22 12:50:54,002 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 2
2025-07-22 12:50:54,002 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 2
2025-07-22 12:50:54,002 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 2
2025-07-22 12:50:54,012 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 3
2025-07-22 12:50:54,013 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 3
2025-07-22 12:50:54,013 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 3
2025-07-22 12:50:54,014 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 3
2025-07-22 12:50:54,014 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 3
2025-07-22 12:50:54,023 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 4
2025-07-22 12:50:54,024 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 4
2025-07-22 12:50:54,024 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 4
2025-07-22 12:50:54,025 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 4
2025-07-22 12:50:54,026 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 4
2025-07-22 12:50:54,035 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 5
2025-07-22 12:50:54,035 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 5
2025-07-22 12:50:54,036 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 5
2025-07-22 12:50:54,036 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 5
2025-07-22 12:50:54,038 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 5
2025-07-22 12:50:54,049 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 6
2025-07-22 12:50:54,050 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 6
2025-07-22 12:50:54,052 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 6
2025-07-22 12:50:54,052 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 6
2025-07-22 12:50:54,053 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 6
2025-07-22 12:50:54,060 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 7
2025-07-22 12:50:54,063 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 7
2025-07-22 12:50:54,064 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 7
2025-07-22 12:50:54,064 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 7
2025-07-22 12:50:54,065 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 7
2025-07-22 12:50:54,071 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 8
2025-07-22 12:50:54,074 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 8
2025-07-22 12:50:54,075 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 8
2025-07-22 12:50:54,076 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 8
2025-07-22 12:50:54,076 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 8
2025-07-22 12:50:54,082 - INFO - [test_io_stability_fix.py:332] - Worker 0 - Message 9
2025-07-22 12:50:54,085 - INFO - [test_io_stability_fix.py:332] - Worker 1 - Message 9
2025-07-22 12:50:54,086 - INFO - [test_io_stability_fix.py:332] - Worker 3 - Message 9
2025-07-22 12:50:54,087 - INFO - [test_io_stability_fix.py:332] - Worker 4 - Message 9
2025-07-22 12:50:54,087 - INFO - [test_io_stability_fix.py:332] - Worker 2 - Message 9
2025-07-22 12:52:53,179 - INFO - [main.py:71] - ============================================================
2025-07-22 12:52:53,180 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-22 12:52:53,181 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:52:53,182 - INFO - [main.py:74] - Platform: win32
2025-07-22 12:52:53,182 - INFO - [main.py:75] - ============================================================
2025-07-22 12:52:55,815 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-22 12:52:55,861 - INFO - [teaching_rules.py:88] - 🎓 Enhanced Teaching Rules Engine initialized with adaptive validation
2025-07-22 12:52:56,076 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-22 12:52:56,077 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-22 12:52:56,078 - INFO - [main.py:757] - ================================================================================
2025-07-22 12:52:56,079 - INFO - [main.py:758] - LESSON MANAGER BACKEND STARTING UP
2025-07-22 12:52:56,079 - INFO - [main.py:759] - ================================================================================
2025-07-22 12:52:56,079 - INFO - [main.py:760] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:52:56,080 - INFO - [main.py:761] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-22 12:52:56,080 - INFO - [main.py:762] - Log level: DEBUG
2025-07-22 12:52:56,081 - INFO - [main.py:763] - ================================================================================
2025-07-22 12:52:56,081 - INFO - [main.py:765] - Logging configuration complete with immediate console output
2025-07-22 12:52:56,082 - INFO - [main.py:766] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-22 12:52:56,084 - INFO - [main.py:1463] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-22 12:52:56,085 - INFO - [main.py:1470] - [OK] Enhanced state and auth managers initialized successfully
2025-07-22 12:52:56,087 - INFO - [main.py:1702] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-22 12:52:56,095 - INFO - [main.py:1731] - Phase transition fixes imported successfully
2025-07-22 12:52:56,135 - INFO - [main.py:5215] - Successfully imported utils functions
2025-07-22 12:52:56,155 - INFO - [main.py:5223] - Successfully imported extract_ai_state functions
2025-07-22 12:52:56,163 - INFO - [main.py:5673] - FLASK: Using unified Firebase initialization approach...
2025-07-22 12:52:56,167 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-22 12:52:56,170 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-22 12:52:56,171 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-22 12:52:56,176 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-22 12:52:56,243 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-22 12:52:56,243 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-22 12:52:56,805 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-22 12:52:57,316 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-22 12:52:57,612 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-22 12:52:57,613 - INFO - [main.py:5681] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-22 12:52:57,613 - INFO - [main.py:5771] - Gemini API will be initialized on first use (lazy loading).
2025-07-22 12:52:57,623 - INFO - [main.py:20330] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-22 12:52:57,625 - INFO - [main.py:20373] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-22 12:52:57,641 - INFO - [main.py:2190] - Successfully imported timetable_generator functions
2025-07-22 12:52:57,656 - INFO - [main.py:26773] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-22 12:52:57,776 - INFO - [main.py:26776] - Google Cloud Storage client initialized successfully.
2025-07-22 12:52:57,779 - INFO - [test_io_stability_fix_modified.py:66] - Test message for normal logging
2025-07-22 12:52:57,779 - DEBUG - [test_io_stability_fix_modified.py:67] - Debug message
2025-07-22 12:52:57,780 - WARNING - [test_io_stability_fix_modified.py:68] - Warning message
2025-07-22 12:52:57,780 - ERROR - [test_io_stability_fix_modified.py:69] - Error message
2025-07-22 12:52:57,790 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 0
2025-07-22 12:52:57,790 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 1
2025-07-22 12:52:57,791 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 2
2025-07-22 12:52:57,791 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 3
2025-07-22 12:52:57,792 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 4
2025-07-22 12:52:57,792 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 5
2025-07-22 12:52:57,793 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 6
2025-07-22 12:52:57,793 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 7
2025-07-22 12:52:57,794 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 8
2025-07-22 12:52:57,794 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 9
2025-07-22 12:52:57,795 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 10
2025-07-22 12:52:57,795 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 11
2025-07-22 12:52:57,796 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 12
2025-07-22 12:52:57,796 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 13
2025-07-22 12:52:57,796 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 14
2025-07-22 12:52:57,797 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 15
2025-07-22 12:52:57,797 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 16
2025-07-22 12:52:57,798 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 17
2025-07-22 12:52:57,798 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 18
2025-07-22 12:52:57,799 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 19
2025-07-22 12:52:57,799 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 20
2025-07-22 12:52:57,799 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 21
2025-07-22 12:52:57,800 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 22
2025-07-22 12:52:57,801 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 23
2025-07-22 12:52:57,802 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 24
2025-07-22 12:52:57,802 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 25
2025-07-22 12:52:57,803 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 26
2025-07-22 12:52:57,804 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 27
2025-07-22 12:52:57,805 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 28
2025-07-22 12:52:57,805 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 29
2025-07-22 12:52:57,806 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 30
2025-07-22 12:52:57,806 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 31
2025-07-22 12:52:57,807 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 32
2025-07-22 12:52:57,807 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 33
2025-07-22 12:52:57,808 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 34
2025-07-22 12:52:57,808 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 35
2025-07-22 12:52:57,809 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 36
2025-07-22 12:52:57,809 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 37
2025-07-22 12:52:57,810 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 38
2025-07-22 12:52:57,810 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 39
2025-07-22 12:52:57,810 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 40
2025-07-22 12:52:57,811 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 41
2025-07-22 12:52:57,811 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 42
2025-07-22 12:52:57,811 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 43
2025-07-22 12:52:57,812 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 44
2025-07-22 12:52:57,812 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 45
2025-07-22 12:52:57,813 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 46
2025-07-22 12:52:57,813 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 47
2025-07-22 12:52:57,814 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 48
2025-07-22 12:52:57,814 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 49
2025-07-22 12:52:57,815 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 50
2025-07-22 12:52:57,815 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 51
2025-07-22 12:52:57,816 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 52
2025-07-22 12:52:57,816 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 53
2025-07-22 12:52:57,816 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 54
2025-07-22 12:52:57,818 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 55
2025-07-22 12:52:57,818 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 56
2025-07-22 12:52:57,819 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 57
2025-07-22 12:52:57,820 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 58
2025-07-22 12:52:57,822 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 59
2025-07-22 12:52:57,823 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 60
2025-07-22 12:52:57,823 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 61
2025-07-22 12:52:57,824 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 62
2025-07-22 12:52:57,825 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 63
2025-07-22 12:52:57,826 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 64
2025-07-22 12:52:57,826 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 65
2025-07-22 12:52:57,827 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 66
2025-07-22 12:52:57,827 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 67
2025-07-22 12:52:57,828 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 68
2025-07-22 12:52:57,828 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 69
2025-07-22 12:52:57,829 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 70
2025-07-22 12:52:57,829 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 71
2025-07-22 12:52:57,830 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 72
2025-07-22 12:52:57,830 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 73
2025-07-22 12:52:57,831 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 74
2025-07-22 12:52:57,831 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 75
2025-07-22 12:52:57,832 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 76
2025-07-22 12:52:57,832 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 77
2025-07-22 12:52:57,833 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 78
2025-07-22 12:52:57,833 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 79
2025-07-22 12:52:57,835 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 80
2025-07-22 12:52:57,835 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 81
2025-07-22 12:52:57,836 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 82
2025-07-22 12:52:57,837 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 83
2025-07-22 12:52:57,837 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 84
2025-07-22 12:52:57,838 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 85
2025-07-22 12:52:57,838 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 86
2025-07-22 12:52:57,840 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 87
2025-07-22 12:52:57,841 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 88
2025-07-22 12:52:57,842 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 89
2025-07-22 12:52:57,843 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 90
2025-07-22 12:52:57,843 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 91
2025-07-22 12:52:57,844 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 92
2025-07-22 12:52:57,844 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 93
2025-07-22 12:52:57,845 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 94
2025-07-22 12:52:57,845 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 95
2025-07-22 12:52:57,846 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 96
2025-07-22 12:52:57,847 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 97
2025-07-22 12:52:57,847 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 98
2025-07-22 12:52:57,848 - INFO - [test_io_stability_fix_modified.py:233] - High volume test message 99
2025-07-22 12:52:57,849 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 0
2025-07-22 12:52:57,849 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 0
2025-07-22 12:52:57,850 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 0
2025-07-22 12:52:57,851 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 0
2025-07-22 12:52:57,852 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 0
2025-07-22 12:52:57,860 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 1
2025-07-22 12:52:57,862 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 1
2025-07-22 12:52:57,862 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 1
2025-07-22 12:52:57,863 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 1
2025-07-22 12:52:57,863 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 1
2025-07-22 12:52:57,871 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 2
2025-07-22 12:52:57,872 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 2
2025-07-22 12:52:57,873 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 2
2025-07-22 12:52:57,873 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 2
2025-07-22 12:52:57,874 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 2
2025-07-22 12:52:57,883 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 3
2025-07-22 12:52:57,884 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 3
2025-07-22 12:52:57,884 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 3
2025-07-22 12:52:57,885 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 3
2025-07-22 12:52:57,885 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 3
2025-07-22 12:52:57,895 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 4
2025-07-22 12:52:57,895 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 4
2025-07-22 12:52:57,895 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 4
2025-07-22 12:52:57,896 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 4
2025-07-22 12:52:57,896 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 4
2025-07-22 12:52:57,906 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 5
2025-07-22 12:52:57,906 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 5
2025-07-22 12:52:57,907 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 5
2025-07-22 12:52:57,907 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 5
2025-07-22 12:52:57,907 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 5
2025-07-22 12:52:57,917 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 6
2025-07-22 12:52:57,918 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 6
2025-07-22 12:52:57,918 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 6
2025-07-22 12:52:57,918 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 6
2025-07-22 12:52:57,919 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 6
2025-07-22 12:52:57,929 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 7
2025-07-22 12:52:57,929 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 7
2025-07-22 12:52:57,930 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 7
2025-07-22 12:52:57,930 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 7
2025-07-22 12:52:57,931 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 7
2025-07-22 12:52:57,940 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 8
2025-07-22 12:52:57,940 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 8
2025-07-22 12:52:57,941 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 8
2025-07-22 12:52:57,941 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 8
2025-07-22 12:52:57,941 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 8
2025-07-22 12:52:57,951 - INFO - [test_io_stability_fix_modified.py:250] - Worker 0 - Message 9
2025-07-22 12:52:57,951 - INFO - [test_io_stability_fix_modified.py:250] - Worker 1 - Message 9
2025-07-22 12:52:57,952 - INFO - [test_io_stability_fix_modified.py:250] - Worker 3 - Message 9
2025-07-22 12:52:57,953 - INFO - [test_io_stability_fix_modified.py:250] - Worker 4 - Message 9
2025-07-22 12:52:57,953 - INFO - [test_io_stability_fix_modified.py:250] - Worker 2 - Message 9
2025-07-22 12:54:29,548 - INFO - [main.py:71] - ============================================================
2025-07-22 12:54:29,549 - INFO - [main.py:72] - >>> FORCEFUL LOGGING INITIALIZED (UTF-8 ENABLED)
2025-07-22 12:54:29,549 - INFO - [main.py:73] - Python Version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:54:29,550 - INFO - [main.py:74] - Platform: win32
2025-07-22 12:54:29,550 - INFO - [main.py:75] - ============================================================
2025-07-22 12:54:31,209 - DEBUG - [_opentelemetry_tracing.py:42] - This service is instrumented using OpenTelemetry. OpenTelemetry or one of its components could not be imported; please add compatible versions of opentelemetry-api and opentelemetry-instrumentation packages in order to get Storage Tracing data.
2025-07-22 12:54:31,236 - INFO - [teaching_rules.py:88] - 🎓 Enhanced Teaching Rules Engine initialized with adaptive validation
2025-07-22 12:54:31,377 - INFO - [main.py:156] - INIT_INFO: Loaded .env file from: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\.env
2025-07-22 12:54:31,377 - INFO - [main.py:476] - INIT_INFO: Firebase initialization deferred until server startup or first use
2025-07-22 12:54:31,379 - INFO - [main.py:757] - ================================================================================
2025-07-22 12:54:31,379 - INFO - [main.py:758] - LESSON MANAGER BACKEND STARTING UP
2025-07-22 12:54:31,380 - INFO - [main.py:759] - ================================================================================
2025-07-22 12:54:31,380 - INFO - [main.py:760] - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-07-22 12:54:31,381 - INFO - [main.py:761] - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website
2025-07-22 12:54:31,381 - INFO - [main.py:762] - Log level: DEBUG
2025-07-22 12:54:31,381 - INFO - [main.py:763] - ================================================================================
2025-07-22 12:54:31,382 - INFO - [main.py:765] - Logging configuration complete with immediate console output
2025-07-22 12:54:31,382 - INFO - [main.py:766] - LOG SETUP COMPLETE - Console output should now be visible
2025-07-22 12:54:31,384 - INFO - [main.py:1463] - INIT_INFO: Flask app instance created and CORS configured.
2025-07-22 12:54:31,384 - INFO - [main.py:1470] - [OK] Enhanced state and auth managers initialized successfully
2025-07-22 12:54:31,388 - INFO - [main.py:1702] - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
2025-07-22 12:54:31,394 - INFO - [main.py:1731] - Phase transition fixes imported successfully
2025-07-22 12:54:31,396 - INFO - [main.py:5215] - Successfully imported utils functions
2025-07-22 12:54:31,397 - INFO - [main.py:5223] - Successfully imported extract_ai_state functions
2025-07-22 12:54:31,404 - INFO - [main.py:5673] - FLASK: Using unified Firebase initialization approach...
2025-07-22 12:54:31,406 - INFO - [unified_firebase_init.py:87] - Using service account: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\solynta-academy-firebase-adminsdk-fbsvc-a49c072c5d.json
2025-07-22 12:54:31,406 - INFO - [unified_firebase_init.py:88] -   Project: solynta-academy
2025-07-22 12:54:31,407 - INFO - [unified_firebase_init.py:89] -   Email: <EMAIL>
2025-07-22 12:54:31,407 - INFO - [unified_firebase_init.py:90] -   Key ID: a49c072c5de7e8a267ea9921f5045b606c4e5873
2025-07-22 12:54:31,450 - INFO - [unified_firebase_init.py:98] - ✅ Firebase Admin initialized successfully with correct credentials
2025-07-22 12:54:31,450 - INFO - [unified_firebase_init.py:102] - ✅ Firestore client initialized successfully
2025-07-22 12:54:31,914 - DEBUG - [connectionpool.py:1022] - Starting new HTTPS connection (1): oauth2.googleapis.com:443
2025-07-22 12:54:32,396 - DEBUG - [connectionpool.py:475] - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
2025-07-22 12:54:32,675 - INFO - [unified_firebase_init.py:111] - ✅ Firestore connection test successful
2025-07-22 12:54:32,675 - INFO - [main.py:5681] - FLASK: Unified Firebase initialization successful - Firestore client ready
2025-07-22 12:54:32,676 - INFO - [main.py:5771] - Gemini API will be initialized on first use (lazy loading).
2025-07-22 12:54:32,685 - INFO - [main.py:20330] - 🔗 REGISTERING DEBUG LESSON NOTES ROUTE...
2025-07-22 12:54:32,686 - INFO - [main.py:20373] - 🔗 REGISTERING LESSON NOTES ROUTES...
2025-07-22 12:54:32,695 - INFO - [main.py:2190] - Successfully imported timetable_generator functions
2025-07-22 12:54:32,700 - INFO - [main.py:26773] - Set GOOGLE_APPLICATION_CREDENTIALS to: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\secure\firebase-credentials.json
2025-07-22 12:54:32,780 - INFO - [main.py:26776] - Google Cloud Storage client initialized successfully.
