# -*- coding: utf-8 -*-
"""
Quiz Phase Handler for Unified Lesson Flow

This module implements the quiz phase handlers for the unified lesson flow state machine.
It manages the quiz_initiate and quiz_questions phases, and detects when to transition to quiz_results.

Key features:
- Serves the first quiz question programmatically in quiz_initiate phase
- Processes answers and serves subsequent questions in quiz_questions phase
- Detects quiz completion and transitions to quiz_results phase
- Ensures proper phase transitions according to the unified lesson pathway
"""

import logging
import time
import json
import random
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class QuizPhaseHandler:
    """
    Handles quiz phase processing for the unified lesson flow state machine.
    Manages quiz_initiate and quiz_questions phases with transition to quiz_results.
    """
    
    def __init__(self):
        """Initialize the quiz phase handler"""
        self.quiz_completion_threshold = 10  # Maximum number of quiz questions
        logger.info("Quiz Phase Handler initialized")
    
    def handle_quiz_initiate(self, 
                           session_data: Dict[str, Any], 
                           user_input: str,
                           context: Dict[str, Any],
                           request_id: str) -> <PERSON><PERSON>[str, Dict[str, Any], str]:
        """
        Handle the quiz_initiate phase - serve first quiz question programmatically
        
        Args:
            session_data: Current session state
            user_input: User's input message
            context: Additional context data
            request_id: Request identifier for logging
            
        Returns:
            Tuple of (response_content, state_updates, next_phase)
        """
        logger.info(f"[{request_id}] 📝 QUIZ_INITIATE PHASE HANDLER")
        
        try:
            # Initialize quiz tracking if not already done
            if not session_data.get('quiz_start_time'):
                quiz_start_time = datetime.now(timezone.utc).isoformat()
                
                # Generate quiz questions if not already generated
                quiz_questions = session_data.get('quiz_questions_generated', [])
                if not quiz_questions:
                    # Generate quiz questions based on lesson content
                    quiz_questions = self._generate_quiz_questions(session_data, context, request_id)
                    logger.info(f"[{request_id}] 📝 Generated {len(quiz_questions)} quiz questions")
                
                # Prepare state updates
                state_updates = {
                    'quiz_start_time': quiz_start_time,
                    'quiz_interactions': 0,
                    'quiz_questions_generated': quiz_questions,
                    'quiz_answers': [],
                    'current_question_index': 0,
                    'quiz_complete': False,
                    'current_phase': 'quiz_initiate'
                }
                
                # Get the first question
                first_question = self._get_formatted_question(quiz_questions, 0)
                
                # Create response with introduction and first question
                student_name = context.get('student_name', 'Student')
                response_content = f"""# Quiz Time!

Great job with the lesson, {student_name}! Now let's test your understanding with a short quiz.

{first_question}

Please select the best answer."""
                
                # Transition to quiz_questions phase
                return response_content, state_updates, 'quiz_questions'
            
            # If already initialized, transition to quiz_questions phase
            # Get the current question
            quiz_questions = session_data.get('quiz_questions_generated', [])
            current_index = session_data.get('current_question_index', 0)
            current_question = self._get_formatted_question(quiz_questions, current_index)
            
            response_content = f"""# Quiz Time!

{current_question}

Please select the best answer."""
            
            return response_content, {'current_phase': 'quiz_questions'}, 'quiz_questions'
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error in quiz_initiate handler: {e}")
            # Safe fallback - stay in quiz_initiate
            error_response = "I'm preparing your quiz questions. Please wait a moment..."
            return error_response, {'error': str(e)}, 'quiz_initiate'
    
    def handle_quiz_questions(self, 
                            session_data: Dict[str, Any], 
                            user_input: str,
                            context: Dict[str, Any],
                            request_id: str) -> Tuple[str, Dict[str, Any], str]:
        """
        Handle the quiz_questions phase - process answers and serve subsequent questions
        
        Args:
            session_data: Current session state
            user_input: User's input message
            context: Additional context data
            request_id: Request identifier for logging
            
        Returns:
            Tuple of (response_content, state_updates, next_phase)
        """
        logger.info(f"[{request_id}] 📝 QUIZ_QUESTIONS PHASE HANDLER")
        
        try:
            # Get quiz state
            quiz_questions = session_data.get('quiz_questions_generated', [])
            current_index = session_data.get('current_question_index', 0)
            quiz_answers = session_data.get('quiz_answers', [])
            quiz_interactions = session_data.get('quiz_interactions', 0) + 1
            
            if not quiz_questions:
                logger.error(f"[{request_id}] ❌ No quiz questions found in session data")
                # Generate emergency questions
                quiz_questions = self._generate_emergency_questions(context)
                current_index = 0
            
            # Process the user's answer for the current question
            current_question = quiz_questions[current_index] if current_index < len(quiz_questions) else None
            
            if current_question:
                # Record the answer
                answer_record = {
                    'question_index': current_index,
                    'question_text': current_question.get('question', ''),
                    'user_answer': user_input,
                    'correct_answer': current_question.get('correct_answer', ''),
                    'options': current_question.get('options', []),
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
                
                # Evaluate the answer
                is_correct = self._evaluate_answer(user_input, current_question, request_id)
                answer_record['is_correct'] = is_correct
                
                # Add to answers list
                quiz_answers.append(answer_record)
                
                # Prepare feedback
                if is_correct:
                    feedback = "✅ Correct! Well done."
                else:
                    correct_answer = current_question.get('correct_answer', '')
                    feedback = f"❌ Not quite. The correct answer is: {correct_answer}"
                
                # Move to the next question
                next_index = current_index + 1
                
                # Check if we've reached the end of the quiz
                if next_index >= len(quiz_questions) or next_index >= self.quiz_completion_threshold:
                    # Quiz is complete, transition to quiz_results
                    quiz_complete = True
                    next_phase = 'quiz_results'
                    
                    # Calculate quiz score
                    correct_answers = sum(1 for answer in quiz_answers if answer.get('is_correct', False))
                    total_questions = len(quiz_answers)
                    score_percentage = (correct_answers / total_questions) * 100 if total_questions > 0 else 0
                    
                    # Prepare quiz results response
                    response_content = f"""# Quiz Complete!

{feedback}

You've completed the quiz! Let's see how you did:

- **Score**: {correct_answers}/{total_questions} ({score_percentage:.1f}%)
- **Questions Answered**: {total_questions}
- **Correct Answers**: {correct_answers}

The system will now generate your final lesson report."""
                    
                    # Prepare state updates
                    state_updates = {
                        'quiz_interactions': quiz_interactions,
                        'quiz_answers': quiz_answers,
                        'current_question_index': next_index,
                        'quiz_complete': quiz_complete,
                        'quiz_score': score_percentage,
                        'quiz_correct_answers': correct_answers,
                        'quiz_total_questions': total_questions,
                        'current_phase': next_phase
                    }
                    
                    return response_content, state_updates, next_phase
                
                else:
                    # Continue with the next question
                    next_question = self._get_formatted_question(quiz_questions, next_index)
                    
                    # Prepare response with feedback and next question
                    response_content = f"""# Question {next_index + 1}

{feedback}

{next_question}

Please select the best answer."""
                    
                    # Prepare state updates
                    state_updates = {
                        'quiz_interactions': quiz_interactions,
                        'quiz_answers': quiz_answers,
                        'current_question_index': next_index,
                        'current_phase': 'quiz_questions'
                    }
                    
                    return response_content, state_updates, 'quiz_questions'
            
            else:
                # No current question found, this is an error state
                logger.error(f"[{request_id}] ❌ Current question index {current_index} out of bounds for quiz with {len(quiz_questions)} questions")
                
                # Generate an emergency question
                emergency_question = self._generate_emergency_question(context)
                
                response_content = f"""# Quiz Question

I apologize for the confusion. Let's continue with this question:

{emergency_question.get('question', 'What is the main topic of this lesson?')}

{self._format_options(emergency_question.get('options', ['A. Option 1', 'B. Option 2', 'C. Option 3', 'D. Option 4']))}

Please select the best answer."""
                
                # Reset the quiz state
                state_updates = {
                    'quiz_interactions': quiz_interactions,
                    'current_question_index': 0,
                    'quiz_questions_generated': [emergency_question],
                    'current_phase': 'quiz_questions'
                }
                
                return response_content, state_updates, 'quiz_questions'
                
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error in quiz_questions handler: {e}")
            # Safe fallback - stay in quiz_questions with an error message
            error_response = "I'm having trouble processing your answer. Let's try to continue with the quiz. Could you please provide your answer again?"
            return error_response, {'error': str(e), 'current_phase': 'quiz_questions'}, 'quiz_questions'
    
    def _generate_quiz_questions(self, 
                               session_data: Dict[str, Any], 
                               context: Dict[str, Any],
                               request_id: str) -> List[Dict[str, Any]]:
        """
        Generate quiz questions based on lesson content
        
        Args:
            session_data: Current session state
            context: Additional context data
            request_id: Request identifier for logging
            
        Returns:
            List of quiz question dictionaries
        """
        try:
            # Extract lesson content
            lesson_content = context.get('instructional_content', '')
            lesson_topic = context.get('topic', '')
            learning_objectives = context.get('learning_objectives', [])
            teaching_level = session_data.get('assigned_level_for_teaching', 5)
            
            # Check if we already have quiz questions in the session
            existing_questions = session_data.get('quiz_questions_generated', [])
            if existing_questions:
                logger.info(f"[{request_id}] 📝 Using {len(existing_questions)} existing quiz questions")
                return existing_questions
            
            # Check if we have pre-generated questions in the context
            pregenerated_questions = context.get('quiz_questions', [])
            if pregenerated_questions:
                logger.info(f"[{request_id}] 📝 Using {len(pregenerated_questions)} pre-generated quiz questions")
                return pregenerated_questions
            
            # If we have a generate_curriculum_aligned_quiz_questions function, use it
            if 'generate_curriculum_aligned_quiz_questions' in globals():
                try:
                    from generate_curriculum_aligned_quiz_questions import generate_curriculum_aligned_quiz_questions
                    quiz_questions = generate_curriculum_aligned_quiz_questions(
                        lesson_content, lesson_topic, learning_objectives, teaching_level
                    )
                    if quiz_questions:
                        logger.info(f"[{request_id}] 📝 Generated {len(quiz_questions)} curriculum-aligned quiz questions")
                        return quiz_questions
                except Exception as quiz_gen_error:
                    logger.error(f"[{request_id}] ❌ Error generating curriculum-aligned quiz questions: {quiz_gen_error}")
            
            # Fallback to generating basic questions
            logger.info(f"[{request_id}] 📝 Generating basic quiz questions")
            return self._generate_basic_questions(lesson_content, lesson_topic, learning_objectives, teaching_level)
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error generating quiz questions: {e}")
            # Return emergency questions
            return self._generate_emergency_questions(context)
    
    def _generate_basic_questions(self, 
                                lesson_content: str, 
                                lesson_topic: str,
                                learning_objectives: List[str],
                                teaching_level: int) -> List[Dict[str, Any]]:
        """
        Generate basic quiz questions when no other method is available
        
        Args:
            lesson_content: Lesson content text
            lesson_topic: Lesson topic
            learning_objectives: List of learning objectives
            teaching_level: Teaching level (1-10)
            
        Returns:
            List of quiz question dictionaries
        """
        # Create a set of generic questions based on the topic
        questions = []
        
        # Use learning objectives to create questions
        for i, objective in enumerate(learning_objectives[:5]):  # Use up to 5 objectives
            question = {
                'question': f"Based on the lesson about {lesson_topic}, which statement best relates to the objective: '{objective}'?",
                'options': [
                    f"A. This is the correct answer about {objective}",
                    f"B. This is an incorrect answer about {objective}",
                    f"C. This is another incorrect answer about {objective}",
                    f"D. This is a distractor answer about {lesson_topic}"
                ],
                'correct_answer': f"A. This is the correct answer about {objective}",
                'explanation': f"The correct answer relates directly to the learning objective about {objective}."
            }
            questions.append(question)
        
        # Add generic questions if we don't have enough
        generic_questions = [
            {
                'question': f"What is the main topic of this lesson?",
                'options': [
                    f"A. {lesson_topic}",
                    "B. A related but incorrect topic",
                    "C. An unrelated topic",
                    "D. None of the above"
                ],
                'correct_answer': f"A. {lesson_topic}",
                'explanation': f"The lesson was specifically about {lesson_topic}."
            },
            {
                'question': f"Which of the following best summarizes the key point about {lesson_topic}?",
                'options': [
                    "A. A correct summary statement",
                    "B. An incorrect summary statement",
                    "C. A statement unrelated to the topic",
                    "D. A partially correct but misleading statement"
                ],
                'correct_answer': "A. A correct summary statement",
                'explanation': "This option correctly summarizes the key point of the lesson."
            }
        ]
        
        # Add generic questions if needed
        while len(questions) < 5:
            questions.append(generic_questions[len(questions) % len(generic_questions)])
        
        return questions
    
    def _generate_emergency_questions(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate emergency quiz questions when other methods fail
        
        Args:
            context: Additional context data
            
        Returns:
            List of emergency quiz question dictionaries
        """
        # Extract whatever topic information we can
        topic = context.get('topic', 'the lesson topic')
        
        # Create a set of very generic questions
        questions = [
            {
                'question': f"What was the main focus of the lesson about {topic}?",
                'options': [
                    f"A. Understanding key concepts of {topic}",
                    f"B. Historical background of {topic}",
                    f"C. Practical applications of {topic}",
                    f"D. Future developments in {topic}"
                ],
                'correct_answer': f"A. Understanding key concepts of {topic}",
                'explanation': f"The lesson primarily focused on understanding the key concepts of {topic}."
            },
            {
                'question': "Which of the following best describes what you learned?",
                'options': [
                    "A. New concepts and their applications",
                    "B. Only theoretical background",
                    "C. Only historical context",
                    "D. None of the above"
                ],
                'correct_answer': "A. New concepts and their applications",
                'explanation': "The lesson covered both concepts and their practical applications."
            },
            {
                'question': "How would you rate your understanding of the material?",
                'options': [
                    "A. I understand it well",
                    "B. I understand some parts",
                    "C. I need more clarification",
                    "D. I didn't understand it"
                ],
                'correct_answer': "A. I understand it well",
                'explanation': "This is a self-assessment question to gauge your confidence in the material."
            },
            {
                'question': "What would help you understand this topic better?",
                'options': [
                    "A. More examples",
                    "B. More practice problems",
                    "C. More detailed explanations",
                    "D. Visual aids and diagrams"
                ],
                'correct_answer': "A. More examples",
                'explanation': "This is a preference question to help improve future lessons."
            },
            {
                'question': "Which aspect of the lesson did you find most valuable?",
                'options': [
                    "A. The explanations of concepts",
                    "B. The examples provided",
                    "C. The interactive elements",
                    "D. The connections to real-world applications"
                ],
                'correct_answer': "A. The explanations of concepts",
                'explanation': "This is a feedback question to understand what aspects of the lesson were most helpful."
            }
        ]
        
        return questions
    
    def _generate_emergency_question(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a single emergency question
        
        Args:
            context: Additional context data
            
        Returns:
            Emergency quiz question dictionary
        """
        # Extract whatever topic information we can
        topic = context.get('topic', 'the lesson topic')
        
        return {
            'question': f"What was the main focus of the lesson about {topic}?",
            'options': [
                f"A. Understanding key concepts of {topic}",
                f"B. Historical background of {topic}",
                f"C. Practical applications of {topic}",
                f"D. Future developments in {topic}"
            ],
            'correct_answer': f"A. Understanding key concepts of {topic}",
            'explanation': f"The lesson primarily focused on understanding the key concepts of {topic}."
        }
    
    def _get_formatted_question(self, quiz_questions: List[Dict[str, Any]], index: int) -> str:
        """
        Get a formatted question string for the given index
        
        Args:
            quiz_questions: List of quiz questions
            index: Question index
            
        Returns:
            Formatted question string
        """
        if not quiz_questions or index >= len(quiz_questions):
            return "No question available."
        
        question = quiz_questions[index]
        question_text = question.get('question', 'Question not available')
        options = question.get('options', [])
        
        formatted_question = f"**Question {index + 1}**: {question_text}\n\n"
        formatted_question += self._format_options(options)
        
        return formatted_question
    
    def _format_options(self, options: List[str]) -> str:
        """
        Format options as a string
        
        Args:
            options: List of option strings
            
        Returns:
            Formatted options string
        """
        if not options:
            return "No options available."
        
        return "\n".join(options)
    
    def _evaluate_answer(self, user_input: str, question: Dict[str, Any], request_id: str) -> bool:
        """
        Evaluate if the user's answer is correct
        
        Args:
            user_input: User's input message
            question: Question dictionary
            request_id: Request identifier for logging
            
        Returns:
            True if the answer is correct, False otherwise
        """
        try:
            correct_answer = question.get('correct_answer', '')
            if not correct_answer:
                logger.warning(f"[{request_id}] ⚠️ No correct answer found for question")
                return False
            
            # Normalize user input and correct answer
            user_input_lower = user_input.lower().strip()
            correct_answer_lower = correct_answer.lower().strip()
            
            # Check for exact match
            if user_input_lower == correct_answer_lower:
                return True
            
            # Check for option letter (A, B, C, D)
            if len(user_input_lower) == 1 and user_input_lower in ['a', 'b', 'c', 'd']:
                # Extract the letter from the correct answer
                correct_letter = correct_answer_lower[0] if correct_answer_lower and correct_answer_lower[0] in ['a', 'b', 'c', 'd'] else None
                if correct_letter and user_input_lower == correct_letter:
                    return True
            
            # Check if user input contains the correct answer
            if correct_answer_lower in user_input_lower:
                return True
            
            # Check if user input contains the correct option letter
            options = question.get('options', [])
            for i, option in enumerate(options):
                option_letter = option[0].lower() if option and option[0].lower() in ['a', 'b', 'c', 'd'] else None
                if option_letter and option_letter == user_input_lower:
                    correct_option_letter = correct_answer_lower[0] if correct_answer_lower and correct_answer_lower[0] in ['a', 'b', 'c', 'd'] else None
                    if correct_option_letter and option_letter == correct_option_letter:
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"[{request_id}] ❌ Error evaluating answer: {e}")
            return False

# Global instance for use throughout the application
quiz_phase_handler = QuizPhaseHandler()