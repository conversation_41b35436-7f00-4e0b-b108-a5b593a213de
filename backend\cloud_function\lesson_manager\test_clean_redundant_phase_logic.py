#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Clean Redundant Phase Logic

This script tests the implementation of task 6.2 from the unified lesson flow refactor spec:
- Remove duplicate or conflicting phase transition code
- Consolidate all phase handling into the main state machine
- Eliminate any code paths that bypass the unified pathway
"""

import os
import sys
import logging
import unittest
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Constants
MAIN_PY_PATH = "backend/cloud_function/lesson_manager/main.py"
PHASE_TRANSITION_INTEGRITY_PATH = "backend/cloud_function/lesson_manager/phase_transition_integrity.py"
TEACHING_RULES_PATH = "backend/cloud_function/lesson_manager/teaching_rules.py"

def read_file(file_path: str) -> str:
    """Read a file and return its contents."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return ""

class TestCleanRedundantPhaseLogic(unittest.TestCase):
    """Test cases for clean_redundant_phase_logic.py"""
    
    def test_legacy_phase_validation_removed(self):
        """Test that legacy phase validation functions are removed."""
        content = read_file(MAIN_PY_PATH)
        self.assertIsNotNone(content)
        
        # Check for legacy functions that should be removed or commented out
        legacy_functions = [
            r'def validate_diagnostic_phase_sequence',
            r'def is_forward_transition_validated',
            r'def determine_diagnostic_phase',
            r'def get_state_transition_rules'
        ]
        
        for func in legacy_functions:
            # Either the function should be completely removed or commented out
            matches = re.findall(func, content)
            commented_matches = re.findall(r'# REMOVED: Legacy phase validation function', content)
            
            self.assertTrue(
                len(matches) == 0 or len(commented_matches) > 0,
                f"Legacy function {func} should be removed or commented out"
            )
    
    def test_consolidated_phase_handling(self):
        """Test that phase handling is consolidated into the main state machine."""
        content = read_file(MAIN_PY_PATH)
        self.assertIsNotNone(content)
        
        # Check for the consolidated phase handling function
        self.assertTrue(
            'def handle_phase_transition(' in content,
            "Consolidated phase handling function should be present"
        )
        
        # Check that it uses the phase transition integrity manager
        self.assertTrue(
            'phase_transition_manager.validate_phase_transition(' in content,
            "Consolidated function should use phase transition manager"
        )
    
    def test_phase_transition_calls_updated(self):
        """Test that phase transition calls are updated to use the consolidated handler."""
        content = read_file(MAIN_PY_PATH)
        self.assertIsNotNone(content)
        
        # Check for updated phase transition calls
        self.assertTrue(
            'handle_phase_transition(' in content,
            "Phase transition calls should use the consolidated handler"
        )
        
        # Check that old validation methods are not used
        self.assertFalse(
            'if not log_state_transition(' in content,
            "Old phase transition validation should not be used"
        )
        
        self.assertFalse(
            'if not self._is_valid_phase_transition(' in content,
            "Old phase transition validation should not be used"
        )
    
    def test_backward_transition_checks_removed(self):
        """Test that redundant backward transition checks are removed."""
        content = read_file(PHASE_TRANSITION_INTEGRITY_PATH)
        self.assertIsNotNone(content)
        
        # Check for removed backward transition checks
        self.assertTrue(
            '# REMOVED: Redundant backward transition check' in content,
            "Redundant backward transition checks should be removed"
        )
    
    def test_phase_transition_integrity_updated(self):
        """Test that the phase transition integrity module is updated to use the unified pathway."""
        content = read_file(PHASE_TRANSITION_INTEGRITY_PATH)
        self.assertIsNotNone(content)
        
        # Check for updated _is_valid_basic_transition method
        self.assertTrue(
            'def _is_valid_basic_transition(self, from_phase: str, to_phase: str)' in content,
            "_is_valid_basic_transition method should be present"
        )
        
        # Check that it uses the unified pathway
        self.assertTrue(
            'CRITICAL: Allow same-phase transitions' in content,
            "_is_valid_basic_transition should allow same-phase transitions"
        )
        
        self.assertTrue(
            'Check explicit transitions from the unified pathway' in content,
            "_is_valid_basic_transition should check explicit transitions"
        )

if __name__ == "__main__":
    unittest.main()