#!/usr/bin/env python3
import unittest

class TestSimple(unittest.TestCase):
    def test_basic(self):
        """Basic test to verify test framework is working"""
        self.assertTrue(True)
        print("Basic test passed!")
    
    def test_math(self):
        """Test basic math operations"""
        self.assertEqual(2 + 2, 4)
        print("Math test passed!")

if __name__ == '__main__':
    print("Running simple unit tests...")
    unittest.main(verbosity=2)