diff --git a/frontend/lesson-platform/src/app/classroom/ClassroomContent.tsx b/frontend/lesson-platform/src/app/classroom/ClassroomContent.tsx
index 1234567..abcdef0 100644
--- a/frontend/lesson-platform/src/app/classroom/ClassroomContent.tsx
+++ b/frontend/lesson-platform/src/app/classroom/ClassroomContent.tsx
@@ -5,6 +5,7 @@ import { useRouter } from 'next/navigation'; 
 import TutorChat from '@/components/lesson-components/TutorChat';
 import { useLessonTimer } from '@/hooks/useLessonTimer';
 import type { ChatMessage } from '@/components/lesson-components/LessonChat';
+import { useQuizResultsTrigger } from '@/hooks/useQuizResultsTrigger';
 import { useSession } from '@/hooks/useSessionSimple'; // Using simplified session hook
 import useInteractionLogger from '@/hooks/use-interaction-logger';
 import { useToast } from '@/app/providers/ClientToastWrapper';
@@ -107,6 +108,15 @@ const ClassroomContent = ({
     // Derive timer status for progress and phase indicators
     const timerStatus = getTimerStatus();
 
+    // Define handleAiInteraction function for the useQuizResultsTrigger hook
+    const handleAiInteraction = useCallback((message: string, isSystemMessage: boolean = false, sessionId: string) => {
+        if (handleAiInteractionRef.current) {
+            handleAiInteractionRef.current(message, isSystemMessage, sessionId);
+        }
+    }, []);
+
+    // Use the quiz results trigger hook to automatically trigger final report generation
+    useQuizResultsTrigger(currentLessonPhase, handleAiInteraction, sessionIdFromUrlProp || backendSessionId || null);
+
     // Automatically start the lesson timer when the page is ready
     useEffect(() => {
         if (!isPageLoading && isReady && !lessonStartTime) {
@@ -396,6 +406,9 @@ const ClassroomContent = ({
     // Store the handleAiInteraction function in a ref so it can be accessed by other hooks
     useEffect(() => {
         handleAiInteractionRef.current = handleAiInteractionInternal;
+        
+        // Log that the handler is ready
+        console.log('🔄 AI interaction handler is ready');
     }, [handleAiInteractionInternal]);
 
     // Scroll to bottom when chat history changes