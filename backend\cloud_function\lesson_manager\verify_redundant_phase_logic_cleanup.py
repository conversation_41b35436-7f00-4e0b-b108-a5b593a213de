#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Verify Redundant Phase Logic Cleanup

This script verifies that task 6.2 from the unified lesson flow refactor spec has been
successfully implemented:
- Remove duplicate or conflicting phase transition code
- Consolidate all phase handling into the main state machine
- Eliminate any code paths that bypass the unified pathway
"""

import os
import sys
import logging
import json
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Constants
MAIN_PY_PATH = "backend/cloud_function/lesson_manager/main.py"
PHASE_TRANSITION_INTEGRITY_PATH = "backend/cloud_function/lesson_manager/phase_transition_integrity.py"
TEACHING_RULES_PATH = "backend/cloud_function/lesson_manager/teaching_rules.py"

def read_file(file_path: str) -> str:
    """Read a file and return its contents."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return ""

def verify_legacy_functions_removed(content: str) -> Dict[str, Any]:
    """Verify that legacy phase validation functions are removed."""
    legacy_functions = [
        'validate_diagnostic_phase_sequence',
        'is_forward_transition_validated',
        'determine_diagnostic_phase',
        'get_state_transition_rules'
    ]
    
    results = {}
    for func in legacy_functions:
        # Check if function is completely removed or commented out
        active_func = f"def {func}"
        commented_func = f"# REMOVED: Legacy phase validation function"
        
        if active_func not in content or commented_func in content:
            results[func] = {
                "status": "removed",
                "message": f"Legacy function '{func}' has been removed or commented out"
            }
        else:
            results[func] = {
                "status": "present",
                "message": f"Legacy function '{func}' is still present and active"
            }
    
    return results

def verify_consolidated_phase_handling(content: str) -> Dict[str, Any]:
    """Verify that phase handling is consolidated into the main state machine."""
    consolidated_function = "handle_phase_transition"
    
    if f"def {consolidated_function}" in content:
        # Check if it uses the phase transition integrity manager
        if "phase_transition_manager.validate_phase_transition" in content:
            return {
                "status": "implemented",
                "message": "Consolidated phase handling function is implemented and uses the phase transition integrity manager"
            }
        else:
            return {
                "status": "partial",
                "message": "Consolidated phase handling function is implemented but does not use the phase transition integrity manager"
            }
    else:
        return {
            "status": "missing",
            "message": "Consolidated phase handling function is not implemented"
        }

def verify_phase_transition_calls(content: str) -> Dict[str, Any]:
    """Verify that phase transition calls are updated to use the consolidated handler."""
    old_calls = [
        "log_state_transition(current_phase, new_phase",
        "self._is_valid_phase_transition(current_state.get('current_phase'), new_phase"
    ]
    
    new_call = "handle_phase_transition("
    
    results = {
        "old_calls_removed": True,
        "new_calls_present": False,
        "details": []
    }
    
    # Check if old calls are removed
    for call in old_calls:
        if call in content:
            results["old_calls_removed"] = False
            results["details"].append(f"Old call '{call}' is still present")
    
    # Check if new calls are present
    if new_call in content:
        results["new_calls_present"] = True
        results["details"].append(f"New call '{new_call}' is present")
    
    # Overall status
    if results["old_calls_removed"] and results["new_calls_present"]:
        results["status"] = "updated"
        results["message"] = "Phase transition calls have been updated to use the consolidated handler"
    elif results["new_calls_present"]:
        results["status"] = "partial"
        results["message"] = "New phase transition calls are present but some old calls remain"
    else:
        results["status"] = "not_updated"
        results["message"] = "Phase transition calls have not been updated"
    
    return results

def verify_backward_transition_checks(content: str) -> Dict[str, Any]:
    """Verify that redundant backward transition checks are removed."""
    backward_checks = [
        "# Check for backward progression with more specific rules",
        "# Check specific completion-based rules"
    ]
    
    removal_comment = "# REMOVED: Redundant backward transition check"
    
    results = {
        "checks_removed": True,
        "removal_comment_present": False,
        "details": []
    }
    
    # Check if backward checks are removed
    for check in backward_checks:
        if check in content and removal_comment not in content:
            results["checks_removed"] = False
            results["details"].append(f"Backward check '{check}' is still present")
    
    # Check if removal comment is present
    if removal_comment in content:
        results["removal_comment_present"] = True
        results["details"].append(f"Removal comment '{removal_comment}' is present")
    
    # Overall status
    if results["checks_removed"] and results["removal_comment_present"]:
        results["status"] = "removed"
        results["message"] = "Redundant backward transition checks have been removed"
    elif results["checks_removed"]:
        results["status"] = "removed_without_comment"
        results["message"] = "Redundant backward transition checks have been removed without comment"
    else:
        results["status"] = "not_removed"
        results["message"] = "Redundant backward transition checks have not been removed"
    
    return results

def verify_phase_transition_integrity(content: str) -> Dict[str, Any]:
    """Verify that the phase transition integrity module is updated to use the unified pathway."""
    updated_method = "_is_valid_basic_transition"
    
    unified_pathway_checks = [
        "CRITICAL: Allow same-phase transitions",
        "Check explicit transitions from the unified pathway"
    ]
    
    results = {
        "method_updated": False,
        "unified_pathway_checks": [],
        "details": []
    }
    
    # Check if method is updated
    if f"def {updated_method}" in content:
        results["method_updated"] = True
        results["details"].append(f"Method '{updated_method}' is present")
    
    # Check for unified pathway checks
    for check in unified_pathway_checks:
        if check in content:
            results["unified_pathway_checks"].append(check)
            results["details"].append(f"Unified pathway check '{check}' is present")
    
    # Overall status
    if results["method_updated"] and len(results["unified_pathway_checks"]) == len(unified_pathway_checks):
        results["status"] = "updated"
        results["message"] = "Phase transition integrity module has been updated to use the unified pathway"
    elif results["method_updated"]:
        results["status"] = "partial"
        results["message"] = "Phase transition integrity module has been partially updated"
    else:
        results["status"] = "not_updated"
        results["message"] = "Phase transition integrity module has not been updated"
    
    return results

def main():
    """Main function to verify redundant phase logic cleanup."""
    logger.info("Starting verification of redundant phase logic cleanup")
    
    verification_results = {
        "legacy_functions_removed": {},
        "consolidated_phase_handling": {},
        "phase_transition_calls": {},
        "backward_transition_checks": {},
        "phase_transition_integrity": {},
        "overall_status": "unknown"
    }
    
    # Read the main.py file
    main_py_content = read_file(MAIN_PY_PATH)
    if not main_py_content:
        logger.error(f"Could not read {MAIN_PY_PATH}")
        verification_results["overall_status"] = "error"
        return verification_results
    
    # Read the phase_transition_integrity.py file
    phase_transition_integrity_content = read_file(PHASE_TRANSITION_INTEGRITY_PATH)
    if not phase_transition_integrity_content:
        logger.error(f"Could not read {PHASE_TRANSITION_INTEGRITY_PATH}")
        verification_results["overall_status"] = "error"
        return verification_results
    
    # Verify legacy functions removed
    logger.info("Verifying legacy functions removed")
    verification_results["legacy_functions_removed"] = verify_legacy_functions_removed(main_py_content)
    
    # Verify consolidated phase handling
    logger.info("Verifying consolidated phase handling")
    verification_results["consolidated_phase_handling"] = verify_consolidated_phase_handling(main_py_content)
    
    # Verify phase transition calls
    logger.info("Verifying phase transition calls")
    verification_results["phase_transition_calls"] = verify_phase_transition_calls(main_py_content)
    
    # Verify backward transition checks
    logger.info("Verifying backward transition checks")
    verification_results["backward_transition_checks"] = verify_backward_transition_checks(phase_transition_integrity_content)
    
    # Verify phase transition integrity
    logger.info("Verifying phase transition integrity")
    verification_results["phase_transition_integrity"] = verify_phase_transition_integrity(phase_transition_integrity_content)
    
    # Determine overall status
    statuses = [
        all(item["status"] == "removed" for item in verification_results["legacy_functions_removed"].values()),
        verification_results["consolidated_phase_handling"]["status"] == "implemented",
        verification_results["phase_transition_calls"]["status"] == "updated",
        verification_results["backward_transition_checks"]["status"] in ["removed", "removed_without_comment"],
        verification_results["phase_transition_integrity"]["status"] == "updated"
    ]
    
    if all(statuses):
        verification_results["overall_status"] = "success"
        logger.info("Verification successful: All redundant phase logic has been cleaned up")
    elif any(statuses):
        verification_results["overall_status"] = "partial"
        logger.warning("Verification partial: Some redundant phase logic has been cleaned up")
    else:
        verification_results["overall_status"] = "failure"
        logger.error("Verification failed: Redundant phase logic has not been cleaned up")
    
    # Print verification results
    logger.info("Verification results:")
    logger.info(json.dumps(verification_results, indent=2))
    
    return verification_results

if __name__ == "__main__":
    main()