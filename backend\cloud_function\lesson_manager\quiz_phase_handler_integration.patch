diff --git a/backend/cloud_function/lesson_manager/main.py b/backend/cloud_function/lesson_manager/main.py
index xxxxxxx..xxxxxxx 100644
--- a/backend/cloud_function/lesson_manager/main.py
+++ b/backend/cloud_function/lesson_manager/main.py
@@ -127,6 +127,7 @@ from teaching_phase_enhancement import teaching_phase_manager
 from teaching_rules import (
     teaching_rules_engine,
     validate_teaching_completion,
-    enforce_phase_consistency
+    enforce_phase_consistency,
+    get_teaching_progress
 )
+from quiz_phase_handler import quiz_phase_handler
 
 # Performance optimization imports removed - caused context override issues
 
@@ -7602,123 +7603,25 @@ async def handle_teaching_phase(user_query, chat_history, context, session_state_
 
 async def handle_quiz_initiate_phase(user_query, chat_history, context, session_state_data, request_id):
     """
-    Handle quiz_initiate phase - serve first quiz question programmatically.
+    Handle quiz_initiate phase - serve first quiz question programmatically
     
     Args:
         user_query: User's input message
         chat_history: Chat history
         context: Additional context data
         session_state_data: Current session state
-        request_id: Request identifier
+        request_id: Request identifier for logging
         
     Returns:
         Tuple of (enhanced_content, state_updates, raw_state_block_str)
     """
-    logger.info(f"[{request_id}] 📝 QUIZ_INITIATE PHASE HANDLER")
-    
-    try:
-        # Initialize quiz tracking if not already done
-        if not session_state_data.get('quiz_start_time'):
-            quiz_start_time = datetime.now(timezone.utc).isoformat()
-            
-            # Generate quiz questions if not already generated
-            quiz_questions = session_state_data.get('quiz_questions_generated', [])
-            if not quiz_questions:
-                # Generate quiz questions based on lesson content
-                quiz_questions = generate_quiz_questions(session_state_data, context, request_id)
-                logger.info(f"[{request_id}] 📝 Generated {len(quiz_questions)} quiz questions")
-            
-            # Prepare state updates
-            state_updates = {
-                'quiz_start_time': quiz_start_time,
-                'quiz_interactions': 0,
-                'quiz_questions_generated': quiz_questions,
-                'quiz_answers': [],
-                'current_question_index': 0,
-                'quiz_complete': False,
-                'current_phase': 'quiz_questions'  # Transition to quiz_questions
-            }
-            
-            # Get the first question
-            first_question = get_formatted_question(quiz_questions, 0)
-            
-            # Create response with introduction and first question
-            student_name = context.get('student_name', 'Student')
-            enhanced_content = f"""# Quiz Time!
-
-Great job with the lesson, {student_name}! Now let's test your understanding with a short quiz.
-
-{first_question}
-
-Please select the best answer."""
-            
-            return enhanced_content, state_updates, json.dumps(state_updates)
-        
-        # If already initialized, transition to quiz_questions phase
-        # Get the current question
-        quiz_questions = session_state_data.get('quiz_questions_generated', [])
-        current_index = session_state_data.get('current_question_index', 0)
-        current_question = get_formatted_question(quiz_questions, current_index)
-        
-        enhanced_content = f"""# Quiz Time!
-
-{current_question}
-
-Please select the best answer."""
-        
-        state_updates = {'current_phase': 'quiz_questions'}
-        
-        return enhanced_content, state_updates, json.dumps(state_updates)
-        
-    except Exception as e:
-        logger.error(f"[{request_id}] ❌ Error in quiz_initiate handler: {e}")
-        # Safe fallback - stay in quiz_initiate
-        enhanced_content = "I'm preparing your quiz questions. Please wait a moment..."
-        state_updates = {'error': str(e)}
-        return enhanced_content, state_updates, json.dumps(state_updates)
+    # Use the quiz phase handler to handle quiz_initiate phase
+    response_content, state_updates, next_phase = quiz_phase_handler.handle_quiz_initiate(
+        session_state_data, user_query, context, request_id
+    )
+    
+    return response_content, state_updates, json.dumps(state_updates)
 
 async def handle_quiz_questions_phase(user_query, chat_history, context, session_state_data, request_id):
     """
-    Handle quiz_questions phase - process answers and serve subsequent questions.
+    Handle quiz_questions phase - process answers and serve subsequent questions
     
     Args:
         user_query: User's input message
         chat_history: Chat history
         context: Additional context data
         session_state_data: Current session state
-        request_id: Request identifier
+        request_id: Request identifier for logging
         
     Returns:
         Tuple of (enhanced_content, state_updates, raw_state_block_str)
     """
-    logger.info(f"[{request_id}] 📝 QUIZ_QUESTIONS PHASE HANDLER")
-    
-    try:
-        # Get quiz state
-        quiz_questions = session_state_data.get('quiz_questions_generated', [])
-        current_index = session_state_data.get('current_question_index', 0)
-        quiz_answers = session_state_data.get('quiz_answers', [])
-        quiz_interactions = session_state_data.get('quiz_interactions', 0) + 1
-        
-        # Process the user's answer for the current question
-        # ... (implementation details)
-        
-        # For now, just transition to quiz_results after one question
-        # This will be replaced with proper quiz flow logic
-        enhanced_content = """# Quiz Complete!
-
-Thank you for completing the quiz. Let's see how you did.
-
-The system will now generate your final lesson report."""
-        
-        state_updates = {
-            'quiz_interactions': quiz_interactions,
-            'quiz_complete': True,
-            'current_phase': 'quiz_results'
-        }
-        
-        return enhanced_content, state_updates, json.dumps(state_updates)
-        
-    except Exception as e:
-        logger.error(f"[{request_id}] ❌ Error in quiz_questions handler: {e}")
-        # Safe fallback - stay in quiz_questions
-        enhanced_content = "I'm having trouble processing your answer. Let's try to continue with the quiz."
-        state_updates = {'error': str(e), 'current_phase': 'quiz_questions'}
-        return enhanced_content, state_updates, json.dumps(state_updates)
+    # Use the quiz phase handler to handle quiz_questions phase
+    response_content, state_updates, next_phase = quiz_phase_handler.handle_quiz_questions(
+        session_state_data, user_query, context, request_id
+    )
+    
+    return response_content, state_updates, json.dumps(state_updates)