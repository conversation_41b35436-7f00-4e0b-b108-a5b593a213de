#!/usr/bin/env python3
"""
Comprehensive End-to-End Lesson Test with Real Student Credentials
================================================================

This test validates the complete lesson flow using the live server with:
- Student: andrea_ugono_33305 (password: test123)
- Real lesson: P5-ENT-046 (Primary 5 Entrepreneurship)
- Complete 9-phase flow validation
- Performance and quality metrics
"""

import requests
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'comprehensive_lesson_test_{int(time.time())}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveLessonTest:
    def __init__(self):
        self.base_url = "http://127.0.0.1:5000"
        self.student_id = "andrea_ugono_33305"
        self.student_password = "test123"
        self.lesson_ref = "P5-ENT-046"
        self.grade = "Primary 5"
        self.subject = "Entrepreneurship"
        self.session_id = f"test_session_{int(time.time())}"
        
        self.test_results = {
            'start_time': datetime.now().isoformat(),
            'student_id': self.student_id,
            'lesson_ref': self.lesson_ref,
            'interactions': [],
            'phase_transitions': [],
            'performance_metrics': {},
            'errors': [],
            'success': False
        }
        
        logger.info("=" * 80)
        logger.info("🚀 COMPREHENSIVE LESSON TEST STARTING")
        logger.info(f"Student: {self.student_id}")
        logger.info(f"Lesson: {self.lesson_ref}")
        logger.info(f"Session: {self.session_id}")
        logger.info("=" * 80)

    def test_server_health(self) -> bool:
        """Test server health and connectivity"""
        try:
            logger.info("🏥 Testing server health...")
            response = requests.get(f"{self.base_url}/api/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info("✅ Server health check passed")
                logger.info(f"   Firebase ready: {health_data.get('firebase_ready')}")
                logger.info(f"   Status: {health_data.get('status')}")
                return True
            else:
                logger.error(f"❌ Health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Health check error: {e}")
            return False

    def send_lesson_interaction(self, message: str, expected_phase: str = None) -> Dict[str, Any]:
        """Send a lesson interaction and return the response"""
        payload = {
            "message": message,
            "lesson_ref": self.lesson_ref,
            "grade": self.grade,
            "subject": self.subject,
            "session_id": self.session_id,
            "student_id": self.student_id,
            "student_name": "Andrea"
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/enhance-content",
                headers=headers,
                json=payload,
                timeout=30
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                # Log interaction
                interaction = {
                    'timestamp': datetime.now().isoformat(),
                    'message': message[:100] + "..." if len(message) > 100 else message,
                    'response_time': response_time,
                    'phase': data.get('data', {}).get('current_phase'),
                    'success': True
                }
                self.test_results['interactions'].append(interaction)
                
                logger.info(f"✅ Interaction successful ({response_time:.2f}s)")
                logger.info(f"   Phase: {interaction['phase']}")
                logger.info(f"   Response: {data.get('data', {}).get('response', '')[:100]}...")

                # Check for quiz transition
                if data.get('data', {}).get('should_show_quiz'):
                    logger.info("🎯 Quiz transition detected!")
                    return data
                
                return data
            else:
                logger.error(f"❌ Interaction failed: {response.status_code}")
                logger.error(f"   Response: {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"❌ Interaction error: {e}")
            return {}

    def run_diagnostic_phase(self) -> bool:
        """Run the complete diagnostic phase"""
        logger.info("🔍 PHASE 1: Starting Diagnostic Assessment")
        
        # Start the lesson
        response = self.send_lesson_interaction("Hello, I'm ready to start my lesson!")
        if not response:
            return False
            
        current_phase = response.get('lesson_state', {}).get('current_phase')
        logger.info(f"   Initial phase: {current_phase}")
        
        # Diagnostic interactions
        diagnostic_responses = [
            "I know some basic concepts about entrepreneurship",
            "An entrepreneur is someone who starts a business",
            "Businesses need customers to buy their products",
            "Money is important for starting a business",
            "I think planning is important for success"
        ]
        
        for i, response_text in enumerate(diagnostic_responses, 1):
            logger.info(f"   Diagnostic Question {i}/5")
            response = self.send_lesson_interaction(response_text)
            if not response:
                return False
                
            time.sleep(1)  # Small delay between interactions
            
        logger.info("✅ Diagnostic phase completed")
        return True

    def run_teaching_phase(self) -> bool:
        """Run the teaching phase"""
        logger.info("📚 PHASE 2: Teaching Phase")
        
        teaching_interactions = [
            "Tell me more about entrepreneurship",
            "What makes a good entrepreneur?",
            "How do I start a business?",
            "What are some examples of successful businesses?",
            "Can you explain more about business planning?",
            "What skills do entrepreneurs need?",
            "How do entrepreneurs find customers?",
            "What challenges do entrepreneurs face?"
        ]
        
        for i, message in enumerate(teaching_interactions, 1):
            logger.info(f"   Teaching Interaction {i}/{len(teaching_interactions)}")
            response = self.send_lesson_interaction(message)
            if not response:
                return False
                
            # Check if quiz is ready
            if response.get('data', {}).get('should_show_quiz'):
                logger.info("🎯 Quiz transition detected!")
                break
                
            time.sleep(1)
            
        logger.info("✅ Teaching phase completed")
        return True

    def run_quiz_phase(self) -> bool:
        """Run the quiz phase"""
        logger.info("🧪 PHASE 3: Quiz Phase")
        
        # Trigger quiz
        response = self.send_lesson_interaction("I'm ready for the quiz!")
        if not response:
            return False
            
        # Answer quiz questions
        quiz_answers = [
            "A", "B", "C", "A", "B",  # First 5 answers
            "C", "A", "B", "C", "A"   # Next 5 answers
        ]
        
        for i, answer in enumerate(quiz_answers, 1):
            logger.info(f"   Quiz Question {i}/10")
            response = self.send_lesson_interaction(f"My answer is {answer}")
            if not response:
                return False
                
            time.sleep(0.5)
            
        logger.info("✅ Quiz phase completed")
        return True

    def run_completion_phase(self) -> bool:
        """Run the lesson completion phase"""
        logger.info("🎉 PHASE 4: Lesson Completion")
        
        # Final interactions
        completion_messages = [
            "Thank you for the lesson!",
            "I learned a lot about entrepreneurship",
            "This was very helpful"
        ]
        
        for message in completion_messages:
            response = self.send_lesson_interaction(message)
            if not response:
                return False
                
            time.sleep(1)
            
        logger.info("✅ Lesson completion phase finished")
        return True

    def analyze_results(self) -> Dict[str, Any]:
        """Analyze test results and generate report"""
        total_interactions = len(self.test_results['interactions'])
        successful_interactions = sum(1 for i in self.test_results['interactions'] if i['success'])
        
        if total_interactions > 0:
            success_rate = (successful_interactions / total_interactions) * 100
            avg_response_time = sum(i['response_time'] for i in self.test_results['interactions']) / total_interactions
        else:
            success_rate = 0
            avg_response_time = 0
            
        phases_covered = set(i['phase'] for i in self.test_results['interactions'] if i['phase'])
        
        analysis = {
            'total_interactions': total_interactions,
            'successful_interactions': successful_interactions,
            'success_rate': success_rate,
            'average_response_time': avg_response_time,
            'phases_covered': list(phases_covered),
            'phase_count': len(phases_covered),
            'test_duration': (datetime.now() - datetime.fromisoformat(self.test_results['start_time'])).total_seconds()
        }
        
        return analysis

    def run_comprehensive_test(self) -> bool:
        """Run the complete comprehensive test"""
        try:
            # Test server health
            if not self.test_server_health():
                logger.error("❌ Server health check failed")
                return False
                
            # Run all phases
            phases = [
                ("Diagnostic", self.run_diagnostic_phase),
                ("Teaching", self.run_teaching_phase),
                ("Quiz", self.run_quiz_phase),
                ("Completion", self.run_completion_phase)
            ]
            
            for phase_name, phase_func in phases:
                logger.info(f"\n{'='*60}")
                logger.info(f"STARTING {phase_name.upper()} PHASE")
                logger.info(f"{'='*60}")
                
                if not phase_func():
                    logger.error(f"❌ {phase_name} phase failed")
                    return False
                    
            # Analyze results
            analysis = self.analyze_results()
            self.test_results['analysis'] = analysis
            self.test_results['success'] = True
            
            # Generate report
            self.generate_report(analysis)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Test execution error: {e}")
            return False

    def generate_report(self, analysis: Dict[str, Any]):
        """Generate comprehensive test report"""
        logger.info("\n" + "="*80)
        logger.info("📊 COMPREHENSIVE TEST RESULTS")
        logger.info("="*80)
        logger.info(f"✅ Test Status: {'PASSED' if self.test_results['success'] else 'FAILED'}")
        logger.info(f"📈 Success Rate: {analysis['success_rate']:.1f}%")
        logger.info(f"⏱️  Average Response Time: {analysis['average_response_time']:.2f}s")
        logger.info(f"🔄 Total Interactions: {analysis['total_interactions']}")
        logger.info(f"📋 Phases Covered: {analysis['phase_count']}")
        logger.info(f"⏰ Test Duration: {analysis['test_duration']:.1f}s")
        logger.info(f"🎯 Phases: {', '.join(analysis['phases_covered'])}")
        
        # Save detailed report
        report_filename = f"comprehensive_test_report_{int(time.time())}.json"
        with open(report_filename, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
            
        logger.info(f"📄 Detailed report saved: {report_filename}")
        logger.info("="*80)

def main():
    """Main test execution"""
    test = ComprehensiveLessonTest()
    success = test.run_comprehensive_test()
    
    if success:
        logger.info("🎉 COMPREHENSIVE TEST COMPLETED SUCCESSFULLY!")
        return 0
    else:
        logger.error("💥 COMPREHENSIVE TEST FAILED!")
        return 1

if __name__ == "__main__":
    exit(main())
