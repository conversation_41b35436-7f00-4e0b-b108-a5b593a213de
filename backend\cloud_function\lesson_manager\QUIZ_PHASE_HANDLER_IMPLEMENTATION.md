# Quiz Phase Handler Implementation

This document describes the implementation of the quiz phase handlers for the unified lesson flow state machine.

## Overview

The quiz phase handlers manage the `quiz_initiate` and `quiz_questions` phases of the unified lesson flow, and handle the transition to the `quiz_results` phase. These handlers are part of the state machine implementation that enforces a strict lesson flow sequence.

## Key Features

- **Quiz Initiate <PERSON><PERSON>**: Serves the first quiz question programmatically
- **Quiz Questions Handler**: Processes answers and serves subsequent questions
- **Quiz Completion Detection**: Detects when the quiz is complete and transitions to quiz_results phase
- **Answer Evaluation**: Evaluates user answers against correct answers
- **Error Handling**: Provides graceful error handling and recovery mechanisms

## Implementation Details

### Quiz Phase Handler Class

The `QuizPhaseHandler` class provides the core functionality for managing quiz phases:

```python
class QuizPhaseHandler:
    """
    Handles quiz phase processing for the unified lesson flow state machine.
    Manages quiz_initiate and quiz_questions phases with transition to quiz_results.
    """
```

### Quiz Initiate Phase

The `handle_quiz_initiate` method manages the quiz_initiate phase:

1. Initializes quiz tracking data if not already done
2. Generates quiz questions based on lesson content
3. Prepares the first question for the user
4. Transitions to the quiz_questions phase

### Quiz Questions Phase

The `handle_quiz_questions` method manages the quiz_questions phase:

1. Processes the user's answer to the current question
2. Records the answer and evaluates if it's correct
3. Provides feedback on the answer
4. Serves the next question or transitions to quiz_results if the quiz is complete

### Quiz Completion

Quiz completion is detected when:
- All questions have been answered, or
- The maximum number of questions (quiz_completion_threshold) has been reached

Upon completion, the handler:
1. Calculates the quiz score
2. Prepares a summary of quiz results
3. Transitions to the quiz_results phase

### Integration with State Machine

The quiz phase handlers are integrated with the main state machine in `enhance_content_api`:

```python
# Route to appropriate phase handler based on unified pathway
if current_phase == LessonPhase.QUIZ_INITIATE:
    logger.info(f"[{request_id}] 🎯 ROUTING: Quiz initiate phase handler")
    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_quiz_initiate_phase(
        user_query, chat_history, context_for_enhance, session_state_data, request_id
    )
elif current_phase == LessonPhase.QUIZ_QUESTIONS:
    logger.info(f"[{request_id}] 🎯 ROUTING: Quiz questions phase handler")
    enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await handle_quiz_questions_phase(
        user_query, chat_history, context_for_enhance, session_state_data, request_id
    )
```

## Testing

The quiz phase handlers are tested in `test_quiz_phase_handler.py`, which includes tests for:

- Quiz initiation with new and existing sessions
- Processing answers in the quiz_questions phase
- Detecting quiz completion and transitioning to quiz_results
- Error handling and recovery mechanisms
- Answer evaluation logic

## Usage

The quiz phase handlers are used automatically by the state machine when the lesson flow reaches the quiz phases. The handlers are accessed through the global `quiz_phase_handler` instance:

```python
from quiz_phase_handler import quiz_phase_handler

# Use the quiz phase handler to handle quiz_initiate phase
response_content, state_updates, next_phase = quiz_phase_handler.handle_quiz_initiate(
    session_state_data, user_query, context, request_id
)
```

## Requirements Fulfilled

This implementation fulfills the following requirements from the unified lesson flow refactor:

- **Requirement 2.3**: "WHEN quiz processing starts THEN the backend SHALL handle all quiz logic programmatically"
- **Requirement 7.1**: "WHEN the enhance_content_api function processes requests THEN it SHALL explicitly handle each phase in the unified pathway"
- **Requirement 7.2**: "WHEN phase processing occurs THEN the system SHALL log all state transitions clearly"