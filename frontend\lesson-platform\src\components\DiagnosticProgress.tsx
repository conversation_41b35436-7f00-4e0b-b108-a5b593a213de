import React from 'react';
import { Card, CardContent } from '@/components/shadcn/card';
import { Progress } from '@/components/shadcn/progress';
import { CheckCircle, Circle, BookOpen } from 'lucide-react';

interface DiagnosticProgressProps {
  currentQuestionIndex: number;
  totalQuestions: number;
  currentProbingLevel: number;
  questionsCompleted: number;
  currentPhase: string | null;
  isComplete: boolean;
  className?: string;
}

export const DiagnosticProgress: React.FC<DiagnosticProgressProps> = ({
  currentQuestionIndex,
  totalQuestions,
  currentProbingLevel,
  questionsCompleted,
  currentPhase,
  isComplete,
  className = ''
}) => {
  const progressPercentage = (questionsCompleted / totalQuestions) * 100;
  
  // Determine if we're in a diagnostic phase or should show the component
  const isDiagnosticPhase = currentPhase?.includes('diagnostic') || 
                           currentPhase?.includes('smart_diagnostic') || 
                           currentPhase?.includes('ask_q') || 
                           currentPhase?.includes('eval_q') || 
                           false;
  
  // Only show during diagnostic phases, when complete, or during final report generation
  const shouldShow = isDiagnosticPhase || 
                    isComplete || 
                    currentPhase === 'quiz_results' || 
                    currentPhase === 'final_report_inprogress';
  
  if (!shouldShow) {
    return null; // Don't show during non-relevant phases
  }

  return (
    <Card className={`${className} border-blue-200 bg-blue-50`}>
      <CardContent className="p-4">
        <div className="flex items-center gap-2 mb-3">
          <BookOpen className="h-5 w-5 text-blue-600" />
          <h3 className="font-semibold text-blue-900">
            {isComplete ? 'Diagnostic Complete!' : 'Diagnostic Assessment'}
          </h3>
        </div>
        
        {!isComplete && (
          <>
            <div className="mb-3">
              <div className="flex justify-between text-sm mb-1">
                <span className="text-blue-700">
                  Question {currentQuestionIndex + 1} of {totalQuestions}
                </span>
                <span className="text-blue-600 font-medium">
                  Level {currentProbingLevel}
                </span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
            </div>
            
            <div className="flex items-center gap-2 text-sm text-blue-700">
              <span>Progress:</span>
              <div className="flex gap-1">
                {Array.from({length: totalQuestions}, (_, i) => (
                  <div key={i} className="flex items-center">
                    {i < questionsCompleted ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : i === currentQuestionIndex ? (
                      <Circle className="h-4 w-4 text-blue-500 animate-pulse" />
                    ) : (
                      <Circle className="h-4 w-4 text-gray-300" />
                    )}
                  </div>
                ))}
              </div>
            </div>
            
            <div className="mt-2 text-xs text-blue-600">
              Phase: {currentPhase || 'Initializing...'}
            </div>
          </>
        )}
        
        {isComplete && currentPhase !== 'quiz_results' && currentPhase !== 'final_report_inprogress' && (
          <div className="text-center">
            <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <p className="text-green-700 font-medium">
              Assessment completed! Moving to lesson content...
            </p>
          </div>
        )}
        
        {currentPhase === 'quiz_results' && (
          <div className="text-center">
            <CheckCircle className="h-8 w-8 text-amber-500 mx-auto mb-2" />
            <p className="text-amber-700 font-medium">
              Quiz completed! Preparing final report...
            </p>
          </div>
        )}
        
        {currentPhase === 'final_report_inprogress' && (
          <div className="text-center">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
            <p className="text-blue-700 font-medium">
              Generating final lesson report...
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
