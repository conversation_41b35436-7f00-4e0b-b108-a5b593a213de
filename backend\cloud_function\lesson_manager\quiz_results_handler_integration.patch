diff --git a/backend/cloud_function/lesson_manager/main.py b/backend/cloud_function/lesson_manager/main.py
index 1234567..abcdef0 100644
--- a/backend/cloud_function/lesson_manager/main.py
+++ b/backend/cloud_function/lesson_manager/main.py
@@ -50,6 +50,7 @@ from teaching_rules import (
     enforce_phase_consistency,
     get_teaching_progress
 )
+from quiz_results_handler import quiz_results_handler
 
 # Initialize Flask app
 app = Flask(__name__)
@@ -7830,7 +7831,7 @@ async def handle_quiz_questions_phase(user_query, chat_history, context, session_
     except Exception as e:
         logger.error(f"[REQ {request_id}] Error in quiz questions phase: {str(e)}")
 
-async def handle_quiz_results_phase(user_query, chat_history, context, session_state_data, request_id):
+async def handle_quiz_results_phase_legacy(user_query, chat_history, context, session_state_data, request_id):
     """
     Handle quiz_results phase - pause and wait for system trigger.
     
@@ -7907,6 +7908,36 @@ async def handle_quiz_results_phase(user_query, chat_history, context, session_st
         logger.error(f"[REQ {request_id}] Error in quiz results phase: {str(e)}")
         return "I'm processing your quiz results. Please wait a moment...", {}, ""
 
+async def handle_quiz_results_phase(user_query, chat_history, context, session_state_data, request_id):
+    """
+    Handle quiz_results phase - pause and wait for system trigger.
+    
+    This phase:
+    1. Calculates final quiz scores and displays comprehensive results
+    2. Pauses execution and waits for '[System: Generate final lesson report]' trigger
+    3. Ensures system remains in quiz_results phase until trigger is received
+    4. Transitions to final_report_inprogress phase when trigger is detected
+    
+    Args:
+        user_query: User's input message
+        chat_history: List of previous chat messages
+        context: Additional context data
+        session_state_data: Current session state
+        request_id: Request identifier for logging
+        
+    Returns:
+        Tuple of (response_content, state_updates, raw_ai_state_block)
+    """
+    try:
+        logger.info(f"[{request_id}] 📊 QUIZ_RESULTS PHASE: Using unified state machine handler")
+        
+        response_content, state_updates, next_phase = quiz_results_handler.handle_quiz_results(
+            session_state_data, user_query, context, request_id)
+        
+        return response_content, state_updates, json.dumps(state_updates)
+    except Exception as e:
+        logger.error(f"[{request_id}] ❌ Error in quiz_results phase handler: {e}")
+        return "I'm processing your quiz results. Please wait a moment...", {'current_phase': 'quiz_results'}, ""
 
 class LessonPhase:
     """
@@ -9217,7 +9248,7 @@ async def enhance_content_api():
                 # Check for system trigger in quiz_results phase
                 system_trigger_detected = False
                 if current_phase == LessonPhase.QUIZ_RESULTS and user_query and '[System: Generate final lesson report]' in user_query:
-                    system_trigger_detected = True
+                    system_trigger_detected = True  # This is now handled by the quiz_results_handler
                     logger.info(f"[{request_id}] 🎯 SYSTEM TRIGGER DETECTED: Frontend requested final report generation")
 
                 # CRITICAL FIX: Ensure we're using the correct phase for AI processing